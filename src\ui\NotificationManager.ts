import * as vscode from "vscode";
import { Logger } from "../utils/Logger";

export interface NotificationAction {
  title: string;
  action: () => void | Promise<void> | Thenable<void>;
  isCloseAffordance?: boolean;
}

export interface NotificationOptions {
  modal?: boolean;
  detail?: string;
  actions?: NotificationAction[];
}

/**
 * Manages user notifications and progress indicators
 */
export class NotificationManager {
  private static instance: NotificationManager;
  private activeProgress: Map<
    string,
    vscode.Progress<{ message?: string; increment?: number }>
  > = new Map();

  private constructor() {}

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  /**
   * Show an information message
   */
  public async showInfo(
    message: string,
    options?: NotificationOptions
  ): Promise<string | undefined> {
    Logger.info(`Notification: ${message}`);

    if (options?.actions && options.actions.length > 0) {
      const actionTitles = options.actions.map((action) => action.title);
      const selection = await vscode.window.showInformationMessage(
        message,
        { modal: options.modal, detail: options.detail },
        ...actionTitles
      );

      if (selection) {
        const selectedAction = options.actions.find(
          (action) => action.title === selection
        );
        if (selectedAction) {
          try {
            await selectedAction.action();
          } catch (error) {
            Logger.error("Error executing notification action", error);
          }
        }
      }

      return selection;
    } else {
      return vscode.window.showInformationMessage(message, {
        modal: options?.modal,
        detail: options?.detail,
      });
    }
  }

  /**
   * Show a warning message
   */
  public async showWarning(
    message: string,
    options?: NotificationOptions
  ): Promise<string | undefined> {
    Logger.warn(`Warning: ${message}`);

    if (options?.actions && options.actions.length > 0) {
      const actionTitles = options.actions.map((action) => action.title);
      const selection = await vscode.window.showWarningMessage(
        message,
        { modal: options.modal, detail: options.detail },
        ...actionTitles
      );

      if (selection) {
        const selectedAction = options.actions.find(
          (action) => action.title === selection
        );
        if (selectedAction) {
          try {
            await selectedAction.action();
          } catch (error) {
            Logger.error("Error executing warning action", error);
          }
        }
      }

      return selection;
    } else {
      return vscode.window.showWarningMessage(message, {
        modal: options?.modal,
        detail: options?.detail,
      });
    }
  }

  /**
   * Show an error message
   */
  public async showError(
    message: string,
    options?: NotificationOptions
  ): Promise<string | undefined> {
    Logger.error(`Error notification: ${message}`);

    if (options?.actions && options.actions.length > 0) {
      const actionTitles = options.actions.map((action) => action.title);
      const selection = await vscode.window.showErrorMessage(
        message,
        { modal: options.modal, detail: options.detail },
        ...actionTitles
      );

      if (selection) {
        const selectedAction = options.actions.find(
          (action) => action.title === selection
        );
        if (selectedAction) {
          try {
            await selectedAction.action();
          } catch (error) {
            Logger.error("Error executing error action", error);
          }
        }
      }

      return selection;
    } else {
      return vscode.window.showErrorMessage(message, {
        modal: options?.modal,
        detail: options?.detail,
      });
    }
  }

  /**
   * Show a progress notification
   */
  public async showProgress<T>(
    title: string,
    task: (
      progress: vscode.Progress<{ message?: string; increment?: number }>,
      token: vscode.CancellationToken
    ) => Promise<T>,
    options?: { location?: vscode.ProgressLocation; cancellable?: boolean }
  ): Promise<T> {
    const progressOptions: vscode.ProgressOptions = {
      location: options?.location || vscode.ProgressLocation.Notification,
      title,
      cancellable: options?.cancellable || false,
    };

    return vscode.window.withProgress(
      progressOptions,
      async (progress, token) => {
        const progressId = `${Date.now()}-${Math.random()}`;
        this.activeProgress.set(progressId, progress);

        try {
          const result = await task(progress, token);
          return result;
        } finally {
          this.activeProgress.delete(progressId);
        }
      }
    );
  }

  /**
   * Show a quick pick selection
   */
  public async showQuickPick<T extends vscode.QuickPickItem>(
    items: T[],
    options?: vscode.QuickPickOptions
  ): Promise<T | undefined> {
    return vscode.window.showQuickPick(items, options);
  }

  /**
   * Show an input box
   */
  public async showInputBox(
    options?: vscode.InputBoxOptions
  ): Promise<string | undefined> {
    return vscode.window.showInputBox(options);
  }

  /**
   * Show Flutter-specific notifications
   */
  public async showFlutterStarting(): Promise<void> {
    await this.showInfo("Starting Flutter development server...", {
      actions: [
        {
          title: "Show Logs",
          action: async () => {
            Logger.show();
          },
        },
      ],
    });
  }

  public async showFlutterReady(url: string): Promise<void> {
    await this.showInfo(`Flutter preview is ready at ${url}`, {
      actions: [
        {
          title: "Open Preview",
          action: async () => {
            await vscode.commands.executeCommand("syncview.showPreview");
          },
        },
      ],
    });
  }

  public async showFlutterError(error: string): Promise<void> {
    await this.showError(`Flutter error: ${error}`, {
      actions: [
        {
          title: "Restart",
          action: async () => {
            await vscode.commands.executeCommand("syncview.restartPreview");
          },
        },
        {
          title: "Show Logs",
          action: async () => {
            Logger.show();
          },
        },
        {
          title: "Open Settings",
          action: async () => {
            await vscode.commands.executeCommand("syncview.openSettings");
          },
        },
      ],
    });
  }

  public async showHotReloadComplete(): Promise<void> {
    // Show a subtle notification for hot reload
    vscode.window.setStatusBarMessage("$(check) Hot reload completed", 2000);
  }

  public async showHotRestartComplete(): Promise<void> {
    // Show a subtle notification for hot restart
    vscode.window.setStatusBarMessage("$(refresh) Hot restart completed", 2000);
  }

  /**
   * Show project setup notifications
   */
  public async showProjectNotFound(): Promise<string | undefined> {
    return this.showWarning(
      "No Flutter project found in the current workspace.",
      {
        actions: [
          {
            title: "Create New Project",
            action: async () => {
              await vscode.commands.executeCommand("flutter.createProject");
            },
          },
          {
            title: "Open Folder",
            action: async () => {
              await vscode.commands.executeCommand("vscode.openFolder");
            },
          },
        ],
      }
    );
  }

  public async showWebSupportMissing(): Promise<string | undefined> {
    return this.showWarning(
      "Flutter web support is not enabled for this project.",
      {
        detail: "Web support is required for SyncView preview functionality.",
        actions: [
          {
            title: "Enable Web Support",
            action: async () => {
              await vscode.commands.executeCommand("syncview.enableWebSupport");
            },
          },
          {
            title: "Learn More",
            action: async () => {
              await vscode.env.openExternal(
                vscode.Uri.parse("https://flutter.dev/docs/get-started/web")
              );
            },
          },
        ],
      }
    );
  }

  public async showFlutterNotInstalled(): Promise<string | undefined> {
    return this.showError("Flutter SDK not found.", {
      detail: "Please install Flutter SDK and configure the path in settings.",
      actions: [
        {
          title: "Download Flutter",
          action: async () => {
            await vscode.env.openExternal(
              vscode.Uri.parse("https://flutter.dev/docs/get-started/install")
            );
          },
        },
        {
          title: "Configure Path",
          action: async () => {
            await vscode.commands.executeCommand(
              "workbench.action.openSettings",
              "syncview.flutter.sdkPath"
            );
          },
        },
      ],
    });
  }

  /**
   * Show configuration-related notifications
   */
  public async showConfigurationError(
    setting: string,
    error: string
  ): Promise<void> {
    await this.showError(`Configuration error in ${setting}: ${error}`, {
      actions: [
        {
          title: "Open Settings",
          action: async () => {
            await vscode.commands.executeCommand(
              "workbench.action.openSettings",
              `syncview.${setting}`
            );
          },
        },
        {
          title: "Reset to Default",
          action: async () => {
            const config = vscode.workspace.getConfiguration("syncview");
            await config.update(
              setting,
              undefined,
              vscode.ConfigurationTarget.Global
            );
          },
        },
      ],
    });
  }

  /**
   * Show update notifications
   */
  public async showExtensionUpdated(version: string): Promise<void> {
    await this.showInfo(`SyncView has been updated to version ${version}`, {
      actions: [
        {
          title: "View Changelog",
          action: async () => {
            await vscode.commands.executeCommand(
              "vscode.open",
              vscode.Uri.parse("https://github.com/your-repo/syncview/releases")
            );
          },
        },
      ],
    });
  }

  /**
   * Clear all active progress indicators
   */
  public clearAllProgress(): void {
    this.activeProgress.clear();
  }

  /**
   * Get the number of active progress indicators
   */
  public getActiveProgressCount(): number {
    return this.activeProgress.size;
  }
}
