"use strict";exports.id=774,exports.ids=[774],exports.modules={6694:function(e,t,n){var o,r=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},o(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=o(e),s=0;s<n.length;s++)"default"!==n[s]&&r(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.startServer=function(e,t){function n(n=["https","http","file"]){const o={};for(const e of n)"file"===e?o[e]=t.file:"http"!==e&&"https"!==e||(o[e]=t.http);return t=>{const n=t.substr(0,t.indexOf(":")),r=o[n];return r?r.getContent(t):e.sendRequest(p.type,t).then((e=>e),(e=>Promise.reject(e.message)))}}let o=(0,l.getLanguageService)({workspaceContext:S,contributions:[],clientCapabilities:l.ClientCapabilities.LATEST});const r=new c.TextDocuments(l.TextDocument);r.listen(e);let i,s=!1,d=!1,j=!1,A=Number.MAX_VALUE,E=Number.MAX_VALUE,R=Number.MAX_VALUE,P=Number.MAX_VALUE,C=Number.MAX_VALUE,_=Number.MAX_VALUE,L=Number.MAX_VALUE;e.onInitialize((u=>{const m=u.initializationOptions||{},f=m?.handledSchemaProtocols;function g(e,t){const n=e.split(".");let o=u.capabilities;for(let e=0;o&&e<n.length;e++){if(!o.hasOwnProperty(n[e]))return t;o=o[n[e]]}return o}o=(0,l.getLanguageService)({schemaRequestService:n(f),workspaceContext:S,contributions:[],clientCapabilities:u.capabilities}),s=g("textDocument.completion.completionItem.snippetSupport",!1),d=g("textDocument.rangeFormatting.dynamicRegistration",!1)&&"boolean"!=typeof m.provideFormatter,A=g("textDocument.foldingRange.rangeLimit",Number.MAX_VALUE),j=g("textDocument.documentSymbol.hierarchicalDocumentSymbolSupport",!1),L=m.customCapabilities?.rangeFormatting?.editLimit||Number.MAX_VALUE;const p=g("textDocument.diagnostic",void 0);return i=void 0===p?(0,a.registerDiagnosticsPushSupport)(r,e,t,$):(0,a.registerDiagnosticsPullSupport)(r,e,t,$),{capabilities:{textDocumentSync:c.TextDocumentSyncKind.Incremental,completionProvider:s?{resolveProvider:!1,triggerCharacters:['"',":"]}:void 0,hoverProvider:!0,documentSymbolProvider:!0,documentRangeFormattingProvider:!0===m.provideFormatter,documentFormattingProvider:!0===m.provideFormatter,colorProvider:{},foldingRangeProvider:!0,selectionRangeProvider:!0,documentLinkProvider:{},diagnosticProvider:{documentSelector:null,interFileDependencies:!1,workspaceDiagnostics:!1},codeActionProvider:{codeActionKinds:[w]}}}}));let O,M,T=null,q=!0,N=!1;function I(e){const t={validate:q,allowComments:!0,schemas:new Array};if(M)if(Array.isArray(M))Array.prototype.push.apply(t.schemas,M);else for(const e in M){const n=M[e];Array.isArray(n)&&n.forEach((n=>{t.schemas.push({uri:n,fileMatch:[e]})}))}O&&O.forEach(((e,n)=>{let o=e.url;!o&&e.schema&&(o=e.schema.id||`vscode://schemas/custom/${n}`),o&&t.schemas.push({uri:o,fileMatch:e.fileMatch,schema:e.schema,folderUri:e.folderUri})})),e&&t.schemas.push(...e),o.configure(t),i?.requestRefresh()}async function $(e){if(0===e.getText().length)return[];const t=F(e),n="jsonc"===e.languageId?{comments:"ignore",trailingCommas:"warning"}:{comments:"error",trailingCommas:"error"};return await o.doValidation(e,t,n)}e.onDidChangeConfiguration((n=>{const o=n.settings;t.configureHttpRequests?.(o?.http?.proxy,!!o.http?.proxyStrictSSL),O=o.json?.schemas,q=!!o.json?.validate?.enable,N=o.json?.keepLines?.enable||!1,I();const r=e=>Math.trunc(Math.max(e,0));if(E=r(o.json?.resultLimit||Number.MAX_VALUE),R=r(o.json?.jsonFoldingLimit||A),P=r(o.json?.jsoncFoldingLimit||A),C=r(o.json?.jsonColorDecoratorLimit||Number.MAX_VALUE),_=r(o.json?.jsoncColorDecoratorLimit||Number.MAX_VALUE),d){const t=o.json?.format?.enable;if(t){if(!T){const t=[{language:"json"},{language:"jsonc"}];T=[e.client.register(c.DocumentRangeFormattingRequest.type,{documentSelector:t}),e.client.register(c.DocumentFormattingRequest.type,{documentSelector:t})]}}else T&&(T.forEach((e=>e.then((e=>e.dispose())))),T=null)}})),e.onNotification(g.type,(e=>{M=e,I()})),e.onNotification(h.type,(e=>{let t=!1;if(Array.isArray(e))for(const n of e)o.resetSchema(n)&&(t=!0);else t=o.resetSchema(e);t&&i?.requestRefresh()})),e.onRequest(y.type,(async e=>{const t=r.get(e);return t?(I(),await $(t)):[]})),e.onRequest(D.type,(async({schemaUri:e,content:t})=>{const n="vscode://schemas/temp/"+(new Date).getTime(),o=l.TextDocument.create(n,"json",1,t);return I([{uri:e,fileMatch:[n]}]),await $(o)})),e.onRequest(v.type,(async e=>{const t=r.get(e);if(t){const e=F(t);return o.getLanguageStatus(t,e)}return{schemas:[]}})),e.onRequest(b.type,(async e=>{const t=e.uri,n=e.options,i=r.get(t);return i?o.sort(i,n):[]})),e.onDidChangeWatchedFiles((e=>{let t=!1;e.changes.forEach((e=>{o.resetSchema(e.uri)&&(t=!0)})),t&&i?.requestRefresh()}));const U=(0,m.getLanguageModelCache)(10,60,(e=>o.parseJSONDocument(e)));function F(e){return U.get(e)}function V(e,t,n){n.keepLines=N;const i=r.get(e.uri);if(i){const e=o.format(i,t??x(i),n);if(e.length>L){const t=l.TextDocument.applyEdits(i,e);return[c.TextEdit.replace(x(i),t)]}return e}return[]}r.onDidClose((e=>{U.onDocumentRemoved(e.document)})),e.onShutdown((()=>{U.dispose()})),e.onCompletion(((e,n)=>(0,u.runSafeAsync)(t,(async()=>{const t=r.get(e.textDocument.uri);if(t){const n=F(t);return o.doComplete(t,e.position,n)}return null}),null,`Error while computing completions for ${e.textDocument.uri}`,n))),e.onHover(((e,n)=>(0,u.runSafeAsync)(t,(async()=>{const t=r.get(e.textDocument.uri);if(t){const n=F(t);return o.doHover(t,e.position,n)}return null}),null,`Error while computing hover for ${e.textDocument.uri}`,n))),e.onDocumentSymbol(((e,n)=>(0,u.runSafe)(t,(()=>{const t=r.get(e.textDocument.uri);if(t){const e=F(t);return j?o.findDocumentSymbols2(t,e,{resultLimit:E}):o.findDocumentSymbols(t,e,{resultLimit:E})}return[]}),[],`Error while computing document symbols for ${e.textDocument.uri}`,n))),e.onCodeAction(((e,n)=>(0,u.runSafeAsync)(t,(async()=>{if(r.get(e.textDocument.uri)){const e=c.CodeAction.create("Sort JSON",w);return e.command={command:"json.sort",title:f.t("Sort JSON")},[e]}return[]}),[],`Error while computing code actions for ${e.textDocument.uri}`,n))),e.onDocumentRangeFormatting(((e,n)=>(0,u.runSafe)(t,(()=>V(e.textDocument,e.range,e.options)),[],`Error while formatting range for ${e.textDocument.uri}`,n))),e.onDocumentFormatting(((e,n)=>(0,u.runSafe)(t,(()=>V(e.textDocument,void 0,e.options)),[],`Error while formatting ${e.textDocument.uri}`,n))),e.onDocumentColor(((e,n)=>(0,u.runSafeAsync)(t,(async()=>{const t=r.get(e.textDocument.uri);if(t){const e=F(t),n="jsonc"===t.languageId?_:C;return o.findDocumentColors(t,e,{resultLimit:n})}return[]}),[],`Error while computing document colors for ${e.textDocument.uri}`,n))),e.onColorPresentation(((e,n)=>(0,u.runSafe)(t,(()=>{const t=r.get(e.textDocument.uri);if(t){const n=F(t);return o.getColorPresentations(t,n,e.color,e.range)}return[]}),[],`Error while computing color presentations for ${e.textDocument.uri}`,n))),e.onFoldingRanges(((e,n)=>(0,u.runSafe)(t,(()=>{const t=r.get(e.textDocument.uri);if(t){const e="jsonc"===t.languageId?P:R;return o.getFoldingRanges(t,{rangeLimit:e})}return null}),null,`Error while computing folding ranges for ${e.textDocument.uri}`,n))),e.onSelectionRanges(((e,n)=>(0,u.runSafe)(t,(()=>{const t=r.get(e.textDocument.uri);if(t){const n=F(t);return o.getSelectionRanges(t,e.positions,n)}return[]}),[],`Error while computing selection ranges for ${e.textDocument.uri}`,n))),e.onDocumentLinks(((e,n)=>(0,u.runSafeAsync)(t,(async()=>{const t=r.get(e.textDocument.uri);if(t){const e=F(t);return o.findLinks(t,e)}return[]}),[],`Error while computing links for ${e.textDocument.uri}`,n))),e.listen()};const c=n(2861),u=n(211),a=n(9178),l=n(7547),m=n(5908),d=n(7608),f=s(n(5747));var g,p,h,y,v,D,b;!function(e){e.type=new c.NotificationType("json/schemaAssociations")}(g||(g={})),function(e){e.type=new c.RequestType("vscode/content")}(p||(p={})),function(e){e.type=new c.NotificationType("json/schemaContent")}(h||(h={})),function(e){e.type=new c.RequestType("json/validate")}(y||(y={})),function(e){e.type=new c.RequestType("json/languageStatus")}(v||(v={})),function(e){e.type=new c.RequestType("json/validateContent")}(D||(D={})),function(e){e.type=new c.RequestType("json/sort")}(b||(b={}));const S={resolveRelativePath:(e,t)=>{const n=t.substring(0,t.lastIndexOf("/")+1);return d.Utils.resolvePath(d.URI.parse(n),e).toString()}},w=c.CodeActionKind.Source.concat(".sort",".json");function x(e){return l.Range.create(l.Position.create(0,0),e.positionAt(e.getText().length))}},5908:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLanguageModelCache=function(e,t,n){let o,r={},i=0;return t>0&&(o=setInterval((()=>{const e=Date.now()-1e3*t,n=Object.keys(r);for(const t of n)r[t].cTime<e&&(delete r[t],i--)}),1e3*t)),{get(t){const o=t.version,s=t.languageId,c=r[t.uri];if(c&&c.version===o&&c.languageId===s)return c.cTime=Date.now(),c.languageModel;const u=n(t);if(r[t.uri]={languageModel:u,version:o,languageId:s,cTime:Date.now()},c||i++,i===e){let e=Number.MAX_VALUE,t=null;for(const n in r){const o=r[n];o.cTime<e&&(t=n,e=o.cTime)}t&&(delete r[t],i--)}return u},onDocumentRemoved(e){const t=e.uri;r[t]&&(delete r[t],i--)},dispose(){void 0!==o&&(clearInterval(o),o=void 0,r={},i=0)}}}},6774:function(e,t,n){var o,r=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},o(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=o(e),s=0;s<n.length;s++)"default"!==n[s]&&r(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0});const c=n(1327),u=n(211),a=n(6694),l=n(9323),m=n(7608),d=n(9896),f=s(n(5747)),g=(0,c.createConnection)();console.log=g.console.log.bind(g.console),console.error=g.console.error.bind(g.console),process.on("unhandledRejection",(e=>{g.console.error((0,u.formatError)("Unhandled exception",e))}));const p={timer:{setImmediate(e,...t){const n=setImmediate(e,...t);return{dispose:()=>clearImmediate(n)}},setTimeout(e,t,...n){const o=setTimeout(e,t,...n);return{dispose:()=>clearTimeout(o)}}},file:{async getContent(e,t){try{const n=m.URI.parse(e);return(await d.promises.readFile(n.fsPath,t)).toString()}catch(t){if("ENOENT"===t.code)throw new Error(f.t("Schema not found: {0}",e));if("EISDIR"===t.code)throw new Error(f.t("{0} is a directory, not a file",e));throw t}}},http:{getContent:(e,t)=>(0,l.xhr)({url:e,followRedirects:5,headers:{"Accept-Encoding":"gzip, deflate"}}).then((e=>e.responseText),(e=>Promise.reject(e.responseText||(0,l.getErrorStatusDescription)(e.status)||e.toString())))},configureHttpRequests:l.configure};(0,a.startServer)(g,p)},211:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.formatError=r,t.runSafeAsync=function(e,t,n,o,s){return new Promise((c=>{e.timer.setImmediate((()=>{if(!s.isCancellationRequested)return t().then((e=>{s.isCancellationRequested?c(i()):c(e)}),(e=>{console.error(r(o,e)),c(n)}));c(i())}))}))},t.runSafe=function(e,t,n,o,s){return new Promise((c=>{e.timer.setImmediate((()=>{if(s.isCancellationRequested)c(i());else try{const e=t();if(s.isCancellationRequested)return void c(i());c(e)}catch(e){console.error(r(o,e)),c(n)}}))}))};const o=n(2861);function r(e,t){if(t instanceof Error){const n=t;return`${e}: ${n.message}\n${n.stack}`}return"string"==typeof t?`${e}: ${t}`:t?`${e}: ${t.toString()}`:e}function i(){return console.log("cancelled"),new o.ResponseError(o.LSPErrorCodes.RequestCancelled,"Request cancelled")}},9178:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.registerDiagnosticsPushSupport=function(e,t,n,o){const i={},s=[];function c(e){const t=i[e.uri];t&&(t.dispose(),delete i[e.uri])}function u(e){c(e);const s=i[e.uri]=n.timer.setTimeout((async()=>{if(s===i[e.uri])try{const n=await o(e);s===i[e.uri]&&t.sendDiagnostics({uri:e.uri,diagnostics:n}),delete i[e.uri]}catch(n){t.console.error((0,r.formatError)(`Error while validating ${e.uri}`,n))}}),500)}return e.onDidChangeContent((e=>{u(e.document)}),void 0,s),e.onDidClose((e=>{c(e.document),t.sendDiagnostics({uri:e.document.uri,diagnostics:[]})}),void 0,s),{requestRefresh:()=>{e.all().forEach(u)},dispose:()=>{s.forEach((e=>e.dispose())),s.length=0;const e=Object.keys(i);for(const t of e)i[t].dispose(),delete i[t]}}},t.registerDiagnosticsPullSupport=function(e,t,n,i){function s(e){return{kind:o.DocumentDiagnosticReportKind.Full,items:e}}const c=t.languages.diagnostics.on((async(t,o)=>(0,r.runSafeAsync)(n,(async()=>{const n=e.get(t.textDocument.uri);return s(n?await i(n):[])}),s([]),`Error while computing diagnostics for ${t.textDocument.uri}`,o)));return{requestRefresh:function(){t.languages.diagnostics.refresh()},dispose:()=>{c.dispose()}}};const o=n(2861),r=n(211)}};
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/848b80aeb52026648a8ff9f7c45a9b0a80641e2e/extensions/json-language-features/server/dist/node/774.jsonServerMain.js.map