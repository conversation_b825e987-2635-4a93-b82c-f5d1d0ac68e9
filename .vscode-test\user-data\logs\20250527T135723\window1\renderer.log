2025-05-27 13:57:27.607 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-05-27 13:57:27.834 [info] Started local extension host with pid 10748.
2025-05-27 13:57:27.934 [info] Started initializing default profile extensions in extensions installation folder. file:///d%3A/DevGen/devgen-syncview/.vscode-test/extensions
2025-05-27 13:57:28.019 [info] ComputeTargetPlatform: win32-x64
2025-05-27 13:57:28.300 [info] Completed initializing default profile extensions in extensions installation folder. file:///d%3A/DevGen/devgen-syncview/.vscode-test/extensions
2025-05-27 13:57:28.546 [info] Loading development extension at d:\DevGen\devgen-syncview
2025-05-27 13:57:28.663 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-05-27 13:57:28.955 [error] Failed to fetch: Failed: Failed to fetch
    at lJe.L (vscode-file://vscode-app/d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/workbench.desktop.main.js:1859:34810)
    at async lJe.I (vscode-file://vscode-app/d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/workbench.desktop.main.js:1859:29229)
    at async lJe.B (vscode-file://vscode-app/d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/workbench.desktop.main.js:1859:23300)
    at async lJe.getExtensions (vscode-file://vscode-app/d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/workbench.desktop.main.js:1859:22171)
    at async ect.getExtensions (vscode-file://vscode-app/d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/workbench.desktop.main.js:2509:14652)
    at async ect.vb (vscode-file://vscode-app/d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/workbench.desktop.main.js:2509:12521)
2025-05-27 13:57:33.968 [info] [0m%s%s[0m  
2025-05-27 13:57:33.971 [info] [0m%s%s[0m    SyncView Extension Test Suite
2025-05-27 13:57:34.016 [info] [0m%s%s[0m      Logger Tests
2025-05-27 13:57:34.039 [info]     [32m  ✔[0m[90m %s[0m Logger should log messages
2025-05-27 13:57:34.040 [info] [0m%s%s[0m      FileUtils Tests
2025-05-27 13:57:34.041 [info]     [32m  ✔[0m[90m %s[0m Should normalize paths correctly
2025-05-27 13:57:34.042 [info]     [32m  ✔[0m[90m %s[0m Should get file extension correctly
2025-05-27 13:57:34.044 [info]     [32m  ✔[0m[90m %s[0m Should get file name without extension
2025-05-27 13:57:34.045 [info]     [32m  ✔[0m[90m %s[0m Should check if path is absolute
2025-05-27 13:57:34.045 [info] [0m%s%s[0m      ProcessUtils Tests
2025-05-27 13:57:34.145 [info]     [32m  ✔[0m[90m %s[0m[31m (%dms)[0m Should check command availability 99
2025-05-27 13:57:34.244 [info]     [32m  ✔[0m[90m %s[0m[31m (%dms)[0m Should handle non-existent commands 98
2025-05-27 13:57:34.245 [info] [0m%s%s[0m      FlutterProjectDetector Tests
2025-05-27 13:57:34.245 [info]     [32m  ✔[0m[90m %s[0m Should create detector instance
2025-05-27 13:57:34.247 [info]     [32m  ✔[0m[90m %s[0m Should validate Flutter project structure
2025-05-27 13:57:34.247 [info] [0m%s%s[0m      Extension Commands
2025-05-27 13:57:34.247 [info]     [32m  ✔[0m[90m %s[0m Extension should be present
2025-05-27 13:57:34.252 [info]     [32m  ✔[0m[90m %s[0m Commands should be registered
2025-05-27 13:57:34.252 [info] [0m%s%s[0m    WorkspaceManager Tests
2025-05-27 13:57:34.253 [info]   [32m  ✔[0m[90m %s[0m Should create WorkspaceManager instance
2025-05-27 13:57:34.256 [info]   [32m  ✔[0m[90m %s[0m Should get workspace info
2025-05-27 13:57:34.258 [info]   [32m  ✔[0m[90m %s[0m Should check Flutter project existence
2025-05-27 13:57:34.260 [info]   [32m  ✔[0m[90m %s[0m Should check web support
2025-05-27 13:57:34.261 [info]   [32m  ✔[0m[90m %s[0m Should get all Flutter projects
2025-05-27 13:57:34.261 [info]   [32m  ✔[0m[90m %s[0m Should handle workspace relative paths
2025-05-27 13:57:34.262 [info]   [32m  ✔[0m[90m %s[0m Should resolve workspace paths
2025-05-27 13:57:34.262 [info]   [32m  ✔[0m[90m %s[0m Should start and stop file watching
2025-05-27 13:57:34.263 [info]   [32m  ✔[0m[90m %s[0m Should emit events correctly
2025-05-27 13:57:34.266 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:57:34.266 [info] [0m%s%s[0m    ProxyServerManager Tests
2025-05-27 13:57:34.267 [info]   [32m  ✔[0m[90m %s[0m Should create ProxyServerManager instance
2025-05-27 13:57:34.268 [info]   [32m  ✔[0m[90m %s[0m Should handle server state correctly
2025-05-27 13:57:34.274 [info]   [32m  ✔[0m[90m %s[0m Should set target URL
2025-05-27 13:57:34.275 [info]   [32m  ✔[0m[90m %s[0m Should emit events correctly
2025-05-27 13:57:34.289 [info]   [31m  %d) %s[0m 1 Should get stats
2025-05-27 13:57:34.294 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:57:34.294 [info] [0m%s%s[0m    FlutterManager Tests
2025-05-27 13:57:34.295 [info]   [32m  ✔[0m[90m %s[0m Should create FlutterManager instance
2025-05-27 13:57:34.296 [info]   [32m  ✔[0m[90m %s[0m Should handle process state correctly
2025-05-27 13:57:34.296 [info]   [32m  ✔[0m[90m %s[0m Should emit events correctly
2025-05-27 13:57:34.297 [info]   [32m  ✔[0m[90m %s[0m Should handle hot reload when not running
2025-05-27 13:57:34.297 [info]   [32m  ✔[0m[90m %s[0m Should handle hot restart when not running
2025-05-27 13:57:34.298 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:57:34.299 [info] [0m%s%s[0m    ConfigurationManager Tests
2025-05-27 13:57:34.302 [info]   [32m  ✔[0m[90m %s[0m Should create ConfigurationManager instance
2025-05-27 13:57:34.303 [info]   [32m  ✔[0m[90m %s[0m Should get configuration values
2025-05-27 13:57:34.304 [info]   [32m  ✔[0m[90m %s[0m Should validate default configuration
2025-05-27 13:57:34.305 [info]   [32m  ✔[0m[90m %s[0m Should handle hot reload settings
2025-05-27 13:57:34.305 [info]   [32m  ✔[0m[90m %s[0m Should handle debug mode settings
2025-05-27 13:57:34.306 [info]   [32m  ✔[0m[90m %s[0m Should handle status bar settings
2025-05-27 13:57:34.307 [info]   [32m  ✔[0m[90m %s[0m Should handle auto show preview settings
2025-05-27 13:57:34.307 [info]   [32m  ✔[0m[90m %s[0m Should get preview position
2025-05-27 13:57:34.309 [info]   [32m  ✔[0m[90m %s[0m Should get device frame setting
2025-05-27 13:57:34.310 [info]   [32m  ✔[0m[90m %s[0m Should handle CORS settings
2025-05-27 13:57:34.310 [info]   [32m  ✔[0m[90m %s[0m Should handle performance settings
2025-05-27 13:57:34.311 [info]   [32m  ✔[0m[90m %s[0m Should get additional Flutter args
2025-05-27 13:57:34.311 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:57:34.312 [info] [0m%s%s[0m    ExtensionManager Integration Tests
2025-05-27 13:57:34.313 [info]   [32m  ✔[0m[90m %s[0m Should create ExtensionManager instance
2025-05-27 13:57:34.317 [info]   [32m  ✔[0m[90m %s[0m Should activate without errors
2025-05-27 13:57:34.338 [info]   [31m  %d) %s[0m 2 Should dispose cleanly
2025-05-27 13:57:34.339 [info]   [31m  %d) %s[0m 3 Should handle multiple activations gracefully
2025-05-27 13:57:34.340 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 47 358ms
2025-05-27 13:57:34.340 [info] [31m  %d failing[0m 3
2025-05-27 13:57:34.341 [info] [0m  %s) %s:
[0m[31m     %s[0m[90m
%s
[0m 1 ProxyServerManager Tests
       Should get stats AssertionError [ERR_ASSERTION]: The expression evaluated to a falsy value:

  assert.ok(stats)
   	at Context.<anonymous> (d:\DevGen\devgen-syncview\out\test\unit\ProxyServerManager.test.js:91:16)
  	at process.processImmediate (node:internal/timers:483:21)
2025-05-27 13:57:34.342 [info] [0m  %s) %s:
[0m[31m     %s[0m[90m
%s
[0m 2 ExtensionManager Integration Tests
       Should dispose cleanly Error: View provider for 'syncview.flutterPreview' already registered   	at pG.registerWebviewViewProvider (file:///d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:170:24055)
  	at Object.registerWebviewViewProvider (file:///d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:170:42961)
  	at ExtensionManager.activate (d:\DevGen\devgen-syncview\out\core\ExtensionManager.js:90:55)
  	at Context.<anonymous> (d:\DevGen\devgen-syncview\out\test\integration\ExtensionManager.test.js:92:26)
  	at process.processImmediate (node:internal/timers:483:21)
2025-05-27 13:57:34.343 [info] [0m  %s) %s:
[0m[31m     %s[0m[90m
%s
[0m 3 ExtensionManager Integration Tests
       Should handle multiple activations gracefully AssertionError [ERR_ASSERTION]: Got unwanted exception.
Actual message: "View provider for 'syncview.flutterPreview' already registered"   	at Context.<anonymous> (d:\DevGen\devgen-syncview\out\test\integration\ExtensionManager.test.js:98:16)
  	at process.processImmediate (node:internal/timers:483:21)
2025-05-27 13:57:34.343 [error] 3 tests failed.
