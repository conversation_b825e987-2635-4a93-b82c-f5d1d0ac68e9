import { EventEmitter } from 'events';
import { ChildProcess } from 'child_process';
import { Logger } from '../utils/Logger';

/**
 * Handles Flutter CLI process output and events
 */
export class FlutterProcessHandler extends EventEmitter {
    private process: ChildProcess | undefined;
    private outputBuffer: string = '';

    /**
     * Attach to a Flutter process
     */
    public attachToProcess(process: ChildProcess): void {
        this.process = process;
        this.setupProcessListeners();
    }

    /**
     * Setup listeners for the Flutter process
     */
    private setupProcessListeners(): void {
        if (!this.process) {
            return;
        }

        // Handle stdout
        this.process.stdout?.on('data', (data: Buffer) => {
            const output = data.toString();
            this.outputBuffer += output;
            this.processOutput(output);
        });

        // Handle stderr
        this.process.stderr?.on('data', (data: Buffer) => {
            const error = data.toString();
            Logger.error('Flutter stderr:', error);
            this.processError(error);
        });

        // <PERSON>le process exit
        this.process.on('exit', (code: number | null, signal: string | null) => {
            Logger.info(`Flutter process exited with code: ${code}, signal: ${signal}`);
            this.emit('exit', code || 0);
        });

        // Handle process error
        this.process.on('error', (error: Error) => {
            Logger.error('Flutter process error:', error);
            this.emit('error', error);
        });
    }

    /**
     * Process Flutter CLI output
     */
    private processOutput(output: string): void {
        const lines = output.split('\n');
        
        for (const line of lines) {
            this.processLine(line.trim());
        }
    }

    /**
     * Process individual output line
     */
    private processLine(line: string): void {
        if (!line) {
            return;
        }

        Logger.debug(`Flutter output: ${line}`);

        // Check for server URL
        const urlMatch = line.match(/A web server for this build is available at:\s*(https?:\/\/[^\s]+)/);
        if (urlMatch) {
            const serverUrl = urlMatch[1];
            Logger.info(`Flutter web server detected: ${serverUrl}`);
            this.emit('serverStarted', serverUrl);
            return;
        }

        // Alternative URL pattern
        const altUrlMatch = line.match(/(https?:\/\/localhost:\d+)/);
        if (altUrlMatch && line.includes('web server')) {
            const serverUrl = altUrlMatch[1];
            Logger.info(`Flutter web server detected (alt): ${serverUrl}`);
            this.emit('serverStarted', serverUrl);
            return;
        }

        // Check for hot reload completion
        if (line.includes('Reloaded') && line.includes('ms')) {
            this.emit('hotReloadComplete');
            return;
        }

        // Check for hot restart completion
        if (line.includes('Restarted application')) {
            this.emit('hotRestartComplete');
            return;
        }

        // Check for compilation errors
        if (line.includes('Error:') || line.includes('error:')) {
            this.emit('compilationError', line);
            return;
        }

        // Check for warnings
        if (line.includes('Warning:') || line.includes('warning:')) {
            this.emit('warning', line);
            return;
        }

        // Check for build completion
        if (line.includes('Built') && line.includes('web')) {
            this.emit('buildComplete');
            return;
        }

        // Check for ready state
        if (line.includes('Flutter run key commands')) {
            this.emit('ready');
            return;
        }

        // Check for dependency resolution
        if (line.includes('Running "flutter pub get"')) {
            this.emit('pubGetStarted');
            return;
        }

        if (line.includes('Resolving dependencies...')) {
            this.emit('resolvingDependencies');
            return;
        }

        // Check for analysis issues
        if (line.includes('issues found')) {
            this.emit('analysisComplete', line);
            return;
        }
    }

    /**
     * Process Flutter CLI errors
     */
    private processError(error: string): void {
        const lines = error.split('\n');
        
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) {
                continue;
            }

            // Check for specific error patterns
            if (trimmedLine.includes('No connected devices')) {
                this.emit('error', new Error('No connected devices found'));
                return;
            }

            if (trimmedLine.includes('Unable to locate a development device')) {
                this.emit('error', new Error('Unable to locate a development device'));
                return;
            }

            if (trimmedLine.includes('Flutter SDK not found')) {
                this.emit('error', new Error('Flutter SDK not found'));
                return;
            }

            if (trimmedLine.includes('pubspec.yaml')) {
                this.emit('error', new Error('pubspec.yaml error: ' + trimmedLine));
                return;
            }

            // Generic error
            this.emit('error', new Error(trimmedLine));
        }
    }

    /**
     * Send input to the Flutter process
     */
    public sendInput(input: string): boolean {
        if (!this.process || !this.process.stdin || this.process.stdin.destroyed) {
            Logger.warn('Cannot send input: Flutter process stdin not available');
            return false;
        }

        try {
            this.process.stdin.write(input + '\n');
            Logger.debug(`Sent input to Flutter process: ${input}`);
            return true;
        } catch (error) {
            Logger.error('Error sending input to Flutter process', error);
            return false;
        }
    }

    /**
     * Get the current output buffer
     */
    public getOutputBuffer(): string {
        return this.outputBuffer;
    }

    /**
     * Clear the output buffer
     */
    public clearOutputBuffer(): void {
        this.outputBuffer = '';
    }

    /**
     * Check if process is running
     */
    public isProcessRunning(): boolean {
        return this.process !== undefined && !this.process.killed;
    }

    /**
     * Dispose the handler
     */
    public dispose(): void {
        this.removeAllListeners();
        this.process = undefined;
        this.outputBuffer = '';
    }
}
