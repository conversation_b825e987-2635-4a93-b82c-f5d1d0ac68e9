{"version": 3, "file": "ErrorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,qCAAkC;AAalC;;GAEG;AACH,MAAa,YAAY;IACf,MAAM,CAAU,WAAW,GAAG;QACpC,iBAAiB,EAAE,mBAAmB;QACtC,iBAAiB,EAAE,mBAAmB;QACtC,cAAc,EAAE,gBAAgB;QAChC,WAAW,EAAE,aAAa;QAC1B,aAAa,EAAE,eAAe;QAC9B,mBAAmB,EAAE,qBAAqB;KAC3C,CAAC;IAEF;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAC7B,KAAqB,EACrB,OAAqB,EACrB,OAAuB;QAEvB,MAAM,YAAY,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QACvE,MAAM,WAAW,GAAG,GAAG,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,SAAS,KAAK,YAAY,EAAE,CAAC;QAEnF,gBAAgB;QAChB,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAEzE,2DAA2D;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAE1E,kCAAkC;QAClC,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACpD,WAAW,EACX,GAAG,YAAY,CAChB,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CACjC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CACvC,CAAC;gBACF,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC;oBAChC,CAAC;oBAAC,OAAO,WAAW,EAAE,CAAC;wBACrB,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,WAAW,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CACpC,KAAqB,EACrB,SAAiB;QAEjB,MAAM,OAAO,GAAiB;YAC5B,SAAS,EAAE,SAAS;YACpB,SAAS;YACT,OAAO,EAAE,EAAE,KAAK,EAAE;SACnB,CAAC;QAEF,MAAM,OAAO,GAAkB,EAAE,CAAC;QAElC,2CAA2C;QAC3C,MAAM,YAAY,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC;QAEvE,IAAI,YAAY,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;YACnD,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAClC,+BAA+B,EAC/B,0BAA0B,CAC3B,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,qBAAqB;gBAC5B,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAC3B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,8CAA8C,CAAC,CACjE,CAAC;gBACJ,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC;gBACX,KAAK,EAAE,mBAAmB;gBAC1B,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;oBAC3D,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpD,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CACpC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,EACvB,cAAc,CACf,CAAC;wBACF,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;oBACnD,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,IAAI,CAAC;YACX,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,KAAK,IAAI,EAAE;gBACjB,eAAM,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAClC,KAAqB,EACrB,SAAiB;QAEjB,MAAM,OAAO,GAAiB;YAC5B,SAAS,EAAE,cAAc;YACzB,SAAS;YACT,OAAO,EAAE,EAAE,KAAK,EAAE;SACnB,CAAC;QAEF,MAAM,OAAO,GAAkB;YAC7B;gBACE,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;gBAClE,CAAC;aACF;YACD;gBACE,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,IAAI,EAAE,CAAC;gBAChB,CAAC;aACF;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,kBAAkB,CACpC,KAAqB,EACrB,SAAiB;QAEjB,MAAM,OAAO,GAAiB;YAC5B,SAAS,EAAE,SAAS;YACpB,SAAS;YACT,OAAO,EAAE,EAAE,KAAK,EAAE;SACnB,CAAC;QAEF,MAAM,OAAO,GAAkB;YAC7B;gBACE,KAAK,EAAE,iBAAiB;gBACxB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;gBAClE,CAAC;aACF;YACD;gBACE,KAAK,EAAE,iBAAiB;gBACxB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;gBAClE,CAAC;aACF;YACD;gBACE,KAAK,EAAE,WAAW;gBAClB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,eAAM,CAAC,IAAI,EAAE,CAAC;gBAChB,CAAC;aACF;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAC1C,KAAqB,EACrB,OAAgB;QAEhB,MAAM,OAAO,GAAiB;YAC5B,SAAS,EAAE,eAAe;YAC1B,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;YACtD,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;SAC5B,CAAC;QAEF,MAAM,OAAO,GAAkB;YAC7B;gBACE,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAClC,+BAA+B,EAC/B,UAAU,CACX,CAAC;gBACJ,CAAC;aACF;YACD;gBACE,KAAK,EAAE,mBAAmB;gBAC1B,MAAM,EAAE,KAAK,IAAI,EAAE;oBACjB,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACpD,0CAA0C,EAC1C,KAAK,EACL,IAAI,CACL,CAAC;oBACF,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;wBACtB,0CAA0C;wBAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;aACF;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,yBAAyB,CACtC,YAAoB,EACpB,OAAqB;QAErB,4BAA4B;QAC5B,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,YAAY,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACnD,OAAO,2EAA2E,CAAC;YACrF,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,CAAC,sBAAsB,CAAC,EAAE,CAAC;gBAClD,OAAO,yEAAyE,CAAC;YACnF,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC1C,OAAO,2EAA2E,CAAC;YACrF,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE,CAAC;gBACtD,OAAO,mFAAmF,CAAC;YAC7F,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,IAAI,OAAO,CAAC,SAAS,KAAK,cAAc,EAAE,CAAC;YACzC,IAAI,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACxC,OAAO,mGAAmG,CAAC;YAC7G,CAAC;YACD,IAAI,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC1C,OAAO,6EAA6E,CAAC;YACvF,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACpC,IAAI,YAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC5C,OAAO,8EAA8E,CAAC;YACxF,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,OAAO,GAAG,OAAO,CAAC,SAAS,iBAAiB,OAAO,CAAC,SAAS,KAAK,YAAY,EAAE,CAAC;IACnF,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,WAAW,CAC7B,OAAe,EACf,OAAuB;QAEvB,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAErB,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACtD,OAAO,EACP,GAAG,YAAY,CAChB,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CACjC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CACvC,CAAC;gBACF,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC;oBAChC,CAAC;oBAAC,OAAO,WAAW,EAAE,CAAC;wBACrB,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAC1B,OAAe,EACf,OAAuB;QAEvB,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAErB,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,OAAO,EACP,GAAG,YAAY,CAChB,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,cAAc,GAAG,OAAO,CAAC,IAAI,CACjC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CACvC,CAAC;gBACF,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC;oBAChC,CAAC;oBAAC,OAAO,WAAW,EAAE,CAAC;wBACrB,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,WAAW,CAAC,CAAC;oBAC3D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAa;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;;AAxVH,oCAyVC"}