import * as assert from "assert";
import * as vscode from "vscode";
import { ExtensionManager } from "../../core/ExtensionManager";
import { Logger } from "../../utils/Logger";

suite("ExtensionManager Integration Tests", () => {
  let extensionManager: ExtensionManager;
  let mockContext: vscode.ExtensionContext;

  suiteSetup(async () => {
    // Create mock context
    mockContext = {
      subscriptions: [],
      workspaceState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        keys: () => [],
      },
      globalState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        setKeysForSync: () => {},
        keys: () => [],
      },
      extensionPath: __dirname,
      extensionUri: vscode.Uri.file(__dirname),
      environmentVariableCollection: {} as any,
      extensionMode: vscode.ExtensionMode.Test,
      storageUri: vscode.Uri.file(__dirname),
      globalStorageUri: vscode.Uri.file(__dirname),
      logUri: vscode.Uri.file(__dirname),
      secrets: {} as any,
      asAbsolutePath: (relativePath: string) => relativePath,
      storagePath: __dirname,
      globalStoragePath: __dirname,
      logPath: __dirname,
      extension: {} as any,
      languageModelAccessInformation: {} as any,
    } as vscode.ExtensionContext;

    Logger.initialize(mockContext);
  });

  setup(() => {
    extensionManager = new ExtensionManager(mockContext);
  });

  teardown(() => {
    if (extensionManager) {
      extensionManager.dispose();
    }
  });

  test("Should create ExtensionManager instance", () => {
    assert.ok(extensionManager);
  });

  test("Should activate without errors", () => {
    assert.doesNotThrow(() => {
      extensionManager.activate();
    });
  });

  test("Should dispose cleanly", () => {
    extensionManager.activate();
    assert.doesNotThrow(() => {
      extensionManager.dispose();
    });
  });

  test("Should handle multiple activations gracefully", () => {
    assert.doesNotThrow(() => {
      extensionManager.activate();
      extensionManager.activate(); // Should not cause issues
    });
  });
});
