import * as vscode from 'vscode';
import { WebViewManager } from '../webview/WebViewManager';
import { FlutterManager } from '../flutter/FlutterManager';
import { StatusBarManager } from '../ui/StatusBarManager';
import { Logger } from '../utils/Logger';

/**
 * Manages all VS Code commands for the SyncView extension
 */
export class CommandManager implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];

    constructor(
        private readonly webViewManager: WebViewManager,
        private readonly flutterManager: FlutterManager,
        private readonly statusBarManager: StatusBarManager
    ) {}

    /**
     * Register all extension commands
     */
    public registerCommands(context: vscode.ExtensionContext): void {
        const commands = [
            // Main preview commands
            vscode.commands.registerCommand('syncview.startPreview', () => this.startPreview()),
            vscode.commands.registerCommand('syncview.stopPreview', () => this.stopPreview()),
            vscode.commands.registerCommand('syncview.restartPreview', () => this.restartPreview()),
            vscode.commands.registerCommand('syncview.togglePreview', () => this.togglePreview()),

            // WebView commands
            vscode.commands.registerCommand('syncview.showPreview', () => this.showPreview()),
            vscode.commands.registerCommand('syncview.hidePreview', () => this.hidePreview()),
            vscode.commands.registerCommand('syncview.refreshPreview', () => this.refreshPreview()),

            // Flutter commands
            vscode.commands.registerCommand('syncview.hotReload', () => this.hotReload()),
            vscode.commands.registerCommand('syncview.hotRestart', () => this.hotRestart()),

            // Utility commands
            vscode.commands.registerCommand('syncview.openSettings', () => this.openSettings()),
            vscode.commands.registerCommand('syncview.showLogs', () => this.showLogs()),
            vscode.commands.registerCommand('syncview.clearLogs', () => this.clearLogs()),

            // Legacy command (for backward compatibility)
            vscode.commands.registerCommand('devgen-syncview.helloWorld', () => this.showWelcome())
        ];

        this.disposables.push(...commands);
        context.subscriptions.push(...commands);

        Logger.info('SyncView commands registered successfully');
    }

    /**
     * Start the Flutter preview
     */
    private async startPreview(): Promise<void> {
        try {
            Logger.info('Starting Flutter preview...');
            this.statusBarManager.setStatus('starting');
            
            await this.flutterManager.startFlutterProcess();
            await this.webViewManager.createPreviewPanel();
            
            Logger.info('Flutter preview started successfully');
        } catch (error) {
            Logger.error('Failed to start Flutter preview', error);
            this.statusBarManager.setStatus('error');
            vscode.window.showErrorMessage('Failed to start Flutter preview. Check the output panel for details.');
        }
    }

    /**
     * Stop the Flutter preview
     */
    private async stopPreview(): Promise<void> {
        try {
            Logger.info('Stopping Flutter preview...');
            this.statusBarManager.setStatus('stopping');
            
            await this.flutterManager.stopFlutterProcess();
            this.webViewManager.disposePreviewPanel();
            
            this.statusBarManager.setStatus('stopped');
            Logger.info('Flutter preview stopped successfully');
        } catch (error) {
            Logger.error('Failed to stop Flutter preview', error);
            vscode.window.showErrorMessage('Failed to stop Flutter preview. Check the output panel for details.');
        }
    }

    /**
     * Restart the Flutter preview
     */
    private async restartPreview(): Promise<void> {
        try {
            Logger.info('Restarting Flutter preview...');
            await this.stopPreview();
            await this.startPreview();
        } catch (error) {
            Logger.error('Failed to restart Flutter preview', error);
            vscode.window.showErrorMessage('Failed to restart Flutter preview. Check the output panel for details.');
        }
    }

    /**
     * Toggle the Flutter preview on/off
     */
    private async togglePreview(): Promise<void> {
        const isRunning = this.flutterManager.isRunning();
        if (isRunning) {
            await this.stopPreview();
        } else {
            await this.startPreview();
        }
    }

    /**
     * Show the preview panel
     */
    private showPreview(): void {
        this.webViewManager.showPreviewPanel();
    }

    /**
     * Hide the preview panel
     */
    private hidePreview(): void {
        this.webViewManager.hidePreviewPanel();
    }

    /**
     * Refresh the preview
     */
    private async refreshPreview(): Promise<void> {
        try {
            Logger.info('Refreshing Flutter preview...');
            await this.webViewManager.refreshPreview();
            Logger.info('Flutter preview refreshed successfully');
        } catch (error) {
            Logger.error('Failed to refresh Flutter preview', error);
            vscode.window.showErrorMessage('Failed to refresh Flutter preview.');
        }
    }

    /**
     * Trigger hot reload
     */
    private async hotReload(): Promise<void> {
        try {
            Logger.info('Triggering hot reload...');
            await this.flutterManager.hotReload();
            Logger.info('Hot reload completed successfully');
        } catch (error) {
            Logger.error('Failed to trigger hot reload', error);
            vscode.window.showErrorMessage('Failed to trigger hot reload.');
        }
    }

    /**
     * Trigger hot restart
     */
    private async hotRestart(): Promise<void> {
        try {
            Logger.info('Triggering hot restart...');
            await this.flutterManager.hotRestart();
            Logger.info('Hot restart completed successfully');
        } catch (error) {
            Logger.error('Failed to trigger hot restart', error);
            vscode.window.showErrorMessage('Failed to trigger hot restart.');
        }
    }

    /**
     * Open extension settings
     */
    private openSettings(): void {
        vscode.commands.executeCommand('workbench.action.openSettings', 'syncview');
    }

    /**
     * Show logs in output panel
     */
    private showLogs(): void {
        Logger.show();
    }

    /**
     * Clear logs
     */
    private clearLogs(): void {
        Logger.clear();
    }

    /**
     * Show welcome message (legacy command)
     */
    private showWelcome(): void {
        vscode.window.showInformationMessage(
            'Welcome to SyncView! Use the status bar controls or command palette to start your Flutter preview.',
            'Start Preview'
        ).then(selection => {
            if (selection === 'Start Preview') {
                this.startPreview();
            }
        });
    }

    /**
     * Dispose all command registrations
     */
    public dispose(): void {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
    }
}
