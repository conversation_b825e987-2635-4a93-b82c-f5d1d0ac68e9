import * as vscode from 'vscode';

/**
 * Centralized logging utility for the SyncView extension
 */
export class Logger {
    private static outputChannel: vscode.OutputChannel;
    private static isInitialized = false;

    /**
     * Initialize the logger with VS Code output channel
     */
    public static initialize(context: vscode.ExtensionContext): void {
        if (this.isInitialized) {
            return;
        }

        this.outputChannel = vscode.window.createOutputChannel('SyncView');
        context.subscriptions.push(this.outputChannel);
        this.isInitialized = true;
    }

    /**
     * Log an info message
     */
    public static info(message: string, ...args: any[]): void {
        this.log('INFO', message, ...args);
    }

    /**
     * Log a warning message
     */
    public static warn(message: string, ...args: any[]): void {
        this.log('WARN', message, ...args);
    }

    /**
     * Log an error message
     */
    public static error(message: string, error?: any): void {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const stack = error instanceof Error ? error.stack : undefined;
        
        this.log('ERROR', message, errorMessage);
        if (stack) {
            this.outputChannel.appendLine(`Stack trace: ${stack}`);
        }
    }

    /**
     * Log a debug message (only in development)
     */
    public static debug(message: string, ...args: any[]): void {
        if (process.env.NODE_ENV === 'development') {
            this.log('DEBUG', message, ...args);
        }
    }

    /**
     * Show the output channel
     */
    public static show(): void {
        if (this.outputChannel) {
            this.outputChannel.show();
        }
    }

    /**
     * Clear the output channel
     */
    public static clear(): void {
        if (this.outputChannel) {
            this.outputChannel.clear();
        }
    }

    /**
     * Internal logging method
     */
    private static log(level: string, message: string, ...args: any[]): void {
        if (!this.isInitialized || !this.outputChannel) {
            console.log(`[${level}] ${message}`, ...args);
            return;
        }

        const timestamp = new Date().toISOString();
        const formattedArgs = args.length > 0 ? ` ${args.join(' ')}` : '';
        const logMessage = `[${timestamp}] [${level}] ${message}${formattedArgs}`;
        
        this.outputChannel.appendLine(logMessage);
        
        // Also log to console for development
        if (process.env.NODE_ENV === 'development') {
            console.log(logMessage);
        }
    }
}
