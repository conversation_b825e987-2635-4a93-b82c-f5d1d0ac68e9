"use strict";
/**
 * Constants used throughout the SyncView extension
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.URLS = exports.PATTERNS = exports.FLUTTER_CLI = exports.PERFORMANCE = exports.UI = exports.ERROR_CODES = exports.EVENTS = exports.FLUTTER_STATUS = exports.STATUS = exports.NETWORK = exports.FLUTTER_PATHS = exports.FILE_PATTERNS = exports.DEFAULT_CONFIG = exports.CONFIG_KEYS = exports.COMMANDS = exports.EXTENSION_VERSION = exports.EXTENSION_DISPLAY_NAME = exports.EXTENSION_NAME = exports.EXTENSION_ID = void 0;
// Extension metadata
exports.EXTENSION_ID = 'devgen-syncview';
exports.EXTENSION_NAME = 'SyncView';
exports.EXTENSION_DISPLAY_NAME = 'SyncView - Flutter Preview';
exports.EXTENSION_VERSION = '0.0.1';
// Command identifiers
exports.COMMANDS = {
    // Main preview commands
    START_PREVIEW: 'syncview.startPreview',
    STOP_PREVIEW: 'syncview.stopPreview',
    RESTART_PREVIEW: 'syncview.restartPreview',
    TOGGLE_PREVIEW: 'syncview.togglePreview',
    // WebView commands
    SHOW_PREVIEW: 'syncview.showPreview',
    HIDE_PREVIEW: 'syncview.hidePreview',
    REFRESH_PREVIEW: 'syncview.refreshPreview',
    // Flutter commands
    HOT_RELOAD: 'syncview.hotReload',
    HOT_RESTART: 'syncview.hotRestart',
    // Utility commands
    OPEN_SETTINGS: 'syncview.openSettings',
    SHOW_LOGS: 'syncview.showLogs',
    CLEAR_LOGS: 'syncview.clearLogs',
    // Legacy command
    HELLO_WORLD: 'devgen-syncview.helloWorld'
};
// Configuration keys
exports.CONFIG_KEYS = {
    // Flutter configuration
    FLUTTER_SDK_PATH: 'syncview.flutter.sdkPath',
    FLUTTER_WEB_PORT: 'syncview.flutter.webPort',
    FLUTTER_HOT_RELOAD_ON_SAVE: 'syncview.flutter.hotReloadOnSave',
    FLUTTER_DEBUG_MODE: 'syncview.flutter.debugMode',
    FLUTTER_ADDITIONAL_ARGS: 'syncview.flutter.additionalArgs',
    // Proxy configuration
    PROXY_PORT: 'syncview.proxy.port',
    PROXY_CORS_ENABLED: 'syncview.proxy.corsEnabled',
    PROXY_CORS_ORIGINS: 'syncview.proxy.corsOrigins',
    // UI configuration
    UI_SHOW_STATUS_BAR: 'syncview.ui.showStatusBar',
    UI_AUTO_SHOW_PREVIEW: 'syncview.ui.autoShowPreview',
    UI_PREVIEW_POSITION: 'syncview.ui.previewPosition',
    UI_DEVICE_FRAME: 'syncview.ui.deviceFrame',
    // Performance configuration
    PERFORMANCE_ENABLE_THROTTLING: 'syncview.performance.enableThrottling',
    PERFORMANCE_MAX_FRAME_RATE: 'syncview.performance.maxFrameRate',
    PERFORMANCE_MEMORY_LIMIT: 'syncview.performance.memoryLimit'
};
// Default configuration values
exports.DEFAULT_CONFIG = {
    FLUTTER_WEB_PORT: 0,
    FLUTTER_HOT_RELOAD_ON_SAVE: true,
    FLUTTER_DEBUG_MODE: false,
    FLUTTER_ADDITIONAL_ARGS: [],
    PROXY_PORT: 0,
    PROXY_CORS_ENABLED: true,
    PROXY_CORS_ORIGINS: ['*'],
    UI_SHOW_STATUS_BAR: true,
    UI_AUTO_SHOW_PREVIEW: true,
    UI_PREVIEW_POSITION: 'beside',
    UI_DEVICE_FRAME: 'none',
    PERFORMANCE_ENABLE_THROTTLING: false,
    PERFORMANCE_MAX_FRAME_RATE: 60,
    PERFORMANCE_MEMORY_LIMIT: 512
};
// File patterns and paths
exports.FILE_PATTERNS = {
    DART_FILES: '**/*.dart',
    PUBSPEC_YAML: '**/pubspec.yaml',
    FLUTTER_PROJECT: 'pubspec.yaml',
    WEB_INDEX: 'web/index.html',
    MAIN_DART: 'lib/main.dart'
};
exports.FLUTTER_PATHS = {
    PUBSPEC: 'pubspec.yaml',
    LIB_DIR: 'lib',
    WEB_DIR: 'web',
    TEST_DIR: 'test',
    BUILD_DIR: 'build',
    DART_TOOL_DIR: '.dart_tool'
};
// Network and server constants
exports.NETWORK = {
    DEFAULT_TIMEOUT: 30000,
    PROXY_TIMEOUT: 30000,
    FLUTTER_STARTUP_TIMEOUT: 120000,
    HOT_RELOAD_TIMEOUT: 10000,
    HOT_RESTART_TIMEOUT: 15000,
    DEFAULT_HOST: 'localhost',
    MIN_PORT: 1024,
    MAX_PORT: 65535,
    CORS_MAX_AGE: 86400 // 24 hours
};
// Status and state constants
exports.STATUS = {
    STOPPED: 'stopped',
    STARTING: 'starting',
    RUNNING: 'running',
    STOPPING: 'stopping',
    ERROR: 'error'
};
exports.FLUTTER_STATUS = {
    NOT_FOUND: 'not-found',
    FOUND: 'found',
    STARTING: 'starting',
    RUNNING: 'running',
    STOPPING: 'stopping',
    STOPPED: 'stopped',
    ERROR: 'error'
};
// Event names
exports.EVENTS = {
    // Flutter events
    FLUTTER_PROCESS_STARTED: 'processStarted',
    FLUTTER_PROCESS_STOPPED: 'processStopped',
    FLUTTER_PROCESS_ERROR: 'error',
    FLUTTER_HOT_RELOAD_COMPLETE: 'hotReloadComplete',
    FLUTTER_HOT_RESTART_COMPLETE: 'hotRestartComplete',
    FLUTTER_BUILD_COMPLETE: 'buildComplete',
    // Proxy events
    PROXY_SERVER_STARTED: 'serverStarted',
    PROXY_SERVER_STOPPED: 'serverStopped',
    PROXY_SERVER_ERROR: 'error',
    PROXY_REQUEST: 'request',
    PROXY_RESPONSE: 'proxyResponse',
    PROXY_TARGET_ERROR: 'targetError',
    // Workspace events
    WORKSPACE_CHANGED: 'workspaceChanged',
    PROJECT_DETECTED: 'projectDetected',
    PROJECT_REMOVED: 'projectRemoved',
    // Configuration events
    CONFIGURATION_CHANGED: 'configurationChanged',
    // Performance events
    PERFORMANCE_ISSUE: 'performanceIssue',
    METRICS_COLLECTED: 'metrics'
};
// Error codes
exports.ERROR_CODES = {
    FLUTTER_NOT_FOUND: 'FLUTTER_NOT_FOUND',
    FLUTTER_PROJECT_NOT_FOUND: 'FLUTTER_PROJECT_NOT_FOUND',
    FLUTTER_WEB_NOT_SUPPORTED: 'FLUTTER_WEB_NOT_SUPPORTED',
    FLUTTER_PROCESS_FAILED: 'FLUTTER_PROCESS_FAILED',
    PROXY_START_FAILED: 'PROXY_START_FAILED',
    PROXY_PORT_IN_USE: 'PROXY_PORT_IN_USE',
    PROXY_TARGET_UNREACHABLE: 'PROXY_TARGET_UNREACHABLE',
    WEBVIEW_CREATION_FAILED: 'WEBVIEW_CREATION_FAILED',
    WEBVIEW_LOAD_FAILED: 'WEBVIEW_LOAD_FAILED',
    CONFIGURATION_INVALID: 'CONFIGURATION_INVALID',
    WORKSPACE_INVALID: 'WORKSPACE_INVALID',
    PERFORMANCE_DEGRADED: 'PERFORMANCE_DEGRADED',
    MEMORY_LIMIT_EXCEEDED: 'MEMORY_LIMIT_EXCEEDED'
};
// UI constants
exports.UI = {
    STATUS_BAR_PRIORITY: 100,
    WEBVIEW_VIEW_TYPE: 'syncview.flutterPreview',
    // Icons (VS Code Codicons)
    ICONS: {
        PLAY: '$(play)',
        STOP: '$(stop)',
        REFRESH: '$(refresh)',
        ERROR: '$(error)',
        WARNING: '$(warning)',
        LOADING: '$(loading~spin)',
        CHECK: '$(check)',
        SETTINGS: '$(settings-gear)',
        LOG: '$(output)'
    },
    // Notification durations
    NOTIFICATION_DURATION: {
        SHORT: 2000,
        MEDIUM: 5000,
        LONG: 10000
    }
};
// Performance thresholds
exports.PERFORMANCE = {
    MEMORY_WARNING_MB: 100,
    MEMORY_CRITICAL_MB: 200,
    ERROR_RATE_WARNING: 10, // percentage
    ERROR_RATE_CRITICAL: 25, // percentage
    OPERATION_SLOW_MS: 5000,
    OPERATION_VERY_SLOW_MS: 10000,
    MONITORING_INTERVAL_MS: 5000,
    METRICS_HISTORY_SIZE: 100
};
// Flutter CLI constants
exports.FLUTTER_CLI = {
    COMMANDS: {
        VERSION: '--version',
        DEVICES: 'devices',
        DOCTOR: 'doctor',
        PUB_GET: 'pub get',
        CLEAN: 'clean',
        BUILD_WEB: 'build web',
        RUN_WEB: 'run -d web-server',
        CREATE: 'create',
        ANALYZE: 'analyze'
    },
    ARGS: {
        MACHINE_OUTPUT: '--machine',
        VERBOSE: '-v',
        WEB_PORT: '--web-port',
        WEB_HOSTNAME: '--web-hostname',
        PLATFORMS: '--platforms'
    },
    PLATFORMS: {
        WEB: 'web',
        ANDROID: 'android',
        IOS: 'ios',
        WINDOWS: 'windows',
        MACOS: 'macos',
        LINUX: 'linux'
    }
};
// Regex patterns
exports.PATTERNS = {
    FLUTTER_VERSION: /Flutter\s+(\d+\.\d+\.\d+)/,
    DART_VERSION: /Dart\s+(\d+\.\d+\.\d+)/,
    WEB_SERVER_URL: /A web server for this build is available at:\s*(https?:\/\/[^\s]+)/,
    HOT_RELOAD_COMPLETE: /Reloaded.*in\s+(\d+)ms/,
    HOT_RESTART_COMPLETE: /Restarted application/,
    BUILD_COMPLETE: /Built.*web/,
    COMPILATION_ERROR: /Error:|error:/,
    WARNING: /Warning:|warning:/
};
// External URLs
exports.URLS = {
    FLUTTER_INSTALL: 'https://flutter.dev/docs/get-started/install',
    FLUTTER_WEB_SETUP: 'https://flutter.dev/docs/get-started/web',
    FLUTTER_DOCTOR: 'https://flutter.dev/docs/get-started/install#run-flutter-doctor',
    EXTENSION_REPO: 'https://github.com/your-repo/syncview',
    EXTENSION_ISSUES: 'https://github.com/your-repo/syncview/issues',
    EXTENSION_DOCS: 'https://github.com/your-repo/syncview/wiki'
};
//# sourceMappingURL=index.js.map