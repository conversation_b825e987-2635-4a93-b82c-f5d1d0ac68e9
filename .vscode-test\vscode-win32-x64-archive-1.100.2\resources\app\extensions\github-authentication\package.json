{"name": "github-authentication", "displayName": "%displayName%", "description": "%description%", "publisher": "vscode", "license": "MIT", "version": "0.0.2", "engines": {"vscode": "^1.41.0"}, "icon": "images/icon.png", "categories": ["Other"], "api": "none", "extensionKind": ["ui", "workspace"], "activationEvents": [], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": "limited", "restrictedConfigurations": ["github-enterprise.uri"]}}, "contributes": {"authentication": [{"label": "GitHub", "id": "github"}, {"label": "GitHub Enterprise Server", "id": "github-enterprise"}], "configuration": [{"title": "%config.github-enterprise.title%", "properties": {"github-enterprise.uri": {"type": "string", "markdownDescription": "%config.github-enterprise.uri.description%", "pattern": "^(?:$|(https?)://(?!github\\.com).*)"}}}]}, "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "main": "./dist/extension.js", "browser": "./dist/browser/extension.js", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}