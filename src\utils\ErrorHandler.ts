import * as vscode from 'vscode';
import { Logger } from './Logger';

export interface ErrorContext {
    component: string;
    operation: string;
    details?: any;
}

export interface ErrorAction {
    title: string;
    action: () => void | Promise<void>;
}

/**
 * Centralized error handling utility
 */
export class ErrorHandler {
    private static readonly ERROR_CODES = {
        FLUTTER_NOT_FOUND: 'FLUTTER_NOT_FOUND',
        PROJECT_NOT_FOUND: 'PROJECT_NOT_FOUND',
        PROCESS_FAILED: 'PROCESS_FAILED',
        PROXY_ERROR: 'PROXY_ERROR',
        WEBVIEW_ERROR: 'WEBVIEW_ERROR',
        CONFIGURATION_ERROR: 'CONFIGURATION_ERROR'
    };

    /**
     * Handle and display an error with context
     */
    public static async handleError(
        error: Error | string,
        context: ErrorContext,
        actions?: ErrorAction[]
    ): Promise<void> {
        const errorMessage = typeof error === 'string' ? error : error.message;
        const fullMessage = `${context.component} - ${context.operation}: ${errorMessage}`;

        // Log the error
        Logger.error(fullMessage, typeof error === 'object' ? error : undefined);

        // Determine error type and create appropriate user message
        const userMessage = this.createUserFriendlyMessage(errorMessage, context);
        
        // Show error to user with actions
        if (actions && actions.length > 0) {
            const actionTitles = actions.map(action => action.title);
            const selection = await vscode.window.showErrorMessage(userMessage, ...actionTitles);
            
            if (selection) {
                const selectedAction = actions.find(action => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    } catch (actionError) {
                        Logger.error('Error executing error action', actionError);
                    }
                }
            }
        } else {
            vscode.window.showErrorMessage(userMessage);
        }
    }

    /**
     * Handle Flutter-specific errors
     */
    public static async handleFlutterError(error: Error | string, operation: string): Promise<void> {
        const context: ErrorContext = {
            component: 'Flutter',
            operation,
            details: { error }
        };

        const actions: ErrorAction[] = [];

        // Add specific actions based on error type
        const errorMessage = typeof error === 'string' ? error : error.message;
        
        if (errorMessage.includes('Flutter SDK not found')) {
            actions.push({
                title: 'Open Settings',
                action: () => vscode.commands.executeCommand('workbench.action.openSettings', 'syncview.flutter.sdkPath')
            });
        }

        if (errorMessage.includes('No connected devices')) {
            actions.push({
                title: 'Check Flutter Setup',
                action: () => vscode.env.openExternal(vscode.Uri.parse('https://flutter.dev/docs/get-started/install'))
            });
        }

        if (errorMessage.includes('pubspec.yaml')) {
            actions.push({
                title: 'Open pubspec.yaml',
                action: async () => {
                    const workspaceFolders = vscode.workspace.workspaceFolders;
                    if (workspaceFolders && workspaceFolders.length > 0) {
                        const pubspecUri = vscode.Uri.joinPath(workspaceFolders[0].uri, 'pubspec.yaml');
                        await vscode.window.showTextDocument(pubspecUri);
                    }
                }
            });
        }

        actions.push({
            title: 'Show Logs',
            action: () => Logger.show()
        });

        await this.handleError(error, context, actions);
    }

    /**
     * Handle proxy server errors
     */
    public static async handleProxyError(error: Error | string, operation: string): Promise<void> {
        const context: ErrorContext = {
            component: 'Proxy Server',
            operation,
            details: { error }
        };

        const actions: ErrorAction[] = [
            {
                title: 'Restart Proxy',
                action: () => vscode.commands.executeCommand('syncview.restartPreview')
            },
            {
                title: 'Show Logs',
                action: () => Logger.show()
            }
        ];

        await this.handleError(error, context, actions);
    }

    /**
     * Handle WebView errors
     */
    public static async handleWebViewError(error: Error | string, operation: string): Promise<void> {
        const context: ErrorContext = {
            component: 'WebView',
            operation,
            details: { error }
        };

        const actions: ErrorAction[] = [
            {
                title: 'Refresh Preview',
                action: () => vscode.commands.executeCommand('syncview.refreshPreview')
            },
            {
                title: 'Restart Preview',
                action: () => vscode.commands.executeCommand('syncview.restartPreview')
            },
            {
                title: 'Show Logs',
                action: () => Logger.show()
            }
        ];

        await this.handleError(error, context, actions);
    }

    /**
     * Handle configuration errors
     */
    public static async handleConfigurationError(error: Error | string, setting?: string): Promise<void> {
        const context: ErrorContext = {
            component: 'Configuration',
            operation: setting ? `Setting: ${setting}` : 'General',
            details: { error, setting }
        };

        const actions: ErrorAction[] = [
            {
                title: 'Open Settings',
                action: () => vscode.commands.executeCommand('workbench.action.openSettings', 'syncview')
            },
            {
                title: 'Reset to Defaults',
                action: async () => {
                    const confirm = await vscode.window.showWarningMessage(
                        'Reset all SyncView settings to defaults?',
                        'Yes', 'No'
                    );
                    if (confirm === 'Yes') {
                        // Reset configuration logic would go here
                        vscode.window.showInformationMessage('Settings reset to defaults');
                    }
                }
            }
        ];

        await this.handleError(error, context, actions);
    }

    /**
     * Create user-friendly error messages
     */
    private static createUserFriendlyMessage(errorMessage: string, context: ErrorContext): string {
        // Flutter-specific messages
        if (context.component === 'Flutter') {
            if (errorMessage.includes('Flutter SDK not found')) {
                return 'Flutter SDK not found. Please configure the Flutter SDK path in settings.';
            }
            if (errorMessage.includes('No connected devices')) {
                return 'No Flutter devices available. Web preview requires Flutter web support.';
            }
            if (errorMessage.includes('pubspec.yaml')) {
                return 'Flutter project configuration error. Please check your pubspec.yaml file.';
            }
            if (errorMessage.includes('No Flutter project found')) {
                return 'No Flutter project found in the current workspace. Please open a Flutter project.';
            }
        }

        // Proxy-specific messages
        if (context.component === 'Proxy Server') {
            if (errorMessage.includes('EADDRINUSE')) {
                return 'Proxy server port is already in use. Try restarting the preview or changing the port in settings.';
            }
            if (errorMessage.includes('ECONNREFUSED')) {
                return 'Cannot connect to Flutter development server. Make sure Flutter is running.';
            }
        }

        // WebView-specific messages
        if (context.component === 'WebView') {
            if (errorMessage.includes('Failed to load')) {
                return 'Failed to load Flutter preview. The development server may not be ready yet.';
            }
        }

        // Generic message
        return `${context.component} error during ${context.operation}: ${errorMessage}`;
    }

    /**
     * Show a warning message with optional actions
     */
    public static async showWarning(
        message: string,
        actions?: ErrorAction[]
    ): Promise<void> {
        Logger.warn(message);

        if (actions && actions.length > 0) {
            const actionTitles = actions.map(action => action.title);
            const selection = await vscode.window.showWarningMessage(message, ...actionTitles);
            
            if (selection) {
                const selectedAction = actions.find(action => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    } catch (actionError) {
                        Logger.error('Error executing warning action', actionError);
                    }
                }
            }
        } else {
            vscode.window.showWarningMessage(message);
        }
    }

    /**
     * Show an information message with optional actions
     */
    public static async showInfo(
        message: string,
        actions?: ErrorAction[]
    ): Promise<void> {
        Logger.info(message);

        if (actions && actions.length > 0) {
            const actionTitles = actions.map(action => action.title);
            const selection = await vscode.window.showInformationMessage(message, ...actionTitles);
            
            if (selection) {
                const selectedAction = actions.find(action => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    } catch (actionError) {
                        Logger.error('Error executing info action', actionError);
                    }
                }
            }
        } else {
            vscode.window.showInformationMessage(message);
        }
    }

    /**
     * Get error codes for programmatic error handling
     */
    public static getErrorCodes() {
        return this.ERROR_CODES;
    }
}
