import { EventEmitter } from "events";
import * as http from "http";
import { <PERSON>rs<PERSON>and<PERSON> } from "./CorsHandler";
import { Logger } from "../utils/Logger";

/**
 * Simple HTTP proxy server for Flutter web preview
 * This is a basic implementation without external dependencies
 */
export class ProxyServer extends EventEmitter {
  private server: http.Server | undefined;
  private targetUrl: string | undefined;
  private proxyPort: number | undefined;
  private isRunning = false;
  private requestCount = 0;
  private errorCount = 0;

  constructor(private readonly corsHandler: CorsHandler) {
    super();
  }

  /**
   * Start the proxy server
   */
  public async start(port?: number): Promise<string> {
    if (this.isRunning) {
      throw new Error("Proxy server is already running");
    }

    try {
      // Create HTTP server
      this.server = http.createServer((req, res) => {
        this.handleRequest(req, res);
      });

      // Start server
      const serverPort = port || (await this.findAvailablePort());
      await this.startServer(serverPort);

      this.proxyPort = serverPort;
      this.isRunning = true;

      const proxyUrl = `http://localhost:${serverPort}`;
      Logger.info(`Proxy server started on ${proxyUrl}`);

      return proxyUrl;
    } catch (error) {
      this.isRunning = false;
      Logger.error("Failed to start proxy server", error);
      throw error;
    }
  }

  /**
   * Stop the proxy server
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      if (this.server) {
        await new Promise<void>((resolve, reject) => {
          this.server!.close((error) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });
      }

      this.isRunning = false;
      this.server = undefined;
      this.proxyPort = undefined;

      Logger.info("Proxy server stopped");
    } catch (error) {
      Logger.error("Error stopping proxy server", error);
      throw error;
    }
  }

  /**
   * Set the target URL for proxying
   */
  public setTargetUrl(url: string): void {
    this.targetUrl = url;
    Logger.info(`Proxy target URL set to: ${url}`);
  }

  /**
   * Handle HTTP requests
   */
  private handleRequest(
    req: http.IncomingMessage,
    res: http.ServerResponse
  ): void {
    this.requestCount++;
    this.emit("request", req);

    // Apply CORS headers
    this.corsHandler.applyCorsHeaders(req, res);

    // Handle preflight requests
    if (req.method === "OPTIONS") {
      res.writeHead(200);
      res.end();
      return;
    }

    // Check if target URL is set
    if (!this.targetUrl) {
      res.writeHead(503, { "Content-Type": "text/plain" });
      res.end("Flutter development server not ready");
      return;
    }

    // Simple proxy implementation
    this.proxyRequest(req, res);
  }

  /**
   * Simple proxy request implementation
   */
  private proxyRequest(
    req: http.IncomingMessage,
    res: http.ServerResponse
  ): void {
    try {
      const targetUrl = new URL(this.targetUrl!);
      const options: http.RequestOptions = {
        hostname: targetUrl.hostname,
        port: targetUrl.port,
        path: req.url,
        method: req.method,
        headers: { ...req.headers },
      };

      // Remove host header to avoid conflicts
      if (
        options.headers &&
        typeof options.headers === "object" &&
        !Array.isArray(options.headers)
      ) {
        delete (options.headers as any).host;
      }

      const proxyReq = http.request(options, (proxyRes) => {
        // Copy status and headers
        res.writeHead(proxyRes.statusCode || 200, proxyRes.headers);

        // Pipe response
        proxyRes.pipe(res);

        this.emit("proxyResponse", proxyRes);
      });

      proxyReq.on("error", (error) => {
        this.handleProxyError(error, req, res);
      });

      // Pipe request body
      req.pipe(proxyReq);
    } catch (error) {
      this.handleProxyError(error, req, res);
    }
  }

  /**
   * Handle proxy errors
   */
  private handleProxyError(
    error: any,
    _req: http.IncomingMessage,
    res: http.ServerResponse
  ): void {
    this.errorCount++;
    Logger.error("Proxy request error", error);

    if (!res.headersSent) {
      res.writeHead(502, { "Content-Type": "text/plain" });
      res.end("Bad Gateway: Flutter development server unavailable");
    }

    this.emit("targetError", error);
  }

  /**
   * Start the HTTP server
   */
  private startServer(port: number): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.server) {
        reject(new Error("Server not created"));
        return;
      }

      this.server.listen(port, "localhost", () => {
        resolve();
      });

      this.server.on("error", (error) => {
        reject(error);
      });
    });
  }

  /**
   * Find an available port
   */
  private async findAvailablePort(startPort: number = 8081): Promise<number> {
    return new Promise((resolve, reject) => {
      const server = http.createServer();

      server.listen(0, "localhost", () => {
        const address = server.address();
        const port =
          typeof address === "object" && address ? address.port : startPort;

        server.close(() => {
          resolve(port);
        });
      });

      server.on("error", (error) => {
        reject(error);
      });
    });
  }

  /**
   * Get proxy server statistics
   */
  public getStats(): any {
    return {
      isRunning: this.isRunning,
      port: this.proxyPort,
      targetUrl: this.targetUrl,
      requestCount: this.requestCount,
      errorCount: this.errorCount,
    };
  }

  /**
   * Check if server is running
   */
  public isServerRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Get the proxy port
   */
  public getPort(): number | undefined {
    return this.proxyPort;
  }
}
