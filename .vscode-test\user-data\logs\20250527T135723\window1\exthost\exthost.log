2025-05-27 13:57:31.455 [info] Extension host with pid 10748 started
2025-05-27 13:57:31.465 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-05-27 13:57:31.508 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-27 13:57:31.516 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-27 13:57:31.631 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-27 13:57:32.050 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-05-27 13:57:32.125 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-05-27 13:57:32.145 [info] Eager extensions activated
2025-05-27 13:57:33.977 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-27 13:57:33.982 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-27 13:57:34.343 [info] Extension host terminating: renderer closed the MessagePort
2025-05-27 13:57:34.361 [info] Extension host with pid 10748 exiting with code 0
