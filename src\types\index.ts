/**
 * Type definitions for the SyncView extension
 */

// Extension-wide interfaces
export interface ExtensionContext {
    subscriptions: any[];
    workspaceState: any;
    globalState: any;
    extensionPath: string;
}

// Flutter-related types
export interface FlutterProject {
    name: string;
    path: string;
    version: string;
    description?: string;
    hasWebSupport: boolean;
    dependencies: string[];
}

export interface FlutterProcess {
    pid: number;
    command: string;
    args: string[];
    workingDirectory: string;
    startTime: number;
    status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error';
}

export interface FlutterServerInfo {
    url: string;
    port: number;
    hostname: string;
    protocol: 'http' | 'https';
}

// Proxy-related types
export interface ProxyConfig {
    port?: number;
    targetUrl?: string;
    corsEnabled: boolean;
    corsOrigins: string[];
    timeout: number;
}

export interface ProxyStats {
    isRunning: boolean;
    port?: number;
    targetUrl?: string;
    requestCount: number;
    errorCount: number;
    uptime: number;
}

// WebView-related types
export interface WebViewState {
    isVisible: boolean;
    currentUrl?: string;
    isLoading: boolean;
    hasError: boolean;
    errorMessage?: string;
}

export interface WebViewMessage {
    type: 'ready' | 'error' | 'reload' | 'loaded' | 'openDevTools';
    data?: any;
    error?: string;
}

// Configuration types
export interface SyncViewConfig {
    flutter: FlutterConfig;
    proxy: ProxyConfig;
    ui: UIConfig;
    performance: PerformanceConfig;
}

export interface FlutterConfig {
    sdkPath?: string;
    webPort?: number;
    hotReloadOnSave: boolean;
    debugMode: boolean;
    additionalArgs: string[];
}

export interface UIConfig {
    showStatusBar: boolean;
    autoShowPreview: boolean;
    previewPosition: 'beside' | 'active';
    deviceFrame: 'none' | 'ios' | 'android';
}

export interface PerformanceConfig {
    enableThrottling: boolean;
    maxFrameRate: number;
    memoryLimit: number;
}

// Status and state types
export type ExtensionStatus = 'inactive' | 'initializing' | 'ready' | 'running' | 'error';
export type PreviewStatus = 'stopped' | 'starting' | 'running' | 'stopping' | 'error';
export type FlutterStatus = 'not-found' | 'found' | 'starting' | 'running' | 'stopping' | 'stopped' | 'error';

// Event types
export interface ExtensionEvent {
    type: string;
    timestamp: number;
    data?: any;
}

export interface FlutterEvent extends ExtensionEvent {
    type: 'processStarted' | 'processStopped' | 'processError' | 'hotReload' | 'hotRestart' | 'buildComplete';
    processId?: number;
    url?: string;
    error?: Error;
}

export interface ProxyEvent extends ExtensionEvent {
    type: 'serverStarted' | 'serverStopped' | 'serverError' | 'request' | 'response';
    url?: string;
    error?: Error;
    requestCount?: number;
}

export interface WorkspaceEvent extends ExtensionEvent {
    type: 'workspaceChanged' | 'projectDetected' | 'projectRemoved' | 'configChanged';
    projectPath?: string;
    hasFlutterProject?: boolean;
    hasWebSupport?: boolean;
}

// Error types
export interface ExtensionError extends Error {
    code: string;
    component: string;
    operation: string;
    recoverable: boolean;
    userMessage: string;
}

export interface FlutterError extends ExtensionError {
    flutterCommand?: string;
    flutterArgs?: string[];
    flutterOutput?: string;
}

export interface ProxyError extends ExtensionError {
    proxyPort?: number;
    targetUrl?: string;
    requestUrl?: string;
}

// Utility types
export interface Disposable {
    dispose(): void;
}

export interface EventEmitter {
    on(event: string, listener: (...args: any[]) => void): void;
    off(event: string, listener: (...args: any[]) => void): void;
    emit(event: string, ...args: any[]): void;
}

// Command types
export interface CommandDefinition {
    command: string;
    title: string;
    category?: string;
    when?: string;
    icon?: string;
}

export interface CommandContext {
    extensionManager: any;
    flutterManager: any;
    webViewManager: any;
    statusBarManager: any;
}

// Validation types
export interface ValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}

export interface FlutterValidation extends ValidationResult {
    flutterVersion?: string;
    dartVersion?: string;
    webSupported: boolean;
    doctorOutput?: string;
}

// Performance types
export interface PerformanceMetric {
    name: string;
    value: number;
    unit: string;
    timestamp: number;
    category: 'memory' | 'cpu' | 'network' | 'operation';
}

export interface ComponentPerformance {
    componentName: string;
    operationCount: number;
    totalDuration: number;
    averageDuration: number;
    errorCount: number;
    lastOperation: number;
}

// Logging types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: number;
    component?: string;
    data?: any;
    error?: Error;
}

// File system types
export interface FileWatchEvent {
    type: 'created' | 'changed' | 'deleted';
    path: string;
    timestamp: number;
}

export interface ProjectStructure {
    rootPath: string;
    pubspecPath: string;
    libPath: string;
    webPath?: string;
    testPath?: string;
    hasWebSupport: boolean;
    dependencies: string[];
}

// Network types
export interface NetworkRequest {
    method: string;
    url: string;
    headers: Record<string, string>;
    body?: any;
    timestamp: number;
}

export interface NetworkResponse {
    statusCode: number;
    headers: Record<string, string>;
    body?: any;
    duration: number;
    timestamp: number;
}

// Extension lifecycle types
export interface ActivationContext {
    subscriptions: any[];
    workspaceState: any;
    globalState: any;
    extensionPath: string;
    extensionUri: any;
}

export interface DeactivationContext {
    reason: 'disable' | 'uninstall' | 'reload' | 'shutdown';
}
