{"version": 3, "file": "WebViewManager.js", "sourceRoot": "", "sources": ["../../src/webview/WebViewManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAE7B,4CAAyC;AAEzC;;GAEG;AACH,MAAa,cAAc;IAMF;IACA;IANb,YAAY,CAAkC;IAC9C,iBAAiB,CAAqB;IAC7B,WAAW,GAAwB,EAAE,CAAC;IAEvD,YACqB,OAAgC,EAChC,kBAAsC;QADtC,YAAO,GAAP,OAAO,CAAyB;QAChC,uBAAkB,GAAlB,kBAAkB,CAAoB;IACxD,CAAC;IAEJ;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC3B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAChD,iBAAiB,EACjB,iBAAiB,EACjB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;aAClE;SACJ,CACJ,CAAC;QAEF,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvD,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAClC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3B,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CACzC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAC7C,SAAS,EACT,IAAI,CAAC,WAAW,CACnB,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,GAAW;QAC/B,IAAI,CAAC,iBAAiB,GAAG,GAAG,CAAC;QAC7B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YAC1D,eAAM,CAAC,IAAI,CAAC,wBAAwB,GAAG,EAAE,CAAC,CAAC;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QAC/B,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,uDAAuD,CAAC,CAAC;QAC9F,CAAC;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,kFAAkF;YAClF,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACvB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACvD,oCAAoC;YACpC,UAAU,CAAC,GAAG,EAAE;gBACZ,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC9C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACjF,CAAC;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;QACZ,CAAC;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAY;QACrC,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;YACtB,KAAK,OAAO;gBACR,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;gBAClE,MAAM;YACV,KAAK,QAAQ;gBACT,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,MAAM;YACV;gBACI,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA8CN,CAAC;IACN,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAW;QAC9B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA6BY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SA+BrB,CAAC;IACN,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;CACJ;AA/PD,wCA+PC"}