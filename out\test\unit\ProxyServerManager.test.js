"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const ProxyServerManager_1 = require("../../proxy/ProxyServerManager");
const Logger_1 = require("../../utils/Logger");
suite("ProxyServerManager Tests", () => {
    let proxyManager;
    suiteSetup(async () => {
        // Initialize logger for tests
        const context = {
            subscriptions: [],
        };
        Logger_1.Logger.initialize(context);
    });
    setup(() => {
        proxyManager = new ProxyServerManager_1.ProxyServerManager();
    });
    teardown(async () => {
        if (proxyManager && proxyManager.isRunning()) {
            await proxyManager.stop();
        }
        if (proxyManager) {
            proxyManager.dispose();
        }
    });
    test("Should create ProxyServerManager instance", () => {
        assert.ok(proxyManager);
        assert.strictEqual(proxyManager.isRunning(), false);
    });
    test("Should handle server state correctly", () => {
        assert.strictEqual(proxyManager.isRunning(), false);
        assert.strictEqual(proxyManager.getProxyUrl(), undefined);
        assert.strictEqual(proxyManager.getTargetUrl(), undefined);
    });
    test("Should set target URL", () => {
        const testUrl = "http://localhost:8080";
        proxyManager.setTargetUrl(testUrl);
        assert.strictEqual(proxyManager.getTargetUrl(), testUrl);
    });
    test("Should emit events correctly", (done) => {
        let eventReceived = false;
        proxyManager.on("error", (error) => {
            eventReceived = true;
            assert.ok(error);
            done();
        });
        // Simulate an error event
        proxyManager.emit("error", new Error("Test proxy error"));
        // Fallback timeout
        setTimeout(() => {
            if (!eventReceived) {
                done(new Error("Event not received"));
            }
        }, 1000);
    });
    test("Should get stats", () => {
        const stats = proxyManager.getStats();
        // Stats should be null when server is not running
        assert.strictEqual(stats, null);
    });
    test("Should dispose cleanly", () => {
        assert.doesNotThrow(() => {
            proxyManager.dispose();
        });
    });
});
//# sourceMappingURL=ProxyServerManager.test.js.map