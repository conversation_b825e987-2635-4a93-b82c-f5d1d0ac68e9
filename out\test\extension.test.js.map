{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../src/test/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AAEjC,uCAAuC;AACvC,4CAAyC;AACzC,kDAA+C;AAC/C,wDAAqD;AACrD,8EAA2E;AAE3E,KAAK,CAAC,+BAA+B,EAAE,GAAG,EAAE;IAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,4BAA4B,CAAC,CAAC;IAEnE,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,8BAA8B;QAC9B,MAAM,OAAO,GAAG;YACd,aAAa,EAAE,EAAE;SACX,CAAC;QACT,eAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,cAAc,EAAE,GAAG,EAAE;QACzB,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACtC,wCAAwC;YACxC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;gBACvB,eAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACjC,eAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;gBACpC,eAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC5B,IAAI,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC5C,MAAM,QAAQ,GAAG,6BAA6B,CAAC;YAC/C,MAAM,UAAU,GAAG,qBAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YACrD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,2BAA2B,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;YAC/C,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;YACrE,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,wCAAwC,EAAE,GAAG,EAAE;YAClD,MAAM,CAAC,WAAW,CAChB,qBAAS,CAAC,2BAA2B,CAAC,WAAW,CAAC,EAClD,MAAM,CACP,CAAC;YACF,MAAM,CAAC,WAAW,CAChB,qBAAS,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,EACxD,cAAc,CACf,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC5C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;gBACjC,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC/D,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;gBAC5D,MAAM,CAAC,WAAW,CAAC,qBAAS,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC/B,IAAI,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACnD,uDAAuD;YACvD,MAAM,aAAa,GAAG,MAAM,2BAAY,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACpE,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,oBAAoB,GAAG,MAAM,2BAAY,CAAC,kBAAkB,CAChE,mCAAmC,CACpC,CAAC;YACF,MAAM,CAAC,WAAW,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACzC,IAAI,QAAgC,CAAC;QAErC,KAAK,CAAC,GAAG,EAAE;YACT,QAAQ,GAAG,IAAI,+CAAsB,EAAE,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iCAAiC,EAAE,GAAG,EAAE;YAC3C,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YAC3D,wDAAwD;YACxD,mDAAmD;YACnD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,sBAAsB,CAClD,oBAAoB,CACrB,CAAC;YACF,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,oBAAoB,EAAE,GAAG,EAAE;QAC/B,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE;YACvC,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAC9C,qCAAqC,CACtC,CAAC;YACF,oFAAoF;YACpF,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;gBACvB,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;YAC/C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAEzD,sCAAsC;YACtC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAC/C,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAC5B,CAAC;YAEF,4DAA4D;YAC5D,oDAAoD;YACpD,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;gBACvB,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}