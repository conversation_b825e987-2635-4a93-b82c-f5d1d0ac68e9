"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlutterProjectDetector = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const Logger_1 = require("../utils/Logger");
/**
 * Detects and validates Flutter projects in the workspace
 */
class FlutterProjectDetector {
    /**
     * Find the Flutter project in the current workspace
     */
    async findFlutterProject() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            Logger_1.Logger.warn('No workspace folders found');
            return undefined;
        }
        // Check each workspace folder for Flutter project
        for (const folder of workspaceFolders) {
            const projectPath = await this.detectFlutterProjectInPath(folder.uri.fsPath);
            if (projectPath) {
                return projectPath;
            }
        }
        // If no direct Flutter project found, check subdirectories
        for (const folder of workspaceFolders) {
            const projectPath = await this.searchFlutterProjectInSubdirectories(folder.uri.fsPath);
            if (projectPath) {
                return projectPath;
            }
        }
        return undefined;
    }
    /**
     * Check if a specific path contains a Flutter project
     */
    async detectFlutterProjectInPath(projectPath) {
        try {
            const pubspecPath = path.join(projectPath, 'pubspec.yaml');
            if (!fs.existsSync(pubspecPath)) {
                return undefined;
            }
            const pubspecContent = fs.readFileSync(pubspecPath, 'utf8');
            // Check if it's a Flutter project
            if (this.isFlutterProject(pubspecContent)) {
                Logger_1.Logger.info(`Flutter project detected at: ${projectPath}`);
                return projectPath;
            }
            return undefined;
        }
        catch (error) {
            Logger_1.Logger.error(`Error detecting Flutter project in ${projectPath}`, error);
            return undefined;
        }
    }
    /**
     * Search for Flutter projects in subdirectories
     */
    async searchFlutterProjectInSubdirectories(rootPath, maxDepth = 2) {
        try {
            const entries = fs.readdirSync(rootPath, { withFileTypes: true });
            for (const entry of entries) {
                if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    const subPath = path.join(rootPath, entry.name);
                    const projectPath = await this.detectFlutterProjectInPath(subPath);
                    if (projectPath) {
                        return projectPath;
                    }
                    // Recursively search subdirectories
                    if (maxDepth > 1) {
                        const deepProjectPath = await this.searchFlutterProjectInSubdirectories(subPath, maxDepth - 1);
                        if (deepProjectPath) {
                            return deepProjectPath;
                        }
                    }
                }
            }
        }
        catch (error) {
            Logger_1.Logger.error(`Error searching subdirectories in ${rootPath}`, error);
        }
        return undefined;
    }
    /**
     * Check if pubspec.yaml content indicates a Flutter project
     */
    isFlutterProject(pubspecContent) {
        // Check for Flutter dependency
        const hasFlutterDependency = /^\s*flutter:\s*$/m.test(pubspecContent);
        // Check for Flutter SDK dependency
        const hasFlutterSdk = /^\s*sdk:\s*flutter\s*$/m.test(pubspecContent);
        return hasFlutterDependency || hasFlutterSdk;
    }
    /**
     * Check if directory should be skipped during search
     */
    shouldSkipDirectory(dirName) {
        const skipDirs = [
            'node_modules',
            '.git',
            '.vscode',
            '.idea',
            'build',
            '.dart_tool',
            'ios',
            'android',
            'web',
            'windows',
            'macos',
            'linux'
        ];
        return skipDirs.includes(dirName) || dirName.startsWith('.');
    }
    /**
     * Validate Flutter project structure
     */
    async validateFlutterProject(projectPath) {
        try {
            const requiredFiles = [
                'pubspec.yaml',
                'lib/main.dart'
            ];
            for (const file of requiredFiles) {
                const filePath = path.join(projectPath, file);
                if (!fs.existsSync(filePath)) {
                    Logger_1.Logger.warn(`Required Flutter file missing: ${file}`);
                    return false;
                }
            }
            // Check if web support is enabled
            const webDir = path.join(projectPath, 'web');
            if (!fs.existsSync(webDir)) {
                Logger_1.Logger.warn('Web support not found. Run "flutter create . --platforms web" to enable web support.');
                return false;
            }
            return true;
        }
        catch (error) {
            Logger_1.Logger.error(`Error validating Flutter project at ${projectPath}`, error);
            return false;
        }
    }
    /**
     * Get Flutter project information
     */
    async getProjectInfo(projectPath) {
        try {
            const pubspecPath = path.join(projectPath, 'pubspec.yaml');
            const pubspecContent = fs.readFileSync(pubspecPath, 'utf8');
            // Parse basic project info (simplified YAML parsing)
            const nameMatch = pubspecContent.match(/^name:\s*(.+)$/m);
            const versionMatch = pubspecContent.match(/^version:\s*(.+)$/m);
            const descriptionMatch = pubspecContent.match(/^description:\s*(.+)$/m);
            return {
                name: nameMatch ? nameMatch[1].trim() : 'Unknown',
                version: versionMatch ? versionMatch[1].trim() : '1.0.0',
                description: descriptionMatch ? descriptionMatch[1].trim() : '',
                path: projectPath
            };
        }
        catch (error) {
            Logger_1.Logger.error(`Error getting project info for ${projectPath}`, error);
            return null;
        }
    }
    /**
     * Check if Flutter web is supported
     */
    async isWebSupported(projectPath) {
        try {
            const webDir = path.join(projectPath, 'web');
            const indexHtml = path.join(webDir, 'index.html');
            return fs.existsSync(webDir) && fs.existsSync(indexHtml);
        }
        catch (error) {
            Logger_1.Logger.error(`Error checking web support for ${projectPath}`, error);
            return false;
        }
    }
    /**
     * Get all Flutter projects in workspace
     */
    async getAllFlutterProjects() {
        const projects = [];
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return projects;
        }
        for (const folder of workspaceFolders) {
            await this.collectFlutterProjects(folder.uri.fsPath, projects);
        }
        return projects;
    }
    /**
     * Recursively collect Flutter projects
     */
    async collectFlutterProjects(rootPath, projects, maxDepth = 3) {
        try {
            const projectPath = await this.detectFlutterProjectInPath(rootPath);
            if (projectPath) {
                projects.push(projectPath);
                return; // Don't search subdirectories if current directory is a Flutter project
            }
            if (maxDepth <= 0) {
                return;
            }
            const entries = fs.readdirSync(rootPath, { withFileTypes: true });
            for (const entry of entries) {
                if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    const subPath = path.join(rootPath, entry.name);
                    await this.collectFlutterProjects(subPath, projects, maxDepth - 1);
                }
            }
        }
        catch (error) {
            Logger_1.Logger.error(`Error collecting Flutter projects in ${rootPath}`, error);
        }
    }
}
exports.FlutterProjectDetector = FlutterProjectDetector;
//# sourceMappingURL=FlutterProjectDetector.js.map