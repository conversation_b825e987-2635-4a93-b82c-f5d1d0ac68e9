{"version": 3, "file": "CorsHandler.js", "sourceRoot": "", "sources": ["../../src/proxy/CorsHandler.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AAUzC;;GAEG;AACH,MAAa,WAAW;IACZ,OAAO,CAAc;IAE7B,YAAY,OAA8B;QACtC,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;YACrE,OAAO,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,CAAC;YAClF,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,KAAK,EAAE,WAAW;YAC1B,GAAG,OAAO;SACb,CAAC;IACN,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,GAAyB,EAAE,GAAwB;QACvE,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;QAElC,kCAAkC;QAClC,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,GAAG,CAAC,SAAS,CAAC,6BAA6B,EAAE,MAAM,IAAI,GAAG,CAAC,CAAC;QAChE,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5C,GAAG,CAAC,SAAS,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC;QACtD,CAAC;QAED,mCAAmC;QACnC,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE/E,mCAAmC;QACnC,MAAM,cAAc,GAAG,GAAG,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QACrE,IAAI,cAAc,EAAE,CAAC;YACjB,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,cAAc,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACJ,GAAG,CAAC,SAAS,CAAC,8BAA8B,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACnF,CAAC;QAED,uCAAuC;QACvC,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3B,GAAG,CAAC,SAAS,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;QAC9D,CAAC;QAED,6BAA6B;QAC7B,GAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAExE,8CAA8C;QAC9C,GAAG,CAAC,SAAS,CAAC,+BAA+B,EAAE,8BAA8B,CAAC,CAAC;QAE/E,eAAM,CAAC,KAAK,CAAC,oCAAoC,MAAM,IAAI,MAAM,EAAE,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,MAA0B;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,OAAO,IAAI,CAAC,CAAC,oDAAoD;QACrE,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YAC7C,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;gBAC3B,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,oDAAoD;YACpD,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC1C,OAAO,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,MAAM,KAAK,MAAM,CAAC;YAC9D,CAAC;YAED,OAAO,KAAK,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,OAA6B;QAC1C,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QAC/C,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAc;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClC,eAAM,CAAC,IAAI,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAAc;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAc;QAC3B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACvC,eAAM,CAAC,IAAI,CAAC,sBAAsB,WAAW,EAAE,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAAc;QAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QACxD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAc;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAClC,eAAM,CAAC,IAAI,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAC;QAChD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAAc;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC;YACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,CAAC,GAAG,CAAC;YACd,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;YACrE,OAAO,EAAE,CAAC,QAAQ,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,CAAC;YAClF,WAAW,EAAE,IAAI;YACjB,MAAM,EAAE,KAAK;SAChB,CAAC;QACF,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,UAAU;QACb,OAAO,CAAC,GAAyB,EAAE,GAAwB,EAAE,IAAiB,EAAE,EAAE;YAC9E,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAEhC,4BAA4B;YAC5B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC3B,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACnB,GAAG,CAAC,GAAG,EAAE,CAAC;gBACV,OAAO;YACX,CAAC;YAED,IAAI,IAAI,EAAE,CAAC;gBACP,IAAI,EAAE,CAAC;YACX,CAAC;QACL,CAAC,CAAC;IACN,CAAC;CACJ;AAhMD,kCAgMC"}