import * as vscode from 'vscode';
import * as path from 'path';
import { EventEmitter } from 'events';
import { FlutterProjectDetector } from '../flutter/FlutterProjectDetector';
import { FileUtils } from '../utils/FileUtils';
import { Logger } from '../utils/Logger';

export interface WorkspaceInfo {
    hasFlutterProject: boolean;
    flutterProjectPath?: string;
    projectName?: string;
    hasWebSupport: boolean;
    workspaceFolders: string[];
}

/**
 * Manages workspace-related functionality and Flutter project detection
 */
export class WorkspaceManager extends EventEmitter implements vscode.Disposable {
    private disposables: vscode.Disposable[] = [];
    private projectDetector: FlutterProjectDetector;
    private currentWorkspaceInfo: WorkspaceInfo | undefined;
    private fileWatcher: vscode.FileSystemWatcher | undefined;

    constructor() {
        super();
        this.projectDetector = new FlutterProjectDetector();
        this.setupWorkspaceWatchers();
        this.initializeWorkspace();
    }

    /**
     * Get current workspace information
     */
    public async getWorkspaceInfo(): Promise<WorkspaceInfo> {
        if (!this.currentWorkspaceInfo) {
            await this.refreshWorkspaceInfo();
        }
        return this.currentWorkspaceInfo!;
    }

    /**
     * Refresh workspace information
     */
    public async refreshWorkspaceInfo(): Promise<void> {
        const workspaceFolders = vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath) || [];
        
        let hasFlutterProject = false;
        let flutterProjectPath: string | undefined;
        let projectName: string | undefined;
        let hasWebSupport = false;

        if (workspaceFolders.length > 0) {
            // Find Flutter project
            flutterProjectPath = await this.projectDetector.findFlutterProject();
            
            if (flutterProjectPath) {
                hasFlutterProject = true;
                hasWebSupport = await this.projectDetector.isWebSupported(flutterProjectPath);
                
                // Get project name from pubspec.yaml
                const projectInfo = await this.projectDetector.getProjectInfo(flutterProjectPath);
                projectName = projectInfo?.name;
            }
        }

        const oldInfo = this.currentWorkspaceInfo;
        this.currentWorkspaceInfo = {
            hasFlutterProject,
            flutterProjectPath,
            projectName,
            hasWebSupport,
            workspaceFolders
        };

        // Emit change event if workspace info changed
        if (!oldInfo || this.hasWorkspaceInfoChanged(oldInfo, this.currentWorkspaceInfo)) {
            this.emit('workspaceChanged', this.currentWorkspaceInfo);
            Logger.info('Workspace information updated', this.currentWorkspaceInfo);
        }
    }

    /**
     * Get the primary Flutter project path
     */
    public async getFlutterProjectPath(): Promise<string | undefined> {
        const info = await this.getWorkspaceInfo();
        return info.flutterProjectPath;
    }

    /**
     * Check if workspace has a Flutter project
     */
    public async hasFlutterProject(): Promise<boolean> {
        const info = await this.getWorkspaceInfo();
        return info.hasFlutterProject;
    }

    /**
     * Check if Flutter project has web support
     */
    public async hasWebSupport(): Promise<boolean> {
        const info = await this.getWorkspaceInfo();
        return info.hasWebSupport;
    }

    /**
     * Get all Flutter projects in workspace
     */
    public async getAllFlutterProjects(): Promise<string[]> {
        return this.projectDetector.getAllFlutterProjects();
    }

    /**
     * Create a new Flutter project in workspace
     */
    public async createFlutterProject(projectName: string): Promise<boolean> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            Logger.error('No workspace folder available for creating Flutter project');
            return false;
        }

        try {
            const workspacePath = workspaceFolders[0].uri.fsPath;
            const projectPath = path.join(workspacePath, projectName);

            // Check if directory already exists
            if (await FileUtils.directoryExists(projectPath)) {
                Logger.error(`Directory ${projectPath} already exists`);
                return false;
            }

            // This would typically use FlutterCliWrapper to create the project
            // For now, we'll just create the basic structure
            await FileUtils.createDirectory(projectPath);
            
            // Refresh workspace info after creation
            await this.refreshWorkspaceInfo();
            
            Logger.info(`Flutter project created at: ${projectPath}`);
            return true;

        } catch (error) {
            Logger.error('Failed to create Flutter project', error);
            return false;
        }
    }

    /**
     * Enable web support for the current Flutter project
     */
    public async enableWebSupport(): Promise<boolean> {
        const projectPath = await this.getFlutterProjectPath();
        if (!projectPath) {
            Logger.error('No Flutter project found to enable web support');
            return false;
        }

        try {
            // This would typically use FlutterCliWrapper to enable web support
            // For now, we'll just create the web directory structure
            const webDir = path.join(projectPath, 'web');
            await FileUtils.createDirectory(webDir);
            
            // Create basic index.html
            const indexHtml = `<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flutter_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <title>Flutter App</title>
  <link rel="manifest" href="manifest.json">
  <script>
    var serviceWorkerVersion = null;
    var scriptLoaded = false;
    function loadMainDartJs() {
      if (scriptLoaded) {
        return;
      }
      scriptLoaded = true;
      var scriptTag = document.createElement('script');
      scriptTag.src = 'main.dart.js';
      scriptTag.type = 'application/javascript';
      document.body.append(scriptTag);
    }

    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function () {
        navigator.serviceWorker.register('flutter_service_worker.js');
      });
    }
  </script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      }).then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        return appRunner.runApp();
      });
    });
  </script>
</body>
</html>`;

            await FileUtils.writeFile(path.join(webDir, 'index.html'), indexHtml);
            
            // Refresh workspace info
            await this.refreshWorkspaceInfo();
            
            Logger.info('Web support enabled for Flutter project');
            return true;

        } catch (error) {
            Logger.error('Failed to enable web support', error);
            return false;
        }
    }

    /**
     * Get workspace relative path
     */
    public getWorkspaceRelativePath(absolutePath: string): string | undefined {
        return FileUtils.getWorkspaceRelativePath(absolutePath);
    }

    /**
     * Resolve path relative to workspace
     */
    public resolveWorkspacePath(relativePath: string): string | undefined {
        return FileUtils.resolveWorkspacePath(relativePath);
    }

    /**
     * Watch for important file changes
     */
    public startFileWatching(): void {
        if (this.fileWatcher) {
            return;
        }

        // Watch for pubspec.yaml changes
        this.fileWatcher = vscode.workspace.createFileSystemWatcher(
            '**/pubspec.yaml',
            false, // Don't ignore creates
            false, // Don't ignore changes
            false  // Don't ignore deletes
        );

        this.fileWatcher.onDidCreate(uri => {
            Logger.debug(`pubspec.yaml created: ${uri.fsPath}`);
            this.refreshWorkspaceInfo();
        });

        this.fileWatcher.onDidChange(uri => {
            Logger.debug(`pubspec.yaml changed: ${uri.fsPath}`);
            this.refreshWorkspaceInfo();
        });

        this.fileWatcher.onDidDelete(uri => {
            Logger.debug(`pubspec.yaml deleted: ${uri.fsPath}`);
            this.refreshWorkspaceInfo();
        });

        this.disposables.push(this.fileWatcher);
        Logger.info('File watching started for pubspec.yaml files');
    }

    /**
     * Stop file watching
     */
    public stopFileWatching(): void {
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
            this.fileWatcher = undefined;
            Logger.info('File watching stopped');
        }
    }

    /**
     * Setup workspace event listeners
     */
    private setupWorkspaceWatchers(): void {
        // Watch for workspace folder changes
        const workspaceWatcher = vscode.workspace.onDidChangeWorkspaceFolders(event => {
            Logger.info('Workspace folders changed', {
                added: event.added.map(folder => folder.uri.fsPath),
                removed: event.removed.map(folder => folder.uri.fsPath)
            });
            this.refreshWorkspaceInfo();
        });

        this.disposables.push(workspaceWatcher);
    }

    /**
     * Initialize workspace on startup
     */
    private async initializeWorkspace(): Promise<void> {
        try {
            await this.refreshWorkspaceInfo();
            this.startFileWatching();
            Logger.info('Workspace manager initialized');
        } catch (error) {
            Logger.error('Failed to initialize workspace manager', error);
        }
    }

    /**
     * Check if workspace info has changed
     */
    private hasWorkspaceInfoChanged(oldInfo: WorkspaceInfo, newInfo: WorkspaceInfo): boolean {
        return (
            oldInfo.hasFlutterProject !== newInfo.hasFlutterProject ||
            oldInfo.flutterProjectPath !== newInfo.flutterProjectPath ||
            oldInfo.projectName !== newInfo.projectName ||
            oldInfo.hasWebSupport !== newInfo.hasWebSupport ||
            JSON.stringify(oldInfo.workspaceFolders) !== JSON.stringify(newInfo.workspaceFolders)
        );
    }

    /**
     * Dispose all resources
     */
    public dispose(): void {
        this.stopFileWatching();
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
        this.removeAllListeners();
        Logger.info('Workspace manager disposed');
    }
}
