"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorsHandler = void 0;
const Logger_1 = require("../utils/Logger");
/**
 * Handles CORS (Cross-Origin Resource Sharing) for the proxy server
 */
class CorsHandler {
    options;
    constructor(options) {
        this.options = {
            origins: ['*'],
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
            headers: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
            credentials: true,
            maxAge: 86400, // 24 hours
            ...options
        };
    }
    /**
     * Apply CORS headers to the response
     */
    applyCorsHeaders(req, res) {
        const origin = req.headers.origin;
        // Set Access-Control-Allow-Origin
        if (this.isOriginAllowed(origin)) {
            res.setHeader('Access-Control-Allow-Origin', origin || '*');
        }
        else if (this.options.origins.includes('*')) {
            res.setHeader('Access-Control-Allow-Origin', '*');
        }
        // Set Access-Control-Allow-Methods
        res.setHeader('Access-Control-Allow-Methods', this.options.methods.join(', '));
        // Set Access-Control-Allow-Headers
        const requestHeaders = req.headers['access-control-request-headers'];
        if (requestHeaders) {
            res.setHeader('Access-Control-Allow-Headers', requestHeaders);
        }
        else {
            res.setHeader('Access-Control-Allow-Headers', this.options.headers.join(', '));
        }
        // Set Access-Control-Allow-Credentials
        if (this.options.credentials) {
            res.setHeader('Access-Control-Allow-Credentials', 'true');
        }
        // Set Access-Control-Max-Age
        res.setHeader('Access-Control-Max-Age', this.options.maxAge.toString());
        // Additional headers for better compatibility
        res.setHeader('Access-Control-Expose-Headers', 'Content-Length, Content-Type');
        Logger_1.Logger.debug(`CORS headers applied for origin: ${origin || 'none'}`);
    }
    /**
     * Check if the origin is allowed
     */
    isOriginAllowed(origin) {
        if (!origin) {
            return true; // Allow requests without origin (e.g., same-origin)
        }
        if (this.options.origins.includes('*')) {
            return true;
        }
        return this.options.origins.some(allowedOrigin => {
            if (allowedOrigin === origin) {
                return true;
            }
            // Support wildcard subdomains (e.g., *.example.com)
            if (allowedOrigin.startsWith('*.')) {
                const domain = allowedOrigin.substring(2);
                return origin.endsWith('.' + domain) || origin === domain;
            }
            return false;
        });
    }
    /**
     * Configure CORS options
     */
    configure(options) {
        this.options = { ...this.options, ...options };
        Logger_1.Logger.info('CORS configuration updated', this.options);
    }
    /**
     * Add allowed origin
     */
    addOrigin(origin) {
        if (!this.options.origins.includes(origin)) {
            this.options.origins.push(origin);
            Logger_1.Logger.info(`Added CORS origin: ${origin}`);
        }
    }
    /**
     * Remove allowed origin
     */
    removeOrigin(origin) {
        const index = this.options.origins.indexOf(origin);
        if (index > -1) {
            this.options.origins.splice(index, 1);
            Logger_1.Logger.info(`Removed CORS origin: ${origin}`);
        }
    }
    /**
     * Add allowed method
     */
    addMethod(method) {
        const upperMethod = method.toUpperCase();
        if (!this.options.methods.includes(upperMethod)) {
            this.options.methods.push(upperMethod);
            Logger_1.Logger.info(`Added CORS method: ${upperMethod}`);
        }
    }
    /**
     * Remove allowed method
     */
    removeMethod(method) {
        const upperMethod = method.toUpperCase();
        const index = this.options.methods.indexOf(upperMethod);
        if (index > -1) {
            this.options.methods.splice(index, 1);
            Logger_1.Logger.info(`Removed CORS method: ${upperMethod}`);
        }
    }
    /**
     * Add allowed header
     */
    addHeader(header) {
        if (!this.options.headers.includes(header)) {
            this.options.headers.push(header);
            Logger_1.Logger.info(`Added CORS header: ${header}`);
        }
    }
    /**
     * Remove allowed header
     */
    removeHeader(header) {
        const index = this.options.headers.indexOf(header);
        if (index > -1) {
            this.options.headers.splice(index, 1);
            Logger_1.Logger.info(`Removed CORS header: ${header}`);
        }
    }
    /**
     * Get current CORS configuration
     */
    getConfiguration() {
        return { ...this.options };
    }
    /**
     * Reset CORS configuration to defaults
     */
    reset() {
        this.options = {
            origins: ['*'],
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
            headers: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
            credentials: true,
            maxAge: 86400
        };
        Logger_1.Logger.info('CORS configuration reset to defaults');
    }
    /**
     * Create a middleware function for Express-like servers
     */
    middleware() {
        return (req, res, next) => {
            this.applyCorsHeaders(req, res);
            // Handle preflight requests
            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }
            if (next) {
                next();
            }
        };
    }
}
exports.CorsHandler = CorsHandler;
//# sourceMappingURL=CorsHandler.js.map