import * as assert from 'assert';
import { ProxyServerManager } from '../../proxy/ProxyServerManager';
import { Logger } from '../../utils/Logger';

suite('ProxyServerManager Tests', () => {
    let proxyManager: ProxyServerManager;

    suiteSetup(async () => {
        // Initialize logger for tests
        const context = {
            subscriptions: []
        } as any;
        Logger.initialize(context);
    });

    setup(() => {
        proxyManager = new ProxyServerManager();
    });

    teardown(async () => {
        if (proxyManager && proxyManager.isRunning()) {
            await proxyManager.stop();
        }
        if (proxyManager) {
            proxyManager.dispose();
        }
    });

    test('Should create ProxyServerManager instance', () => {
        assert.ok(proxyManager);
        assert.strictEqual(proxyManager.isRunning(), false);
    });

    test('Should handle server state correctly', () => {
        assert.strictEqual(proxyManager.isRunning(), false);
        assert.strictEqual(proxyManager.getProxyUrl(), undefined);
        assert.strictEqual(proxyManager.getTargetUrl(), undefined);
    });

    test('Should set target URL', () => {
        const testUrl = 'http://localhost:8080';
        proxyManager.setTargetUrl(testUrl);
        assert.strictEqual(proxyManager.getTargetUrl(), testUrl);
    });

    test('Should emit events correctly', (done) => {
        let eventReceived = false;

        proxyManager.on('error', (error) => {
            eventReceived = true;
            assert.ok(error);
            done();
        });

        // Simulate an error event
        proxyManager.emit('error', new Error('Test proxy error'));

        // Fallback timeout
        setTimeout(() => {
            if (!eventReceived) {
                done(new Error('Event not received'));
            }
        }, 1000);
    });

    test('Should get stats', () => {
        const stats = proxyManager.getStats();
        assert.ok(stats);
        assert.strictEqual(typeof stats.isRunning, 'boolean');
        assert.strictEqual(typeof stats.requestCount, 'number');
        assert.strictEqual(typeof stats.errorCount, 'number');
    });

    test('Should dispose cleanly', () => {
        assert.doesNotThrow(() => {
            proxyManager.dispose();
        });
    });
});
