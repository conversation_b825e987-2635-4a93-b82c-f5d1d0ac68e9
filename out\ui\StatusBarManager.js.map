{"version": 3, "file": "StatusBarManager.js", "sourceRoot": "", "sources": ["../../src/ui/StatusBarManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,4CAAyC;AAIzC;;GAEG;AACH,MAAa,gBAAgB;IACjB,aAAa,CAAuB;IACpC,aAAa,GAAoB,SAAS,CAAC;IAEnD;QACI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAClD,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAC9B,GAAG,CACN,CAAC;IACN,CAAC;IAED;;OAEG;IACI,UAAU;QACb,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC1B,eAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,MAAuB;QACpC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,eAAM,CAAC,KAAK,CAAC,uBAAuB,MAAM,EAAE,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,eAAe;QACnB,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,KAAK,SAAS;gBACV,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,kBAAkB,CAAC;gBAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,uBAAuB,CAAC;gBACrD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,uBAAuB,CAAC;gBACrD,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;gBAC/C,MAAM;YAEV,KAAK,UAAU;gBACX,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,sCAAsC,CAAC;gBACjE,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,6BAA6B,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;gBACvC,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;gBAC/C,MAAM;YAEV,KAAK,SAAS;gBACV,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,0BAA0B,CAAC;gBACrD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,sBAAsB,CAAC;gBACpD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,sBAAsB,CAAC;gBACpD,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;gBAC/C,MAAM;YAEV,KAAK,UAAU;gBACX,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,sCAAsC,CAAC;gBACjE,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,6BAA6B,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;gBACvC,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;gBAC/C,MAAM;YAEV,KAAK,OAAO;gBACR,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,yBAAyB,CAAC;gBACpD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,yDAAyD,CAAC;gBACvF,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,yBAAyB,CAAC;gBACvD,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC;gBAC5F,MAAM;YAEV;gBACI,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,sBAAsB,CAAC;gBACjD,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,gBAAgB,CAAC;gBAC9C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;gBACvC,IAAI,CAAC,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;QACvD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,OAAe,EAAE,aAAqB,IAAI;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACnD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QAEnD,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,OAAO,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;QACrC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS,CAAC;QAEvC,UAAU,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,YAAY,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,eAAe,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,eAAe,CAAC;QACjD,CAAC,EAAE,UAAU,CAAC,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;IACjC,CAAC;CACJ;AA3HD,4CA2HC"}