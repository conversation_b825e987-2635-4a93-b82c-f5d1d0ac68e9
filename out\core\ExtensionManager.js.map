{"version": 3, "file": "ExtensionManager.js", "sourceRoot": "", "sources": ["../../src/core/ExtensionManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+DAA4D;AAC5D,8DAA2D;AAC3D,gEAA6D;AAC7D,8DAA2D;AAC3D,oEAAiE;AACjE,6DAA0D;AAC1D,mEAAgE;AAChE,yEAAsE;AACtE,yDAAsD;AACtD,oEAAiE;AACjE,4CAAyC;AAEzC;;GAEG;AACH,MAAa,gBAAgB;IAcE;IAbZ,WAAW,GAAwB,EAAE,CAAC;IAC/C,WAAW,GAAG,KAAK,CAAC;IACpB,cAAc,CAAiB;IAC/B,cAAc,CAAiB;IAC/B,eAAe,CAAkB;IACjC,cAAc,CAAiB;IAC/B,kBAAkB,CAAqB;IACvC,gBAAgB,CAAmB;IACnC,mBAAmB,CAAsB;IACzC,oBAAoB,CAAuB;IAC3C,gBAAgB,CAAmB;IACnC,kBAAkB,CAAqB;IAE/C,YAA6B,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAC3D,2BAA2B;QAC3B,IAAI,CAAC,oBAAoB,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACvD,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,kBAAkB,GAAG,uCAAkB,CAAC,WAAW,EAAE,CAAC;QAC3D,IAAI,CAAC,mBAAmB,GAAG,yCAAmB,CAAC,WAAW,EAAE,CAAC;QAE7D,0CAA0C;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;QAEnD,2BAA2B;QAC3B,IAAI,CAAC,eAAe,GAAG,IAAI,iCAAe,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAC1E,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC3E,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAE/C,mDAAmD;QACnD,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CACtC,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,gBAAgB,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAEnD,+BAA+B;QAC/B,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;QAE1C,4BAA4B;QAC5B,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAC7B,MAAM,CAAC,MAAM,CAAC,2BAA2B,CACvC,iCAAe,CAAC,QAAQ,EACxB,IAAI,CAAC,eAAe,CACrB,CACF,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,uCAAuC;QACvC,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,kBAAkB,CACxB,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;QAEnC,wBAAwB;QACxB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,6BAA6B;QAC7B,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,yBAAyB;QACzB,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,EAAE,CAAC,GAAW,EAAE,EAAE;YACvD,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC3C,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC5C,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YAC/C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzD,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC/C,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAChD,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,QAAgB,EAAE,EAAE;YAC/D,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAC/C,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACnD,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,aAAkB,EAAE,EAAE;YAClE,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACxD,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,MAAgB,EAAE,EAAE;YAClE,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAErE,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBACrC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,CAAC;gBAC/C,OAAO;YACT,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;gBACjC,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,EAAE,CAAC;gBACjD,OAAO;YACT,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,aAAa,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,aAAkB;QAC9C,yCAAyC;QACzC,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;YACrC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QAED,wDAAwD;QACxD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,IAAI,aAAa,CAAC,kBAAkB,EAAE,CAAC;YACxE,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC/B,eAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAE7D,yBAAyB;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,EAAE,CAAC;QACjE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,OAAO,CAAC,CAAC,KAAa,EAAE,EAAE;gBAC/B,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YACpE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,4DAA4D;QAC5D,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB;QAChC,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,EAAE,CAAC;gBACpC,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YACjD,CAAC;YACD,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACjD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACvD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC3B,CAAC;CACF;AAtOD,4CAsOC"}