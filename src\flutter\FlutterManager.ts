import * as vscode from 'vscode';
import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import { FlutterProjectDetector } from './FlutterProjectDetector';
import { FlutterProcessHandler } from './FlutterProcessHandler';
import { HotReloadHandler } from './HotReloadHandler';
import { Logger } from '../utils/Logger';

/**
 * Manages Flutter CLI processes and integration
 */
export class FlutterManager extends EventEmitter implements vscode.Disposable {
    private flutterProcess: ChildProcess | undefined;
    private processHandler: FlutterProcessHandler;
    private hotReloadHandler: HotReloadHandler;
    private projectDetector: FlutterProjectDetector;
    private isFlutterRunning = false;
    private currentServerUrl: string | undefined;

    constructor() {
        super();
        this.processHandler = new FlutterProcessHandler();
        this.hotReloadHandler = new HotReloadHandler();
        this.projectDetector = new FlutterProjectDetector();
        
        this.setupEventListeners();
    }

    /**
     * Start the Flutter development server
     */
    public async startFlutterProcess(): Promise<void> {
        if (this.isFlutterRunning) {
            Logger.warn('Flutter process is already running');
            return;
        }

        try {
            // Detect Flutter project
            const projectPath = await this.projectDetector.findFlutterProject();
            if (!projectPath) {
                throw new Error('No Flutter project found in workspace');
            }

            Logger.info(`Starting Flutter process in: ${projectPath}`);

            // Start Flutter web server
            const args = [
                'run',
                '-d', 'web-server',
                '--web-port=0', // Use random available port
                '--web-hostname=localhost',
                '--no-sound-null-safety' // For compatibility
            ];

            this.flutterProcess = spawn('flutter', args, {
                cwd: projectPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });

            this.processHandler.attachToProcess(this.flutterProcess);
            this.hotReloadHandler.attachToProcess(this.flutterProcess);

            this.isFlutterRunning = true;
            Logger.info('Flutter process started successfully');

        } catch (error) {
            this.isFlutterRunning = false;
            Logger.error('Failed to start Flutter process', error);
            throw error;
        }
    }

    /**
     * Stop the Flutter development server
     */
    public async stopFlutterProcess(): Promise<void> {
        if (!this.isFlutterRunning || !this.flutterProcess) {
            Logger.warn('Flutter process is not running');
            return;
        }

        try {
            Logger.info('Stopping Flutter process...');
            
            // Send quit command to Flutter
            if (this.flutterProcess.stdin && !this.flutterProcess.stdin.destroyed) {
                this.flutterProcess.stdin.write('q\n');
            }

            // Wait for graceful shutdown or force kill after timeout
            await this.waitForProcessExit(5000);

            this.isFlutterRunning = false;
            this.currentServerUrl = undefined;
            this.flutterProcess = undefined;

            this.emit('processStopped');
            Logger.info('Flutter process stopped successfully');

        } catch (error) {
            Logger.error('Failed to stop Flutter process gracefully', error);
            this.forceKillProcess();
        }
    }

    /**
     * Trigger hot reload
     */
    public async hotReload(): Promise<void> {
        if (!this.isFlutterRunning || !this.flutterProcess) {
            throw new Error('Flutter process is not running');
        }

        return this.hotReloadHandler.triggerHotReload();
    }

    /**
     * Trigger hot restart
     */
    public async hotRestart(): Promise<void> {
        if (!this.isFlutterRunning || !this.flutterProcess) {
            throw new Error('Flutter process is not running');
        }

        return this.hotReloadHandler.triggerHotRestart();
    }

    /**
     * Check if Flutter process is running
     */
    public isRunning(): boolean {
        return this.isFlutterRunning;
    }

    /**
     * Get the current server URL
     */
    public getServerUrl(): string | undefined {
        return this.currentServerUrl;
    }

    /**
     * Setup event listeners for process handlers
     */
    private setupEventListeners(): void {
        this.processHandler.on('serverStarted', (url: string) => {
            this.currentServerUrl = url;
            this.emit('processStarted', url);
            Logger.info(`Flutter server started at: ${url}`);
        });

        this.processHandler.on('error', (error: Error) => {
            this.emit('error', error);
            Logger.error('Flutter process error', error);
        });

        this.processHandler.on('exit', (code: number) => {
            this.isFlutterRunning = false;
            this.currentServerUrl = undefined;
            this.flutterProcess = undefined;
            this.emit('processStopped');
            Logger.info(`Flutter process exited with code: ${code}`);
        });

        this.hotReloadHandler.on('hotReloadComplete', () => {
            this.emit('hotReloadComplete');
            Logger.info('Hot reload completed');
        });

        this.hotReloadHandler.on('hotRestartComplete', () => {
            this.emit('hotRestartComplete');
            Logger.info('Hot restart completed');
        });
    }

    /**
     * Wait for process to exit gracefully
     */
    private waitForProcessExit(timeoutMs: number): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.flutterProcess) {
                resolve();
                return;
            }

            const timeout = setTimeout(() => {
                reject(new Error('Process exit timeout'));
            }, timeoutMs);

            this.flutterProcess.on('exit', () => {
                clearTimeout(timeout);
                resolve();
            });
        });
    }

    /**
     * Force kill the Flutter process
     */
    private forceKillProcess(): void {
        if (this.flutterProcess && !this.flutterProcess.killed) {
            Logger.warn('Force killing Flutter process');
            this.flutterProcess.kill('SIGKILL');
            this.isFlutterRunning = false;
            this.currentServerUrl = undefined;
            this.flutterProcess = undefined;
            this.emit('processStopped');
        }
    }

    /**
     * Dispose all resources
     */
    public dispose(): void {
        this.stopFlutterProcess().catch(error => {
            Logger.error('Error during Flutter manager disposal', error);
        });
        
        this.processHandler.dispose();
        this.hotReloadHandler.dispose();
        this.removeAllListeners();
    }
}
