"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlutterProcessHandler = void 0;
const events_1 = require("events");
const Logger_1 = require("../utils/Logger");
/**
 * Handles Flutter CLI process output and events
 */
class FlutterProcessHandler extends events_1.EventEmitter {
    process;
    outputBuffer = '';
    /**
     * Attach to a Flutter process
     */
    attachToProcess(process) {
        this.process = process;
        this.setupProcessListeners();
    }
    /**
     * Setup listeners for the Flutter process
     */
    setupProcessListeners() {
        if (!this.process) {
            return;
        }
        // Handle stdout
        this.process.stdout?.on('data', (data) => {
            const output = data.toString();
            this.outputBuffer += output;
            this.processOutput(output);
        });
        // Handle stderr
        this.process.stderr?.on('data', (data) => {
            const error = data.toString();
            Logger_1.Logger.error('Flutter stderr:', error);
            this.processError(error);
        });
        // Handle process exit
        this.process.on('exit', (code, signal) => {
            Logger_1.Logger.info(`Flutter process exited with code: ${code}, signal: ${signal}`);
            this.emit('exit', code || 0);
        });
        // Handle process error
        this.process.on('error', (error) => {
            Logger_1.Logger.error('Flutter process error:', error);
            this.emit('error', error);
        });
    }
    /**
     * Process Flutter CLI output
     */
    processOutput(output) {
        const lines = output.split('\n');
        for (const line of lines) {
            this.processLine(line.trim());
        }
    }
    /**
     * Process individual output line
     */
    processLine(line) {
        if (!line) {
            return;
        }
        Logger_1.Logger.debug(`Flutter output: ${line}`);
        // Check for server URL
        const urlMatch = line.match(/A web server for this build is available at:\s*(https?:\/\/[^\s]+)/);
        if (urlMatch) {
            const serverUrl = urlMatch[1];
            Logger_1.Logger.info(`Flutter web server detected: ${serverUrl}`);
            this.emit('serverStarted', serverUrl);
            return;
        }
        // Alternative URL pattern
        const altUrlMatch = line.match(/(https?:\/\/localhost:\d+)/);
        if (altUrlMatch && line.includes('web server')) {
            const serverUrl = altUrlMatch[1];
            Logger_1.Logger.info(`Flutter web server detected (alt): ${serverUrl}`);
            this.emit('serverStarted', serverUrl);
            return;
        }
        // Check for hot reload completion
        if (line.includes('Reloaded') && line.includes('ms')) {
            this.emit('hotReloadComplete');
            return;
        }
        // Check for hot restart completion
        if (line.includes('Restarted application')) {
            this.emit('hotRestartComplete');
            return;
        }
        // Check for compilation errors
        if (line.includes('Error:') || line.includes('error:')) {
            this.emit('compilationError', line);
            return;
        }
        // Check for warnings
        if (line.includes('Warning:') || line.includes('warning:')) {
            this.emit('warning', line);
            return;
        }
        // Check for build completion
        if (line.includes('Built') && line.includes('web')) {
            this.emit('buildComplete');
            return;
        }
        // Check for ready state
        if (line.includes('Flutter run key commands')) {
            this.emit('ready');
            return;
        }
        // Check for dependency resolution
        if (line.includes('Running "flutter pub get"')) {
            this.emit('pubGetStarted');
            return;
        }
        if (line.includes('Resolving dependencies...')) {
            this.emit('resolvingDependencies');
            return;
        }
        // Check for analysis issues
        if (line.includes('issues found')) {
            this.emit('analysisComplete', line);
            return;
        }
    }
    /**
     * Process Flutter CLI errors
     */
    processError(error) {
        const lines = error.split('\n');
        for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) {
                continue;
            }
            // Check for specific error patterns
            if (trimmedLine.includes('No connected devices')) {
                this.emit('error', new Error('No connected devices found'));
                return;
            }
            if (trimmedLine.includes('Unable to locate a development device')) {
                this.emit('error', new Error('Unable to locate a development device'));
                return;
            }
            if (trimmedLine.includes('Flutter SDK not found')) {
                this.emit('error', new Error('Flutter SDK not found'));
                return;
            }
            if (trimmedLine.includes('pubspec.yaml')) {
                this.emit('error', new Error('pubspec.yaml error: ' + trimmedLine));
                return;
            }
            // Generic error
            this.emit('error', new Error(trimmedLine));
        }
    }
    /**
     * Send input to the Flutter process
     */
    sendInput(input) {
        if (!this.process || !this.process.stdin || this.process.stdin.destroyed) {
            Logger_1.Logger.warn('Cannot send input: Flutter process stdin not available');
            return false;
        }
        try {
            this.process.stdin.write(input + '\n');
            Logger_1.Logger.debug(`Sent input to Flutter process: ${input}`);
            return true;
        }
        catch (error) {
            Logger_1.Logger.error('Error sending input to Flutter process', error);
            return false;
        }
    }
    /**
     * Get the current output buffer
     */
    getOutputBuffer() {
        return this.outputBuffer;
    }
    /**
     * Clear the output buffer
     */
    clearOutputBuffer() {
        this.outputBuffer = '';
    }
    /**
     * Check if process is running
     */
    isProcessRunning() {
        return this.process !== undefined && !this.process.killed;
    }
    /**
     * Dispose the handler
     */
    dispose() {
        this.removeAllListeners();
        this.process = undefined;
        this.outputBuffer = '';
    }
}
exports.FlutterProcessHandler = FlutterProcessHandler;
//# sourceMappingURL=FlutterProcessHandler.js.map