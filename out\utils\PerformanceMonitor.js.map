{"version": 3, "file": "PerformanceMonitor.js", "sourceRoot": "", "sources": ["../../src/utils/PerformanceMonitor.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,qCAAkC;AAkBlC;;GAEG;AACH,MAAa,kBAAmB,SAAQ,qBAAY;IACxC,MAAM,CAAC,QAAQ,CAAqB;IACpC,YAAY,GAAG,KAAK,CAAC;IACrB,kBAAkB,CAA6B;IAC/C,gBAAgB,GAAkC,IAAI,GAAG,EAAE,CAAC;IAC5D,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IACjD,cAAc,GAAyB,EAAE,CAAC;IACjC,cAAc,GAAG,GAAG,CAAC;IAEtC;QACI,KAAK,EAAE,CAAC;IACZ,CAAC;IAEM,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YAC/B,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,aAAqB,IAAI;QAC5C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1B,CAAC,EAAE,UAAU,CAAC,CAAC;QAEf,eAAM,CAAC,IAAI,CAAC,6CAA6C,UAAU,KAAK,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,cAAc;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACxC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,WAAmB;QACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,WAAmB,EAAE,aAAqB,EAAE,UAAmB,IAAI;QACnF,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,eAAM,CAAC,IAAI,CAAC,sCAAsC,WAAW,EAAE,CAAC,CAAC;YACjE,OAAO,CAAC,CAAC;QACb,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEzC,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE9D,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACtB,WAAmB,EACnB,aAAqB,EACrB,SAA2B;QAE3B,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEjC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,OAAO;YACH,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACxB,CAAC;IACN,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,aAAsB;QAC7C,IAAI,aAAa,EAAE,CAAC;YAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;QACvG,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QAE9D,OAAO;YACH,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC7D,SAAS,EAAE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClF,aAAa,EAAE,IAAI,CAAC,aAAa;aACpC,CAAC,CAAC;YACH,MAAM,EAAE;gBACJ,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;gBACnF,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;gBACrF,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;aACtF;YACD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;SAC5C,CAAC;IACN,CAAC;IAED;;OAEG;IACI,sBAAsB;QACzB,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEhD,4CAA4C;QAC5C,MAAM,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;QACrE,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAClE,CAAC;QAED,8BAA8B;QAC9B,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAClD,IAAI,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;gBAC9B,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;gBAClE,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;oBACjB,MAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACzE,CAAC;YACL,CAAC;YAED,kDAAkD;YAClD,IAAI,OAAO,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;YAChG,CAAC;QACL,CAAC;QAED,OAAO;YACH,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACT,CAAC;IACN,CAAC;IAED;;OAEG;IACI,YAAY;QACf,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,aAAa;QAChB,MAAM,IAAI,GAAG;YACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACrC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SAC1D,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,qBAAqB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE7C,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,cAAc,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM;YACzC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;SACxB,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc;QAClB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzC,iBAAiB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElC,qBAAqB;QACrB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAChC,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE9B,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7C,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,aAAqB,EAAE,QAAgB,EAAE,OAAgB;QACpF,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,CAAC,cAAc,EAAE,CAAC;QACzB,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;QAClC,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC;QACzE,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEnC,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,CAAC,MAAM,EAAE,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,IAAY;QAC5C,OAAO;YACH,IAAI;YACJ,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,CAAC;SACZ,CAAC;IACN,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;CACJ;AA5SD,gDA4SC"}