{"version": 3, "file": "PerformanceMonitor.js", "sourceRoot": "", "sources": ["../../src/utils/PerformanceMonitor.ts"], "names": [], "mappings": ";;;AAAA,mCAAsC;AACtC,qCAAkC;AAkBlC;;GAEG;AACH,MAAa,kBAAmB,SAAQ,qBAAY;IAC1C,MAAM,CAAC,QAAQ,CAAqB;IACpC,YAAY,GAAG,KAAK,CAAC;IACrB,kBAAkB,CAA6B;IAC/C,gBAAgB,GAAkC,IAAI,GAAG,EAAE,CAAC;IAC5D,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;IACjD,cAAc,GAAyB,EAAE,CAAC;IACjC,cAAc,GAAG,GAAG,CAAC;IAEtC;QACE,KAAK,EAAE,CAAC;IACV,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,aAAqB,KAAK;QAC/C,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,GAAG,EAAE;YACzC,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAAE,UAAU,CAAC,CAAC;QAEf,eAAM,CAAC,IAAI,CAAC,6CAA6C,UAAU,KAAK,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC;QACtC,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,WAAmB;QACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,YAAY,CACjB,WAAmB,EACnB,aAAqB,EACrB,UAAmB,IAAI;QAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACxD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,sCAAsC,WAAW,EAAE,CAAC,CAAC;YACjE,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAEzC,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAE9D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACxB,WAAmB,EACnB,aAAqB,EACrB,SAA2B;QAE3B,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC;YACpD,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO;YACL,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;YAClC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;YACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,mBAAmB,CACxB,aAAsB;QAEtB,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,CACL,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC;gBACxC,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAChD,CAAC;QACJ,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,iBAAiB;QACtB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,qBAAqB;QAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;QAE9D,OAAO;YACL,OAAO,EAAE,cAAc;YACvB,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACpC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC7D,SAAS,EACP,IAAI,CAAC,cAAc,GAAG,CAAC;oBACrB,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,GAAG;oBAC3C,CAAC,CAAC,CAAC;gBACP,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC,CAAC;YACH,MAAM,EAAE;gBACN,QAAQ,EACN,IAAI,CAAC,KAAK,CACR,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAC1D,GAAG,GAAG;gBACT,SAAS,EACP,IAAI,CAAC,KAAK,CACR,CAAC,cAAc,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAC3D,GAAG,GAAG;gBACT,QAAQ,EACN,IAAI,CAAC,KAAK,CACR,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAC1D,GAAG,GAAG;aACV;YACD,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEhD,4CAA4C;QAC5C,MAAM,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;QACrE,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAChE,CAAC;QAED,8BAA8B;QAC9B,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpD,IAAI,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE,CAAC;gBAChC,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,GAAG,GAAG,CAAC;gBAClE,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC,sBAAsB,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;YAED,kDAAkD;YAClD,IAAI,OAAO,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC;gBACnC,MAAM,CAAC,IAAI,CACT,sBAAsB,IAAI,KAAK,IAAI,CAAC,KAAK,CACvC,OAAO,CAAC,eAAe,CACxB,YAAY,CACd,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,aAAa;QAClB,MAAM,IAAI,GAAG;YACX,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACrC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SACxD,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,qBAAqB;QAC1B,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE7C,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACjC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,cAAc,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM;YACzC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzC,iBAAiB;QACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAElC,qBAAqB;QACrB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACrD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC9B,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE9B,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC7C,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAC5B,aAAqB,EACrB,QAAgB,EAChB,OAAgB;QAEhB,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAEvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAC1D,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,CAAC,cAAc,EAAE,CAAC;QACzB,OAAO,CAAC,aAAa,IAAI,QAAQ,CAAC;QAClC,OAAO,CAAC,eAAe,GAAG,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC;QACzE,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEnC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,IAAY;QAC9C,OAAO;YACL,IAAI;YACJ,cAAc,EAAE,CAAC;YACjB,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,MAAM,EAAE,CAAC;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;CACF;AAzUD,gDAyUC"}