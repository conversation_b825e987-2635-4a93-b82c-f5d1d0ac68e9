import { EventEmitter } from 'events';
import { ChildProcess } from 'child_process';
import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';

/**
 * Handles Flutter hot reload and hot restart functionality
 */
export class HotReloadHandler extends EventEmitter {
    private process: ChildProcess | undefined;
    private fileWatcher: vscode.FileSystemWatcher | undefined;
    private hotReloadTimeout: NodeJS.Timeout | undefined;
    private isHotReloadEnabled = true;
    private lastReloadTime = 0;
    private readonly reloadDebounceMs = 1000; // Debounce hot reload requests

    /**
     * Attach to a Flutter process
     */
    public attachToProcess(process: ChildProcess): void {
        this.process = process;
        this.setupFileWatcher();
    }

    /**
     * Setup file system watcher for automatic hot reload
     */
    private setupFileWatcher(): void {
        // Watch for Dart file changes
        this.fileWatcher = vscode.workspace.createFileSystemWatcher(
            '**/*.dart',
            false, // Don't ignore creates
            false, // Don't ignore changes
            true   // Ignore deletes (they require hot restart)
        );

        this.fileWatcher.onDidCreate(uri => {
            Logger.debug(`Dart file created: ${uri.fsPath}`);
            this.scheduleHotReload();
        });

        this.fileWatcher.onDidChange(uri => {
            Logger.debug(`Dart file changed: ${uri.fsPath}`);
            this.scheduleHotReload();
        });

        Logger.info('File watcher setup for hot reload');
    }

    /**
     * Schedule a hot reload with debouncing
     */
    private scheduleHotReload(): void {
        if (!this.isHotReloadEnabled) {
            return;
        }

        // Clear existing timeout
        if (this.hotReloadTimeout) {
            clearTimeout(this.hotReloadTimeout);
        }

        // Schedule new hot reload
        this.hotReloadTimeout = setTimeout(() => {
            this.triggerHotReload().catch(error => {
                Logger.error('Scheduled hot reload failed', error);
            });
        }, this.reloadDebounceMs);
    }

    /**
     * Trigger hot reload manually
     */
    public async triggerHotReload(): Promise<void> {
        if (!this.process || !this.process.stdin || this.process.stdin.destroyed) {
            throw new Error('Flutter process is not available for hot reload');
        }

        const now = Date.now();
        if (now - this.lastReloadTime < this.reloadDebounceMs) {
            Logger.debug('Hot reload skipped due to debouncing');
            return;
        }

        try {
            Logger.info('Triggering hot reload...');
            this.lastReloadTime = now;

            // Send 'r' command to Flutter CLI for hot reload
            this.process.stdin.write('r\n');
            
            // Wait for hot reload completion or timeout
            await this.waitForHotReloadCompletion();
            
            this.emit('hotReloadComplete');
            Logger.info('Hot reload completed successfully');

        } catch (error) {
            Logger.error('Hot reload failed', error);
            throw error;
        }
    }

    /**
     * Trigger hot restart manually
     */
    public async triggerHotRestart(): Promise<void> {
        if (!this.process || !this.process.stdin || this.process.stdin.destroyed) {
            throw new Error('Flutter process is not available for hot restart');
        }

        try {
            Logger.info('Triggering hot restart...');

            // Send 'R' command to Flutter CLI for hot restart
            this.process.stdin.write('R\n');
            
            // Wait for hot restart completion or timeout
            await this.waitForHotRestartCompletion();
            
            this.emit('hotRestartComplete');
            Logger.info('Hot restart completed successfully');

        } catch (error) {
            Logger.error('Hot restart failed', error);
            throw error;
        }
    }

    /**
     * Wait for hot reload completion
     */
    private waitForHotReloadCompletion(timeoutMs: number = 10000): Promise<void> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Hot reload timeout'));
            }, timeoutMs);

            const onComplete = () => {
                clearTimeout(timeout);
                this.removeListener('hotReloadComplete', onComplete);
                this.removeListener('error', onError);
                resolve();
            };

            const onError = (error: Error) => {
                clearTimeout(timeout);
                this.removeListener('hotReloadComplete', onComplete);
                this.removeListener('error', onError);
                reject(error);
            };

            this.once('hotReloadComplete', onComplete);
            this.once('error', onError);
        });
    }

    /**
     * Wait for hot restart completion
     */
    private waitForHotRestartCompletion(timeoutMs: number = 15000): Promise<void> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Hot restart timeout'));
            }, timeoutMs);

            const onComplete = () => {
                clearTimeout(timeout);
                this.removeListener('hotRestartComplete', onComplete);
                this.removeListener('error', onError);
                resolve();
            };

            const onError = (error: Error) => {
                clearTimeout(timeout);
                this.removeListener('hotRestartComplete', onComplete);
                this.removeListener('error', onError);
                reject(error);
            };

            this.once('hotRestartComplete', onComplete);
            this.once('error', onError);
        });
    }

    /**
     * Enable or disable hot reload
     */
    public setHotReloadEnabled(enabled: boolean): void {
        this.isHotReloadEnabled = enabled;
        Logger.info(`Hot reload ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Check if hot reload is enabled
     */
    public isEnabled(): boolean {
        return this.isHotReloadEnabled;
    }

    /**
     * Get hot reload statistics
     */
    public getStats(): any {
        return {
            enabled: this.isHotReloadEnabled,
            lastReloadTime: this.lastReloadTime,
            debounceMs: this.reloadDebounceMs
        };
    }

    /**
     * Clear any pending hot reload
     */
    public clearPendingReload(): void {
        if (this.hotReloadTimeout) {
            clearTimeout(this.hotReloadTimeout);
            this.hotReloadTimeout = undefined;
        }
    }

    /**
     * Dispose the handler
     */
    public dispose(): void {
        this.clearPendingReload();
        
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
            this.fileWatcher = undefined;
        }

        this.removeAllListeners();
        this.process = undefined;
    }
}
