"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const vscode = __importStar(require("vscode"));
const events_1 = require("events");
const Logger_1 = require("../utils/Logger");
/**
 * Manages extension configuration and settings
 */
class ConfigurationManager extends events_1.EventEmitter {
    configSection = 'syncview';
    disposables = [];
    currentConfig;
    constructor() {
        super();
        this.currentConfig = this.loadConfiguration();
        this.setupConfigurationWatcher();
    }
    /**
     * Get the current configuration
     */
    getConfiguration() {
        return { ...this.currentConfig };
    }
    /**
     * Get a specific configuration value
     */
    get(key, defaultValue) {
        const config = vscode.workspace.getConfiguration(this.configSection);
        return config.get(key, defaultValue);
    }
    /**
     * Update a configuration value
     */
    async update(key, value, target) {
        const config = vscode.workspace.getConfiguration(this.configSection);
        await config.update(key, value, target);
        Logger_1.Logger.info(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
    }
    /**
     * Get Flutter SDK path
     */
    getFlutterSdkPath() {
        return this.currentConfig.flutter.sdkPath || this.detectFlutterSdkPath();
    }
    /**
     * Get Flutter web port
     */
    getFlutterWebPort() {
        return this.currentConfig.flutter.webPort;
    }
    /**
     * Check if hot reload on save is enabled
     */
    isHotReloadOnSaveEnabled() {
        return this.currentConfig.flutter.hotReloadOnSave;
    }
    /**
     * Check if debug mode is enabled
     */
    isDebugModeEnabled() {
        return this.currentConfig.flutter.debugMode;
    }
    /**
     * Get additional Flutter arguments
     */
    getFlutterAdditionalArgs() {
        return this.currentConfig.flutter.additionalArgs;
    }
    /**
     * Get proxy port
     */
    getProxyPort() {
        return this.currentConfig.proxy.port;
    }
    /**
     * Check if CORS is enabled
     */
    isCorsEnabled() {
        return this.currentConfig.proxy.corsEnabled;
    }
    /**
     * Get CORS origins
     */
    getCorsOrigins() {
        return this.currentConfig.proxy.corsOrigins;
    }
    /**
     * Check if status bar should be shown
     */
    shouldShowStatusBar() {
        return this.currentConfig.ui.showStatusBar;
    }
    /**
     * Check if preview should auto-show
     */
    shouldAutoShowPreview() {
        return this.currentConfig.ui.autoShowPreview;
    }
    /**
     * Get preview position
     */
    getPreviewPosition() {
        return this.currentConfig.ui.previewPosition;
    }
    /**
     * Get device frame setting
     */
    getDeviceFrame() {
        return this.currentConfig.ui.deviceFrame;
    }
    /**
     * Check if performance throttling is enabled
     */
    isThrottlingEnabled() {
        return this.currentConfig.performance.enableThrottling;
    }
    /**
     * Get maximum frame rate
     */
    getMaxFrameRate() {
        return this.currentConfig.performance.maxFrameRate;
    }
    /**
     * Get memory limit
     */
    getMemoryLimit() {
        return this.currentConfig.performance.memoryLimit;
    }
    /**
     * Load configuration from VS Code settings
     */
    loadConfiguration() {
        const config = vscode.workspace.getConfiguration(this.configSection);
        return {
            flutter: {
                sdkPath: config.get('flutter.sdkPath'),
                webPort: config.get('flutter.webPort'),
                hotReloadOnSave: config.get('flutter.hotReloadOnSave', true),
                debugMode: config.get('flutter.debugMode', false),
                additionalArgs: config.get('flutter.additionalArgs', [])
            },
            proxy: {
                port: config.get('proxy.port'),
                corsEnabled: config.get('proxy.corsEnabled', true),
                corsOrigins: config.get('proxy.corsOrigins', ['*'])
            },
            ui: {
                showStatusBar: config.get('ui.showStatusBar', true),
                autoShowPreview: config.get('ui.autoShowPreview', true),
                previewPosition: config.get('ui.previewPosition', 'beside'),
                deviceFrame: config.get('ui.deviceFrame', 'none')
            },
            performance: {
                enableThrottling: config.get('performance.enableThrottling', false),
                maxFrameRate: config.get('performance.maxFrameRate', 60),
                memoryLimit: config.get('performance.memoryLimit', 512)
            }
        };
    }
    /**
     * Setup configuration change watcher
     */
    setupConfigurationWatcher() {
        const watcher = vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration(this.configSection)) {
                const oldConfig = this.currentConfig;
                this.currentConfig = this.loadConfiguration();
                this.emit('configurationChanged', {
                    oldConfig,
                    newConfig: this.currentConfig
                });
                Logger_1.Logger.info('Configuration changed');
            }
        });
        this.disposables.push(watcher);
    }
    /**
     * Detect Flutter SDK path from environment
     */
    detectFlutterSdkPath() {
        // Try to detect Flutter SDK from PATH or common locations
        const possiblePaths = [
            process.env.FLUTTER_ROOT,
            process.env.FLUTTER_SDK,
            '/usr/local/flutter',
            '/opt/flutter',
            'C:\\flutter',
            'C:\\tools\\flutter'
        ];
        // This is a simplified detection - in a real implementation,
        // you would check if the flutter binary exists at these paths
        return possiblePaths.find(path => path && path.length > 0);
    }
    /**
     * Validate configuration
     */
    validateConfiguration() {
        const errors = [];
        // Validate Flutter configuration
        if (this.currentConfig.flutter.webPort &&
            (this.currentConfig.flutter.webPort < 1024 || this.currentConfig.flutter.webPort > 65535)) {
            errors.push('Flutter web port must be between 1024 and 65535');
        }
        // Validate proxy configuration
        if (this.currentConfig.proxy.port &&
            (this.currentConfig.proxy.port < 1024 || this.currentConfig.proxy.port > 65535)) {
            errors.push('Proxy port must be between 1024 and 65535');
        }
        // Validate performance configuration
        if (this.currentConfig.performance.maxFrameRate < 1 || this.currentConfig.performance.maxFrameRate > 120) {
            errors.push('Max frame rate must be between 1 and 120');
        }
        if (this.currentConfig.performance.memoryLimit < 128 || this.currentConfig.performance.memoryLimit > 4096) {
            errors.push('Memory limit must be between 128 and 4096 MB');
        }
        return errors;
    }
    /**
     * Reset configuration to defaults
     */
    async resetToDefaults() {
        const config = vscode.workspace.getConfiguration(this.configSection);
        const keys = [
            'flutter.sdkPath',
            'flutter.webPort',
            'flutter.hotReloadOnSave',
            'flutter.debugMode',
            'flutter.additionalArgs',
            'proxy.port',
            'proxy.corsEnabled',
            'proxy.corsOrigins',
            'ui.showStatusBar',
            'ui.autoShowPreview',
            'ui.previewPosition',
            'ui.deviceFrame',
            'performance.enableThrottling',
            'performance.maxFrameRate',
            'performance.memoryLimit'
        ];
        for (const key of keys) {
            await config.update(key, undefined, vscode.ConfigurationTarget.Global);
        }
        Logger_1.Logger.info('Configuration reset to defaults');
    }
    /**
     * Dispose all resources
     */
    dispose() {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
        this.removeAllListeners();
    }
}
exports.ConfigurationManager = ConfigurationManager;
//# sourceMappingURL=ConfigurationManager.js.map