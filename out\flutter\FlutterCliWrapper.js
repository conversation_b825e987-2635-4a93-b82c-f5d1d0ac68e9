"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlutterCliWrapper = void 0;
const events_1 = require("events");
const ProcessUtils_1 = require("../utils/ProcessUtils");
const Logger_1 = require("../utils/Logger");
/**
 * Wrapper for Flutter CLI commands with proper error handling and output parsing
 */
class FlutterCliWrapper extends events_1.EventEmitter {
    options;
    flutterCommand;
    constructor(options) {
        super();
        this.options = options;
        this.flutterCommand = this.resolveFlutterCommand();
    }
    /**
     * Check if Flutter is available
     */
    async isFlutterAvailable() {
        try {
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["--version"], {
                timeout: 10000,
                cwd: this.options.workingDirectory,
            });
            return result.exitCode === 0;
        }
        catch (error) {
            Logger_1.Logger.error("Flutter availability check failed", error);
            return false;
        }
    }
    /**
     * Get Flutter version information
     */
    async getFlutterVersion() {
        try {
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["--version"], {
                timeout: 10000,
                cwd: this.options.workingDirectory,
            });
            if (result.exitCode === 0) {
                const versionLine = result.stdout.split("\n")[0];
                return versionLine.trim();
            }
            return undefined;
        }
        catch (error) {
            Logger_1.Logger.error("Failed to get Flutter version", error);
            return undefined;
        }
    }
    /**
     * Get available Flutter devices
     */
    async getDevices() {
        try {
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["devices", "--machine"], {
                timeout: 15000,
                cwd: this.options.workingDirectory,
            });
            if (result.exitCode === 0) {
                try {
                    const devices = JSON.parse(result.stdout);
                    return devices.map((device) => ({
                        id: device.id,
                        name: device.name,
                        platform: device.platform,
                        category: device.category,
                        platformType: device.platformType,
                    }));
                }
                catch (parseError) {
                    Logger_1.Logger.error("Failed to parse devices JSON", parseError);
                    return [];
                }
            }
            return [];
        }
        catch (error) {
            Logger_1.Logger.error("Failed to get Flutter devices", error);
            return [];
        }
    }
    /**
     * Check if web support is available
     */
    async isWebSupported() {
        const devices = await this.getDevices();
        return devices.some((device) => device.platform === "web");
    }
    /**
     * Run flutter pub get
     */
    async pubGet() {
        try {
            Logger_1.Logger.info("Running flutter pub get...");
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["pub", "get"], {
                timeout: 60000,
                cwd: this.options.workingDirectory,
            });
            if (result.exitCode === 0) {
                Logger_1.Logger.info("flutter pub get completed successfully");
                return true;
            }
            else {
                Logger_1.Logger.error("flutter pub get failed", result.stderr);
                return false;
            }
        }
        catch (error) {
            Logger_1.Logger.error("Failed to run flutter pub get", error);
            return false;
        }
    }
    /**
     * Clean Flutter project
     */
    async clean() {
        try {
            Logger_1.Logger.info("Running flutter clean...");
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["clean"], {
                timeout: 30000,
                cwd: this.options.workingDirectory,
            });
            if (result.exitCode === 0) {
                Logger_1.Logger.info("flutter clean completed successfully");
                return true;
            }
            else {
                Logger_1.Logger.error("flutter clean failed", result.stderr);
                return false;
            }
        }
        catch (error) {
            Logger_1.Logger.error("Failed to run flutter clean", error);
            return false;
        }
    }
    /**
     * Build Flutter web
     */
    async buildWeb() {
        try {
            Logger_1.Logger.info("Building Flutter web...");
            const args = ["build", "web"];
            if (this.options.additionalArgs) {
                args.push(...this.options.additionalArgs);
            }
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, args, {
                timeout: 300000, // 5 minutes
                cwd: this.options.workingDirectory,
            });
            if (result.exitCode === 0) {
                Logger_1.Logger.info("Flutter web build completed successfully");
                return true;
            }
            else {
                Logger_1.Logger.error("Flutter web build failed", result.stderr);
                return false;
            }
        }
        catch (error) {
            Logger_1.Logger.error("Failed to build Flutter web", error);
            return false;
        }
    }
    /**
     * Start Flutter web server
     */
    startWebServer(port) {
        const args = ["run", "-d", "web-server"];
        if (port) {
            args.push("--web-port", port.toString());
        }
        else {
            args.push("--web-port", "0"); // Random port
        }
        args.push("--web-hostname", "localhost");
        // Add additional args
        if (this.options.additionalArgs) {
            args.push(...this.options.additionalArgs);
        }
        Logger_1.Logger.info(`Starting Flutter web server: ${this.flutterCommand} ${args.join(" ")}`);
        const process = ProcessUtils_1.ProcessUtils.spawn(this.flutterCommand, args, {
            cwd: this.options.workingDirectory,
        });
        return process;
    }
    /**
     * Create a new Flutter project
     */
    async createProject(projectName, projectPath) {
        try {
            Logger_1.Logger.info(`Creating Flutter project: ${projectName}`);
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["create", "--platforms", "web", projectName], {
                timeout: 120000, // 2 minutes
                cwd: projectPath,
            });
            if (result.exitCode === 0) {
                Logger_1.Logger.info("Flutter project created successfully");
                return true;
            }
            else {
                Logger_1.Logger.error("Flutter project creation failed", result.stderr);
                return false;
            }
        }
        catch (error) {
            Logger_1.Logger.error("Failed to create Flutter project", error);
            return false;
        }
    }
    /**
     * Enable web support for existing project
     */
    async enableWeb() {
        try {
            Logger_1.Logger.info("Enabling Flutter web support...");
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["create", ".", "--platforms", "web"], {
                timeout: 60000,
                cwd: this.options.workingDirectory,
            });
            if (result.exitCode === 0) {
                Logger_1.Logger.info("Flutter web support enabled successfully");
                return true;
            }
            else {
                Logger_1.Logger.error("Failed to enable Flutter web support", result.stderr);
                return false;
            }
        }
        catch (error) {
            Logger_1.Logger.error("Failed to enable Flutter web support", error);
            return false;
        }
    }
    /**
     * Run Flutter doctor
     */
    async doctor() {
        try {
            Logger_1.Logger.info("Running flutter doctor...");
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["doctor", "-v"], {
                timeout: 30000,
                cwd: this.options.workingDirectory,
            });
            return {
                success: result.exitCode === 0,
                output: result.stdout + result.stderr,
            };
        }
        catch (error) {
            Logger_1.Logger.error("Failed to run flutter doctor", error);
            return {
                success: false,
                output: `Error running flutter doctor: ${error}`,
            };
        }
    }
    /**
     * Analyze Flutter project
     */
    async analyze() {
        try {
            Logger_1.Logger.info("Analyzing Flutter project...");
            const result = await ProcessUtils_1.ProcessUtils.execute(this.flutterCommand, ["analyze"], {
                timeout: 60000,
                cwd: this.options.workingDirectory,
            });
            const issues = result.stdout
                .split("\n")
                .filter((line) => line.includes("•") ||
                line.includes("error") ||
                line.includes("warning"))
                .map((line) => line.trim())
                .filter((line) => line.length > 0);
            return {
                success: result.exitCode === 0,
                issues,
            };
        }
        catch (error) {
            Logger_1.Logger.error("Failed to analyze Flutter project", error);
            return {
                success: false,
                issues: [`Analysis failed: ${error}`],
            };
        }
    }
    /**
     * Resolve Flutter command path
     */
    resolveFlutterCommand() {
        if (this.options.flutterSdkPath) {
            const isWindows = process.platform === "win32";
            const flutterBin = isWindows ? "flutter.bat" : "flutter";
            return `${this.options.flutterSdkPath}/bin/${flutterBin}`;
        }
        // Use flutter from PATH
        return process.platform === "win32" ? "flutter.bat" : "flutter";
    }
    /**
     * Validate Flutter installation
     */
    async validateInstallation() {
        const issues = [];
        try {
            // Check if Flutter command is available
            const isAvailable = await this.isFlutterAvailable();
            if (!isAvailable) {
                issues.push("Flutter command not found. Please install Flutter SDK.");
                return { valid: false, issues };
            }
            // Check Flutter doctor
            const doctorResult = await this.doctor();
            if (!doctorResult.success) {
                issues.push('Flutter doctor reported issues. Run "flutter doctor" for details.');
            }
            // Check web support
            const webSupported = await this.isWebSupported();
            if (!webSupported) {
                issues.push('Flutter web support not available. Run "flutter config --enable-web".');
            }
            return {
                valid: issues.length === 0,
                issues,
            };
        }
        catch (error) {
            issues.push(`Flutter validation failed: ${error}`);
            return { valid: false, issues };
        }
    }
}
exports.FlutterCliWrapper = FlutterCliWrapper;
//# sourceMappingURL=FlutterCliWrapper.js.map