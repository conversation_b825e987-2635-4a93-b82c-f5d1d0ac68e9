{"version": 3, "file": "CommandManager.js", "sourceRoot": "", "sources": ["../../src/commands/CommandManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,4CAAyC;AAEzC;;GAEG;AACH,MAAa,cAAc;IAIF;IACA;IACA;IALJ,WAAW,GAAwB,EAAE,CAAC;IAEvD,YACqB,cAA8B,EAC9B,cAA8B,EAC9B,gBAAkC;QAFlC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,qBAAgB,GAAhB,gBAAgB,CAAkB;IACpD,CAAC;IAEJ;;OAEG;IACI,gBAAgB,CAAC,OAAgC;QACpD,MAAM,QAAQ,GAAG;YACb,wBAAwB;YACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACvF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAErF,mBAAmB;YACnB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YAEvF,mBAAmB;YACnB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAC7E,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAE/E,mBAAmB;YACnB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnF,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3E,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAE7E,8CAA8C;YAC9C,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;SAC1F,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QACnC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAExC,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACtB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE5C,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;YAChD,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sEAAsE,CAAC,CAAC;QAC3G,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACrB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE5C,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC/C,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;YAE1C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC3C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qEAAqE,CAAC,CAAC;QAC1G,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wEAAwE,CAAC,CAAC;QAC7G,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;QAClD,IAAI,SAAS,EAAE,CAAC;YACZ,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9B,CAAC;IACL,CAAC;IAED;;OAEG;IACK,WAAW;QACf,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,WAAW;QACf,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QACxB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YAC3C,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,CAAC;QACzE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS;QACnB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QACpE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU;QACpB,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACvC,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QACrE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,YAAY;QAChB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACK,QAAQ;QACZ,eAAM,CAAC,IAAI,EAAE,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,SAAS;QACb,eAAM,CAAC,KAAK,EAAE,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,WAAW;QACf,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,oGAAoG,EACpG,eAAe,CAClB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;gBAChC,IAAI,CAAC,YAAY,EAAE,CAAC;YACxB,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;CACJ;AA9MD,wCA8MC"}