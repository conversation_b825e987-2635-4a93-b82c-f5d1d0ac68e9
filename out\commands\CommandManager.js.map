{"version": 3, "file": "CommandManager.js", "sourceRoot": "", "sources": ["../../src/commands/CommandManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAIjC,4CAAyC;AAEzC;;GAEG;AACH,MAAa,cAAc;IAIN;IACA;IACA;IALF,WAAW,GAAwB,EAAE,CAAC;IAEvD,YACmB,cAA8B,EAC9B,cAA8B,EAC9B,gBAAkC;QAFlC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,mBAAc,GAAd,cAAc,CAAgB;QAC9B,qBAAgB,GAAhB,gBAAgB,CAAkB;IAClD,CAAC;IAEJ;;OAEG;IACI,gBAAgB,CAAC,OAAgC;QACtD,MAAM,QAAQ,GAAG;YACf,wBAAwB;YACxB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAC5D,IAAI,CAAC,YAAY,EAAE,CACpB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAC3D,IAAI,CAAC,WAAW,EAAE,CACnB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAC9D,IAAI,CAAC,cAAc,EAAE,CACtB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,GAAG,EAAE,CAC7D,IAAI,CAAC,aAAa,EAAE,CACrB;YAED,mBAAmB;YACnB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAC3D,IAAI,CAAC,WAAW,EAAE,CACnB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAAE,GAAG,EAAE,CAC3D,IAAI,CAAC,WAAW,EAAE,CACnB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE,CAC9D,IAAI,CAAC,cAAc,EAAE,CACtB;YAED,mBAAmB;YACnB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE,CACzD,IAAI,CAAC,SAAS,EAAE,CACjB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,EAAE,GAAG,EAAE,CAC1D,IAAI,CAAC,UAAU,EAAE,CAClB;YAED,mBAAmB;YACnB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAC5D,IAAI,CAAC,YAAY,EAAE,CACpB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE,CACxD,IAAI,CAAC,QAAQ,EAAE,CAChB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE,CACzD,IAAI,CAAC,SAAS,EAAE,CACjB;YACD,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE,CAC5D,IAAI,CAAC,wBAAwB,EAAE,CAChC;YAED,8CAA8C;YAC9C,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,GAAG,EAAE,CACjE,IAAI,CAAC,WAAW,EAAE,CACnB;SACF,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QACnC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAExC,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY;QACxB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE5C,MAAM,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;YAChD,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAE/C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC5B,sEAAsE,CACvE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW;QACvB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAE5C,MAAM,IAAI,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;YAC/C,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,CAAC;YAE1C,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC3C,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC5B,qEAAqE,CACtE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC5B,wEAAwE,CACzE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;QAClD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAC5B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc;QAC1B,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YAC3C,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS;QACrB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;YACtC,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU;QACtB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACvC,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,UAAU,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,QAAQ;QACd,eAAM,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,SAAS;QACf,eAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAEhD,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;YAChE,QAAQ,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;YACvC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEhB,MAAM,CAAC,MAAM;iBACV,sBAAsB,CACrB,6EAA6E,EAC7E,eAAe,CAChB;iBACA,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;gBAClB,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;oBAClC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uCAAuC,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,MAAM,CAAC,MAAM;aACV,sBAAsB,CACrB,oGAAoG,EACpG,eAAe,CAChB;aACA,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YAClB,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;gBAClC,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACZ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9B,CAAC;CACF;AA/QD,wCA+QC"}