import { spawn, ChildProcess, SpawnOptions } from "child_process";
import { Logger } from "./Logger";

export interface ProcessResult {
  exitCode: number;
  stdout: string;
  stderr: string;
  duration: number;
}

export interface ProcessOptions extends SpawnOptions {
  timeout?: number;
  encoding?: BufferEncoding;
}

/**
 * Process utilities for spawning and managing child processes
 */
export class ProcessUtils {
  /**
   * Execute a command and return the result
   */
  public static async execute(
    command: string,
    args: string[] = [],
    options: ProcessOptions = {}
  ): Promise<ProcessResult> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        stdio: ["pipe", "pipe", "pipe"],
        ...options,
      });

      let stdout = "";
      let stderr = "";
      let timeoutId: NodeJS.Timeout | undefined;

      // Setup timeout
      if (options.timeout) {
        timeoutId = setTimeout(() => {
          child.kill("SIGKILL");
          reject(new Error(`Process timeout after ${options.timeout}ms`));
        }, options.timeout);
      }

      // Collect stdout
      if (child.stdout) {
        child.stdout.on("data", (data: Buffer) => {
          stdout += data.toString(options.encoding || "utf8");
        });
      }

      // Collect stderr
      if (child.stderr) {
        child.stderr.on("data", (data: Buffer) => {
          stderr += data.toString(options.encoding || "utf8");
        });
      }

      // Handle process completion
      child.on("close", (exitCode: number | null) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        const duration = Date.now() - startTime;
        const result: ProcessResult = {
          exitCode: exitCode || 0,
          stdout,
          stderr,
          duration,
        };

        Logger.debug(
          `Process completed: ${command} ${args.join(
            " "
          )} (${duration}ms, exit code: ${exitCode})`
        );
        resolve(result);
      });

      // Handle process errors
      child.on("error", (error: Error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        Logger.error(`Process error: ${command} ${args.join(" ")}`, error);
        reject(error);
      });
    });
  }

  /**
   * Spawn a long-running process
   */
  public static spawn(
    command: string,
    args: string[] = [],
    options: ProcessOptions = {}
  ): ChildProcess {
    Logger.debug(`Spawning process: ${command} ${args.join(" ")}`);

    const child = spawn(command, args, {
      stdio: ["pipe", "pipe", "pipe"],
      ...options,
    });

    // Log process events
    child.on("spawn", () => {
      Logger.debug(`Process spawned: ${command} (PID: ${child.pid})`);
    });

    child.on("exit", (code, signal) => {
      Logger.debug(
        `Process exited: ${command} (code: ${code}, signal: ${signal})`
      );
    });

    child.on("error", (error) => {
      Logger.error(`Process error: ${command}`, error);
    });

    return child;
  }

  /**
   * Check if a command is available in the system
   */
  public static async isCommandAvailable(command: string): Promise<boolean> {
    try {
      const isWindows = process.platform === "win32";
      const checkCommand = isWindows ? "where" : "which";

      const result = await this.execute(checkCommand, [command], {
        timeout: 5000,
      });

      return result.exitCode === 0;
    } catch (error) {
      Logger.debug(`Command not available: ${command}`, error);
      return false;
    }
  }

  /**
   * Get command version
   */
  public static async getCommandVersion(
    command: string,
    versionArg: string = "--version"
  ): Promise<string | undefined> {
    try {
      const result = await this.execute(command, [versionArg], {
        timeout: 10000,
      });

      if (result.exitCode === 0) {
        return result.stdout.trim();
      }

      return undefined;
    } catch (error) {
      Logger.debug(`Failed to get version for command: ${command}`, error);
      return undefined;
    }
  }

  /**
   * Kill a process gracefully
   */
  public static async killProcess(
    process: ChildProcess,
    timeoutMs: number = 5000
  ): Promise<void> {
    if (!process || process.killed) {
      return;
    }

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        // Force kill if graceful shutdown failed
        if (!process.killed) {
          Logger.warn(`Force killing process (PID: ${process.pid})`);
          process.kill("SIGKILL");
        }
        resolve();
      }, timeoutMs);

      process.on("exit", () => {
        clearTimeout(timeout);
        resolve();
      });

      // Try graceful shutdown first
      Logger.debug(`Gracefully terminating process (PID: ${process.pid})`);
      process.kill("SIGTERM");
    });
  }

  /**
   * Send input to a process
   */
  public static sendInput(process: ChildProcess, input: string): boolean {
    if (!process.stdin || process.stdin.destroyed) {
      Logger.warn("Cannot send input: process stdin not available");
      return false;
    }

    try {
      process.stdin.write(input);
      return true;
    } catch (error) {
      Logger.error("Failed to send input to process", error);
      return false;
    }
  }

  /**
   * Wait for process to exit
   */
  public static async waitForExit(
    process: ChildProcess,
    timeoutMs?: number
  ): Promise<number> {
    return new Promise((resolve, reject) => {
      let timeoutId: NodeJS.Timeout | undefined;

      if (timeoutMs) {
        timeoutId = setTimeout(() => {
          reject(new Error(`Process exit timeout after ${timeoutMs}ms`));
        }, timeoutMs);
      }

      process.on("exit", (code) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        resolve(code || 0);
      });

      process.on("error", (error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        reject(error);
      });
    });
  }

  /**
   * Create a process monitor
   */
  public static createProcessMonitor(process: ChildProcess): ProcessMonitor {
    return new ProcessMonitor(process);
  }

  /**
   * Find processes by name (platform-specific)
   */
  public static async findProcessesByName(name: string): Promise<number[]> {
    try {
      const isWindows = process.platform === "win32";
      let command: string;
      let args: string[];

      if (isWindows) {
        command = "tasklist";
        args = ["/FI", `IMAGENAME eq ${name}*`, "/FO", "CSV", "/NH"];
      } else {
        command = "pgrep";
        args = ["-f", name];
      }

      const result = await this.execute(command, args, { timeout: 5000 });

      if (result.exitCode !== 0) {
        return [];
      }

      if (isWindows) {
        // Parse Windows tasklist output
        const lines = result.stdout.split("\n").filter((line) => line.trim());
        return lines
          .map((line) => {
            const parts = line.split(",");
            return parseInt(parts[1]?.replace(/"/g, "") || "0", 10);
          })
          .filter((pid) => pid > 0);
      } else {
        // Parse Unix pgrep output
        return result.stdout
          .split("\n")
          .filter((line) => line.trim())
          .map((line) => parseInt(line.trim(), 10))
          .filter((pid) => !isNaN(pid));
      }
    } catch (error) {
      Logger.error(`Failed to find processes by name: ${name}`, error);
      return [];
    }
  }

  /**
   * Kill processes by name
   */
  public static async killProcessesByName(name: string): Promise<number> {
    try {
      const pids = await this.findProcessesByName(name);
      let killedCount = 0;

      for (const pid of pids) {
        try {
          process.kill(pid, "SIGTERM");
          killedCount++;
          Logger.debug(`Killed process: ${name} (PID: ${pid})`);
        } catch (error) {
          Logger.debug(`Failed to kill process: ${name} (PID: ${pid})`, error);
        }
      }

      return killedCount;
    } catch (error) {
      Logger.error(`Failed to kill processes by name: ${name}`, error);
      return 0;
    }
  }
}

/**
 * Process monitor for tracking process health and statistics
 */
export class ProcessMonitor {
  private process: ChildProcess;
  private startTime: number;
  private isMonitoring = false;
  private stats = {
    restarts: 0,
    uptime: 0,
    lastRestart: 0,
  };

  constructor(process: ChildProcess) {
    this.process = process;
    this.startTime = Date.now();
    this.startMonitoring();
  }

  private startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;

    this.process.on("exit", (code, signal) => {
      this.stats.uptime = Date.now() - this.startTime;
      Logger.debug(
        `Process monitor: process exited (code: ${code}, signal: ${signal}, uptime: ${this.stats.uptime}ms)`
      );
    });

    this.process.on("error", (error) => {
      Logger.error("Process monitor: process error", error);
    });
  }

  public getStats() {
    return {
      ...this.stats,
      currentUptime: this.isRunning()
        ? Date.now() - this.startTime
        : this.stats.uptime,
      isRunning: this.isRunning(),
      pid: this.process.pid,
    };
  }

  public isRunning(): boolean {
    return !this.process.killed && this.process.exitCode === null;
  }

  public restart(): void {
    this.stats.restarts++;
    this.stats.lastRestart = Date.now();
    this.startTime = Date.now();
  }

  public stop(): void {
    this.isMonitoring = false;
    this.stats.uptime = Date.now() - this.startTime;
  }
}
