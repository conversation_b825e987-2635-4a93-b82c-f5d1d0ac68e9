"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const ConfigurationManager_1 = require("../../config/ConfigurationManager");
const Logger_1 = require("../../utils/Logger");
suite("ConfigurationManager Tests", () => {
    let configManager;
    let mockContext;
    suiteSetup(async () => {
        // Create mock context
        mockContext = {
            subscriptions: [],
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => [],
            },
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                setKeysForSync: () => { },
                keys: () => [],
            },
            extensionPath: __dirname,
            extensionUri: vscode.Uri.file(__dirname),
            environmentVariableCollection: {},
            extensionMode: vscode.ExtensionMode.Test,
            storageUri: vscode.Uri.file(__dirname),
            globalStorageUri: vscode.Uri.file(__dirname),
            logUri: vscode.Uri.file(__dirname),
            secrets: {},
            asAbsolutePath: (relativePath) => relativePath,
            storagePath: __dirname,
            globalStoragePath: __dirname,
            logPath: __dirname,
            extension: {},
            languageModelAccessInformation: {},
        };
        Logger_1.Logger.initialize(mockContext);
    });
    setup(() => {
        configManager = new ConfigurationManager_1.ConfigurationManager();
    });
    teardown(() => {
        if (configManager) {
            configManager.dispose();
        }
    });
    test("Should create ConfigurationManager instance", () => {
        assert.ok(configManager);
    });
    test("Should get configuration values", () => {
        const config = configManager.getConfiguration();
        assert.ok(config);
        assert.ok(config.flutter);
        assert.ok(config.proxy);
        assert.ok(config.ui);
        assert.ok(config.performance);
    });
    test("Should validate default configuration", () => {
        const errors = configManager.validateConfiguration();
        assert.strictEqual(errors.length, 0, "Default configuration should be valid");
    });
    test("Should handle hot reload settings", () => {
        const hotReloadEnabled = configManager.isHotReloadOnSaveEnabled();
        assert.strictEqual(typeof hotReloadEnabled, "boolean");
    });
    test("Should handle debug mode settings", () => {
        const debugMode = configManager.isDebugModeEnabled();
        assert.strictEqual(typeof debugMode, "boolean");
    });
    test("Should handle status bar settings", () => {
        const showStatusBar = configManager.shouldShowStatusBar();
        assert.strictEqual(typeof showStatusBar, "boolean");
    });
    test("Should handle auto show preview settings", () => {
        const autoShow = configManager.shouldAutoShowPreview();
        assert.strictEqual(typeof autoShow, "boolean");
    });
    test("Should get preview position", () => {
        const position = configManager.getPreviewPosition();
        assert.ok(["beside", "active"].includes(position));
    });
    test("Should get device frame setting", () => {
        const frame = configManager.getDeviceFrame();
        assert.ok(["none", "ios", "android"].includes(frame));
    });
    test("Should handle CORS settings", () => {
        const corsEnabled = configManager.isCorsEnabled();
        assert.strictEqual(typeof corsEnabled, "boolean");
        const origins = configManager.getCorsOrigins();
        assert.ok(Array.isArray(origins));
    });
    test("Should handle performance settings", () => {
        const throttling = configManager.isThrottlingEnabled();
        assert.strictEqual(typeof throttling, "boolean");
        const frameRate = configManager.getMaxFrameRate();
        assert.strictEqual(typeof frameRate, "number");
        assert.ok(frameRate > 0);
        const memoryLimit = configManager.getMemoryLimit();
        assert.strictEqual(typeof memoryLimit, "number");
        assert.ok(memoryLimit > 0);
    });
    test("Should get additional Flutter args", () => {
        const args = configManager.getFlutterAdditionalArgs();
        assert.ok(Array.isArray(args));
    });
    test("Should dispose cleanly", () => {
        assert.doesNotThrow(() => {
            configManager.dispose();
        });
    });
});
//# sourceMappingURL=ConfigurationManager.test.js.map