{"name": "devgen-syncview", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Live, interactive Flutter app preview in VS Code", "version": "0.0.1", "engines": {"vscode": "^1.100.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "syncview.startPreview", "title": "Start Flutter Preview", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.stopPreview", "title": "Stop Flutter Preview", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.restartPreview", "title": "Restart Flutter Preview", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.togglePreview", "title": "Toggle Flutter Preview", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.showPreview", "title": "Show Preview Panel", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.refreshPreview", "title": "Refresh Preview", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.hotReload", "title": "Hot Reload", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.hotRestart", "title": "<PERSON> Restart", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.openSettings", "title": "Open Settings", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "syncview.showLogs", "title": "Show Logs", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"command": "devgen-syncview.helloWorld", "title": "Hello World (Legacy)", "category": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "configuration": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"syncview.flutter.sdkPath": {"type": "string", "description": "Path to Flutter SDK (leave empty for auto-detection)"}, "syncview.flutter.webPort": {"type": "number", "description": "Port for Flutter web server (0 for random port)", "default": 0}, "syncview.flutter.hotReloadOnSave": {"type": "boolean", "description": "Enable automatic hot reload on file save", "default": true}, "syncview.flutter.debugMode": {"type": "boolean", "description": "Enable debug mode for Flutter", "default": false}, "syncview.flutter.additionalArgs": {"type": "array", "items": {"type": "string"}, "description": "Additional arguments for Flutter CLI", "default": []}, "syncview.proxy.port": {"type": "number", "description": "Port for proxy server (0 for random port)", "default": 0}, "syncview.proxy.corsEnabled": {"type": "boolean", "description": "Enable CORS handling", "default": true}, "syncview.proxy.corsOrigins": {"type": "array", "items": {"type": "string"}, "description": "Allowed CORS origins", "default": ["*"]}, "syncview.ui.showStatusBar": {"type": "boolean", "description": "Show status bar item", "default": true}, "syncview.ui.autoShowPreview": {"type": "boolean", "description": "Automatically show preview when starting", "default": true}, "syncview.ui.previewPosition": {"type": "string", "enum": ["beside", "active"], "description": "Position for preview panel", "default": "beside"}, "syncview.ui.deviceFrame": {"type": "string", "enum": ["none", "ios", "android"], "description": "Device frame for preview", "default": "none"}, "syncview.performance.enableThrottling": {"type": "boolean", "description": "Enable performance throttling", "default": false}, "syncview.performance.maxFrameRate": {"type": "number", "description": "Maximum frame rate for preview", "default": 60, "minimum": 1, "maximum": 120}, "syncview.performance.memoryLimit": {"type": "number", "description": "Memory limit in MB", "default": 512, "minimum": 128, "maximum": 4096}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.100.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2"}}