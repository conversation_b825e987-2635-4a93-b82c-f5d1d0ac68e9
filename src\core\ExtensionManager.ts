import * as vscode from "vscode";
import { CommandManager } from "../commands/CommandManager";
import { WebViewManager } from "../webview/WebViewManager";
import { WebViewProvider } from "../webview/WebViewProvider";
import { FlutterManager } from "../flutter/FlutterManager";
import { ProxyServerManager } from "../proxy/ProxyServerManager";
import { StatusBarManager } from "../ui/StatusBarManager";
import { NotificationManager } from "../ui/NotificationManager";
import { ConfigurationManager } from "../config/ConfigurationManager";
import { WorkspaceManager } from "./WorkspaceManager";
import { PerformanceMonitor } from "../utils/PerformanceMonitor";
import { Logger } from "../utils/Logger";

/**
 * Main extension manager that coordinates all components
 */
export class ExtensionManager implements vscode.Disposable {
  private readonly disposables: vscode.Disposable[] = [];
  private commandManager: CommandManager;
  private webViewManager: WebViewManager;
  private webViewProvider: WebViewProvider;
  private flutterManager: FlutterManager;
  private proxyServerManager: ProxyServerManager;
  private statusBarManager: StatusBarManager;
  private notificationManager: NotificationManager;
  private configurationManager: ConfigurationManager;
  private workspaceManager: WorkspaceManager;
  private performanceMonitor: PerformanceMonitor;

  constructor(private readonly context: vscode.ExtensionContext) {
    // Initialize core managers
    this.configurationManager = new ConfigurationManager();
    this.workspaceManager = new WorkspaceManager();
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.notificationManager = NotificationManager.getInstance();

    // Initialize Flutter and proxy components
    this.flutterManager = new FlutterManager();
    this.proxyServerManager = new ProxyServerManager();

    // Initialize UI components
    this.webViewProvider = new WebViewProvider(context.extensionUri, context);
    this.webViewManager = new WebViewManager(context, this.proxyServerManager);
    this.statusBarManager = new StatusBarManager();

    // Initialize command manager with all dependencies
    this.commandManager = new CommandManager(
      this.webViewManager,
      this.flutterManager,
      this.statusBarManager
    );
  }

  /**
   * Activate all extension components
   */
  public activate(): void {
    Logger.info("Initializing SyncView components...");

    // Start performance monitoring
    this.performanceMonitor.startMonitoring();

    // Register WebView provider
    this.context.subscriptions.push(
      vscode.window.registerWebviewViewProvider(
        WebViewProvider.viewType,
        this.webViewProvider
      )
    );

    // Register all components for disposal
    this.disposables.push(
      this.configurationManager,
      this.workspaceManager,
      this.flutterManager,
      this.proxyServerManager,
      this.webViewManager,
      this.statusBarManager,
      this.commandManager,
      this.performanceMonitor
    );

    // Initialize components
    this.commandManager.registerCommands(this.context);
    this.statusBarManager.initialize();

    // Setup event listeners
    this.setupEventListeners();

    // Check workspace on startup
    this.checkWorkspaceOnStartup();

    Logger.info("SyncView components initialized successfully");
  }

  /**
   * Setup cross-component event listeners
   */
  private setupEventListeners(): void {
    // Flutter process events
    this.flutterManager.on("processStarted", (url: string) => {
      this.proxyServerManager.setTargetUrl(url);
      this.statusBarManager.setStatus("running");
      this.notificationManager.showFlutterReady(url);
    });

    this.flutterManager.on("processStopped", () => {
      this.proxyServerManager.stop();
      this.statusBarManager.setStatus("stopped");
    });

    this.flutterManager.on("error", (error: Error) => {
      this.statusBarManager.setStatus("error");
      this.notificationManager.showFlutterError(error.message);
      Logger.error("Flutter process error", error);
    });

    this.flutterManager.on("hotReloadComplete", () => {
      this.notificationManager.showHotReloadComplete();
    });

    this.flutterManager.on("hotRestartComplete", () => {
      this.notificationManager.showHotRestartComplete();
    });

    // Proxy server events
    this.proxyServerManager.on("serverStarted", (proxyUrl: string) => {
      this.webViewManager.updatePreviewUrl(proxyUrl);
      this.webViewProvider.updatePreview(proxyUrl);
    });

    this.proxyServerManager.on("error", (error: Error) => {
      Logger.error("Proxy server error", error);
      this.statusBarManager.setStatus("error");
    });

    // Workspace events
    this.workspaceManager.on("workspaceChanged", (workspaceInfo: any) => {
      Logger.info("Workspace changed", workspaceInfo);
      this.handleWorkspaceChange(workspaceInfo);
    });

    // Configuration changes
    this.configurationManager.on("configurationChanged", () => {
      this.handleConfigurationChange();
    });

    // Performance monitoring
    this.performanceMonitor.on("performanceIssue", (issues: string[]) => {
      Logger.warn("Performance issues detected", issues);
    });
  }

  /**
   * Check workspace on startup
   */
  private async checkWorkspaceOnStartup(): Promise<void> {
    try {
      const workspaceInfo = await this.workspaceManager.getWorkspaceInfo();

      if (!workspaceInfo.hasFlutterProject) {
        this.notificationManager.showProjectNotFound();
        return;
      }

      if (!workspaceInfo.hasWebSupport) {
        this.notificationManager.showWebSupportMissing();
        return;
      }

      Logger.info("Workspace validation completed", workspaceInfo);
    } catch (error) {
      Logger.error("Workspace startup check failed", error);
    }
  }

  /**
   * Handle workspace changes
   */
  private handleWorkspaceChange(workspaceInfo: any): void {
    // Update status based on workspace state
    if (!workspaceInfo.hasFlutterProject) {
      this.statusBarManager.setStatus("stopped");
    }

    // Restart preview if it was running and project changed
    if (this.flutterManager.isRunning() && workspaceInfo.flutterProjectPath) {
      Logger.info("Restarting preview due to workspace change");
      this.restartPreviewSafely();
    }
  }

  /**
   * Handle configuration changes
   */
  private handleConfigurationChange(): void {
    Logger.info("Configuration changed, updating components...");

    // Validate configuration
    const errors = this.configurationManager.validateConfiguration();
    if (errors.length > 0) {
      Logger.warn("Configuration validation errors", errors);
      errors.forEach((error: string) => {
        this.notificationManager.showConfigurationError("general", error);
      });
    }

    // Restart services if needed based on configuration changes
    if (this.flutterManager.isRunning()) {
      Logger.info("Restarting services due to configuration change");
      this.restartPreviewSafely();
    }
  }

  /**
   * Safely restart preview without throwing errors
   */
  private async restartPreviewSafely(): Promise<void> {
    try {
      if (this.flutterManager.isRunning()) {
        await this.flutterManager.stopFlutterProcess();
      }
      await this.flutterManager.startFlutterProcess();
    } catch (error) {
      Logger.error("Failed to restart preview", error);
      this.statusBarManager.setStatus("error");
    }
  }

  /**
   * Dispose all resources
   */
  public dispose(): void {
    Logger.info("Disposing SyncView extension manager...");
    this.disposables.forEach((disposable) => disposable.dispose());
    this.disposables.length = 0;
  }
}
