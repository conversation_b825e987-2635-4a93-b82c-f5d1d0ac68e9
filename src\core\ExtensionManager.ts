import * as vscode from 'vscode';
import { CommandManager } from '../commands/CommandManager';
import { WebViewManager } from '../webview/WebViewManager';
import { FlutterManager } from '../flutter/FlutterManager';
import { ProxyServerManager } from '../proxy/ProxyServerManager';
import { StatusBarManager } from '../ui/StatusBarManager';
import { ConfigurationManager } from '../config/ConfigurationManager';
import { Logger } from '../utils/Logger';

/**
 * Main extension manager that coordinates all components
 */
export class ExtensionManager implements vscode.Disposable {
    private readonly disposables: vscode.Disposable[] = [];
    private commandManager: CommandManager;
    private webViewManager: WebViewManager;
    private flutterManager: FlutterManager;
    private proxyServerManager: ProxyServerManager;
    private statusBarManager: StatusBarManager;
    private configurationManager: ConfigurationManager;

    constructor(private readonly context: vscode.ExtensionContext) {
        this.configurationManager = new ConfigurationManager();
        this.flutterManager = new FlutterManager();
        this.proxyServerManager = new ProxyServerManager();
        this.webViewManager = new WebViewManager(context, this.proxyServerManager);
        this.statusBarManager = new StatusBarManager();
        this.commandManager = new CommandManager(
            this.webViewManager,
            this.flutterManager,
            this.statusBarManager
        );
    }

    /**
     * Activate all extension components
     */
    public activate(): void {
        Logger.info('Initializing SyncView components...');

        // Register all components
        this.disposables.push(
            this.configurationManager,
            this.flutterManager,
            this.proxyServerManager,
            this.webViewManager,
            this.statusBarManager,
            this.commandManager
        );

        // Initialize components
        this.commandManager.registerCommands(this.context);
        this.statusBarManager.initialize();

        // Setup event listeners
        this.setupEventListeners();

        Logger.info('SyncView components initialized successfully');
    }

    /**
     * Setup cross-component event listeners
     */
    private setupEventListeners(): void {
        // Flutter process events
        this.flutterManager.onProcessStarted((url: string) => {
            this.proxyServerManager.setTargetUrl(url);
            this.statusBarManager.setStatus('running');
        });

        this.flutterManager.onProcessStopped(() => {
            this.proxyServerManager.stop();
            this.statusBarManager.setStatus('stopped');
        });

        this.flutterManager.onError((error: Error) => {
            this.statusBarManager.setStatus('error');
            Logger.error('Flutter process error', error);
        });

        // Proxy server events
        this.proxyServerManager.onServerStarted((proxyUrl: string) => {
            this.webViewManager.updatePreviewUrl(proxyUrl);
        });

        // Configuration changes
        this.configurationManager.onConfigurationChanged(() => {
            this.handleConfigurationChange();
        });
    }

    /**
     * Handle configuration changes
     */
    private handleConfigurationChange(): void {
        Logger.info('Configuration changed, updating components...');
        // Restart services if needed based on configuration changes
    }

    /**
     * Dispose all resources
     */
    public dispose(): void {
        Logger.info('Disposing SyncView extension manager...');
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
    }
}
