"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyServerManager = void 0;
const events_1 = require("events");
const ProxyServer_1 = require("./ProxyServer");
const CorsHandler_1 = require("./CorsHandler");
const Logger_1 = require("../utils/Logger");
/**
 * Manages the HTTP proxy server for Flutter web preview
 */
class ProxyServerManager extends events_1.EventEmitter {
    proxyServer;
    corsHandler;
    isServerRunning = false;
    currentProxyUrl;
    targetUrl;
    constructor() {
        super();
        this.corsHandler = new CorsHandler_1.CorsHandler();
    }
    /**
     * Start the proxy server
     */
    async start() {
        if (this.isServerRunning) {
            Logger_1.Logger.warn('Proxy server is already running');
            return this.currentProxyUrl;
        }
        try {
            Logger_1.Logger.info('Starting proxy server...');
            this.proxyServer = new ProxyServer_1.ProxyServer(this.corsHandler);
            const proxyUrl = await this.proxyServer.start();
            this.isServerRunning = true;
            this.currentProxyUrl = proxyUrl;
            this.setupServerEventListeners();
            this.emit('serverStarted', proxyUrl);
            Logger_1.Logger.info(`Proxy server started at: ${proxyUrl}`);
            return proxyUrl;
        }
        catch (error) {
            this.isServerRunning = false;
            Logger_1.Logger.error('Failed to start proxy server', error);
            throw error;
        }
    }
    /**
     * Stop the proxy server
     */
    async stop() {
        if (!this.isServerRunning || !this.proxyServer) {
            Logger_1.Logger.warn('Proxy server is not running');
            return;
        }
        try {
            Logger_1.Logger.info('Stopping proxy server...');
            await this.proxyServer.stop();
            this.isServerRunning = false;
            this.currentProxyUrl = undefined;
            this.proxyServer = undefined;
            this.emit('serverStopped');
            Logger_1.Logger.info('Proxy server stopped successfully');
        }
        catch (error) {
            Logger_1.Logger.error('Failed to stop proxy server', error);
            throw error;
        }
    }
    /**
     * Set the target URL for proxying
     */
    setTargetUrl(url) {
        this.targetUrl = url;
        if (this.proxyServer) {
            this.proxyServer.setTargetUrl(url);
            Logger_1.Logger.info(`Proxy target URL updated: ${url}`);
        }
        // Start proxy server if not already running
        if (!this.isServerRunning) {
            this.start().catch(error => {
                Logger_1.Logger.error('Failed to start proxy server after setting target URL', error);
            });
        }
    }
    /**
     * Get the current proxy URL
     */
    getProxyUrl() {
        return this.currentProxyUrl;
    }
    /**
     * Get the current target URL
     */
    getTargetUrl() {
        return this.targetUrl;
    }
    /**
     * Check if proxy server is running
     */
    isRunning() {
        return this.isServerRunning;
    }
    /**
     * Restart the proxy server
     */
    async restart() {
        Logger_1.Logger.info('Restarting proxy server...');
        if (this.isServerRunning) {
            await this.stop();
        }
        return this.start();
    }
    /**
     * Setup event listeners for the proxy server
     */
    setupServerEventListeners() {
        if (!this.proxyServer) {
            return;
        }
        this.proxyServer.on('request', (req) => {
            Logger_1.Logger.debug(`Proxy request: ${req.method} ${req.url}`);
        });
        this.proxyServer.on('error', (error) => {
            Logger_1.Logger.error('Proxy server error', error);
            this.emit('error', error);
        });
        this.proxyServer.on('targetError', (error) => {
            Logger_1.Logger.error('Proxy target error', error);
            this.emit('targetError', error);
        });
        this.proxyServer.on('proxyResponse', (proxyRes) => {
            Logger_1.Logger.debug(`Proxy response: ${proxyRes.statusCode}`);
        });
    }
    /**
     * Get proxy server statistics
     */
    getStats() {
        if (!this.proxyServer) {
            return null;
        }
        return this.proxyServer.getStats();
    }
    /**
     * Configure CORS settings
     */
    configureCors(options) {
        this.corsHandler.configure(options);
        Logger_1.Logger.info('CORS configuration updated');
    }
    /**
     * Dispose all resources
     */
    dispose() {
        this.stop().catch(error => {
            Logger_1.Logger.error('Error during proxy server disposal', error);
        });
        this.removeAllListeners();
    }
}
exports.ProxyServerManager = ProxyServerManager;
//# sourceMappingURL=ProxyServerManager.js.map