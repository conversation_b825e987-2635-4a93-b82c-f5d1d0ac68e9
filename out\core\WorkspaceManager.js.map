{"version": 3, "file": "WorkspaceManager.js", "sourceRoot": "", "sources": ["../../src/core/WorkspaceManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,mCAAsC;AACtC,8EAA2E;AAC3E,kDAA+C;AAC/C,4CAAyC;AAUzC;;GAEG;AACH,MAAa,gBAAiB,SAAQ,qBAAY;IACtC,WAAW,GAAwB,EAAE,CAAC;IACtC,eAAe,CAAyB;IACxC,oBAAoB,CAA4B;IAChD,WAAW,CAAuC;IAE1D;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,eAAe,GAAG,IAAI,+CAAsB,EAAE,CAAC;QACpD,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB;QACzB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC7B,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACtC,CAAC;QACD,OAAO,IAAI,CAAC,oBAAqB,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB;QAC7B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAEnG,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,IAAI,kBAAsC,CAAC;QAC3C,IAAI,WAA+B,CAAC;QACpC,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,uBAAuB;YACvB,kBAAkB,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;YAErE,IAAI,kBAAkB,EAAE,CAAC;gBACrB,iBAAiB,GAAG,IAAI,CAAC;gBACzB,aAAa,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;gBAE9E,qCAAqC;gBACrC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC;gBAClF,WAAW,GAAG,WAAW,EAAE,IAAI,CAAC;YACpC,CAAC;QACL,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC1C,IAAI,CAAC,oBAAoB,GAAG;YACxB,iBAAiB;YACjB,kBAAkB;YAClB,WAAW;YACX,aAAa;YACb,gBAAgB;SACnB,CAAC;QAEF,8CAA8C;QAC9C,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACzD,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB;QAC9B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB;QAC1B,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa;QACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB;QAC9B,OAAO,IAAI,CAAC,eAAe,CAAC,qBAAqB,EAAE,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAAC,WAAmB;QACjD,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,eAAM,CAAC,KAAK,CAAC,4DAA4D,CAAC,CAAC;YAC3E,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YACrD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;YAE1D,oCAAoC;YACpC,IAAI,MAAM,qBAAS,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/C,eAAM,CAAC,KAAK,CAAC,aAAa,WAAW,iBAAiB,CAAC,CAAC;gBACxD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,mEAAmE;YACnE,iDAAiD;YACjD,MAAM,qBAAS,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE7C,wCAAwC;YACxC,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAElC,eAAM,CAAC,IAAI,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;YAC1D,OAAO,IAAI,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB;QACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACvD,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC;YACD,mEAAmE;YACnE,yDAAyD;YACzD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,qBAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAExC,0BAA0B;YAC1B,MAAM,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAiDtB,CAAC;YAEG,MAAM,qBAAS,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,SAAS,CAAC,CAAC;YAEtE,yBAAyB;YACzB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAElC,eAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,YAAoB;QAChD,OAAO,qBAAS,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,oBAAoB,CAAC,YAAoB;QAC5C,OAAO,qBAAS,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,iBAAiB;QACpB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,OAAO;QACX,CAAC;QAED,iCAAiC;QACjC,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CACvD,iBAAiB,EACjB,KAAK,EAAE,uBAAuB;QAC9B,KAAK,EAAE,uBAAuB;QAC9B,KAAK,CAAE,uBAAuB;SACjC,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YAC/B,eAAM,CAAC,KAAK,CAAC,yBAAyB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YAC/B,eAAM,CAAC,KAAK,CAAC,yBAAyB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YAC/B,eAAM,CAAC,KAAK,CAAC,yBAAyB,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACpD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACxC,eAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,gBAAgB;QACnB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3B,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAC7B,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC1B,qCAAqC;QACrC,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,KAAK,CAAC,EAAE;YAC1E,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACrC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACnD,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC1D,CAAC,CAAC;YACH,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB;QAC7B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,OAAsB,EAAE,OAAsB;QAC1E,OAAO,CACH,OAAO,CAAC,iBAAiB,KAAK,OAAO,CAAC,iBAAiB;YACvD,OAAO,CAAC,kBAAkB,KAAK,OAAO,CAAC,kBAAkB;YACzD,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW;YAC3C,OAAO,CAAC,aAAa,KAAK,OAAO,CAAC,aAAa;YAC/C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,CACxF,CAAC;IACN,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC;CACJ;AArUD,4CAqUC"}