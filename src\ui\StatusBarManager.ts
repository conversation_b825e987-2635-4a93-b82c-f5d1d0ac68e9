import * as vscode from 'vscode';
import { Logger } from '../utils/Logger';

export type StatusBarStatus = 'stopped' | 'starting' | 'running' | 'stopping' | 'error';

/**
 * Manages the status bar items for SyncView
 */
export class StatusBarManager implements vscode.Disposable {
    private statusBarItem: vscode.StatusBarItem;
    private currentStatus: StatusBarStatus = 'stopped';

    constructor() {
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Left,
            100
        );
    }

    /**
     * Initialize the status bar
     */
    public initialize(): void {
        this.updateStatusBar();
        this.statusBarItem.show();
        Logger.info('Status bar initialized');
    }

    /**
     * Set the current status
     */
    public setStatus(status: StatusBarStatus): void {
        this.currentStatus = status;
        this.updateStatusBar();
        Logger.debug(`Status bar updated: ${status}`);
    }

    /**
     * Get the current status
     */
    public getStatus(): StatusBarStatus {
        return this.currentStatus;
    }

    /**
     * Update the status bar display
     */
    private updateStatusBar(): void {
        switch (this.currentStatus) {
            case 'stopped':
                this.statusBarItem.text = '$(play) SyncView';
                this.statusBarItem.tooltip = 'Start Flutter Preview';
                this.statusBarItem.command = 'syncview.startPreview';
                this.statusBarItem.backgroundColor = undefined;
                break;

            case 'starting':
                this.statusBarItem.text = '$(loading~spin) SyncView Starting...';
                this.statusBarItem.tooltip = 'Flutter Preview is starting';
                this.statusBarItem.command = undefined;
                this.statusBarItem.backgroundColor = undefined;
                break;

            case 'running':
                this.statusBarItem.text = '$(stop) SyncView Running';
                this.statusBarItem.tooltip = 'Stop Flutter Preview';
                this.statusBarItem.command = 'syncview.stopPreview';
                this.statusBarItem.backgroundColor = undefined;
                break;

            case 'stopping':
                this.statusBarItem.text = '$(loading~spin) SyncView Stopping...';
                this.statusBarItem.tooltip = 'Flutter Preview is stopping';
                this.statusBarItem.command = undefined;
                this.statusBarItem.backgroundColor = undefined;
                break;

            case 'error':
                this.statusBarItem.text = '$(error) SyncView Error';
                this.statusBarItem.tooltip = 'Flutter Preview encountered an error. Click to restart.';
                this.statusBarItem.command = 'syncview.restartPreview';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                break;

            default:
                this.statusBarItem.text = '$(question) SyncView';
                this.statusBarItem.tooltip = 'Unknown status';
                this.statusBarItem.command = undefined;
                this.statusBarItem.backgroundColor = undefined;
        }
    }

    /**
     * Show a temporary message in the status bar
     */
    public showTemporaryMessage(message: string, durationMs: number = 3000): void {
        const originalText = this.statusBarItem.text;
        const originalTooltip = this.statusBarItem.tooltip;
        const originalCommand = this.statusBarItem.command;

        this.statusBarItem.text = message;
        this.statusBarItem.tooltip = message;
        this.statusBarItem.command = undefined;

        setTimeout(() => {
            this.statusBarItem.text = originalText;
            this.statusBarItem.tooltip = originalTooltip;
            this.statusBarItem.command = originalCommand;
        }, durationMs);
    }

    /**
     * Hide the status bar item
     */
    public hide(): void {
        this.statusBarItem.hide();
    }

    /**
     * Show the status bar item
     */
    public show(): void {
        this.statusBarItem.show();
    }

    /**
     * Dispose the status bar item
     */
    public dispose(): void {
        this.statusBarItem.dispose();
    }
}
