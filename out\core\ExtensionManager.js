"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExtensionManager = void 0;
const vscode = __importStar(require("vscode"));
const CommandManager_1 = require("../commands/CommandManager");
const WebViewManager_1 = require("../webview/WebViewManager");
const WebViewProvider_1 = require("../webview/WebViewProvider");
const FlutterManager_1 = require("../flutter/FlutterManager");
const ProxyServerManager_1 = require("../proxy/ProxyServerManager");
const StatusBarManager_1 = require("../ui/StatusBarManager");
const NotificationManager_1 = require("../ui/NotificationManager");
const ConfigurationManager_1 = require("../config/ConfigurationManager");
const WorkspaceManager_1 = require("./WorkspaceManager");
const PerformanceMonitor_1 = require("../utils/PerformanceMonitor");
const Logger_1 = require("../utils/Logger");
/**
 * Main extension manager that coordinates all components
 */
class ExtensionManager {
    context;
    disposables = [];
    isActivated = false;
    commandManager;
    webViewManager;
    webViewProvider;
    flutterManager;
    proxyServerManager;
    statusBarManager;
    notificationManager;
    configurationManager;
    workspaceManager;
    performanceMonitor;
    constructor(context) {
        this.context = context;
        // Initialize core managers
        this.configurationManager = new ConfigurationManager_1.ConfigurationManager();
        this.workspaceManager = new WorkspaceManager_1.WorkspaceManager();
        this.performanceMonitor = PerformanceMonitor_1.PerformanceMonitor.getInstance();
        this.notificationManager = NotificationManager_1.NotificationManager.getInstance();
        // Initialize Flutter and proxy components
        this.flutterManager = new FlutterManager_1.FlutterManager();
        this.proxyServerManager = new ProxyServerManager_1.ProxyServerManager();
        // Initialize UI components
        this.webViewProvider = new WebViewProvider_1.WebViewProvider(context.extensionUri, context);
        this.webViewManager = new WebViewManager_1.WebViewManager(context, this.proxyServerManager);
        this.statusBarManager = new StatusBarManager_1.StatusBarManager();
        // Initialize command manager with all dependencies
        this.commandManager = new CommandManager_1.CommandManager(this.webViewManager, this.flutterManager, this.statusBarManager);
    }
    /**
     * Activate all extension components
     */
    activate() {
        if (this.isActivated) {
            Logger_1.Logger.warn("Extension manager is already activated");
            return;
        }
        Logger_1.Logger.info("Initializing SyncView components...");
        // Start performance monitoring
        this.performanceMonitor.startMonitoring();
        // Register WebView provider
        this.context.subscriptions.push(vscode.window.registerWebviewViewProvider(WebViewProvider_1.WebViewProvider.viewType, this.webViewProvider));
        this.isActivated = true;
        // Register all components for disposal
        this.disposables.push(this.configurationManager, this.workspaceManager, this.flutterManager, this.proxyServerManager, this.webViewManager, this.statusBarManager, this.commandManager, this.performanceMonitor);
        // Initialize components
        this.commandManager.registerCommands(this.context);
        this.statusBarManager.initialize();
        // Setup event listeners
        this.setupEventListeners();
        // Check workspace on startup
        this.checkWorkspaceOnStartup();
        Logger_1.Logger.info("SyncView components initialized successfully");
    }
    /**
     * Setup cross-component event listeners
     */
    setupEventListeners() {
        // Flutter process events
        this.flutterManager.on("processStarted", (url) => {
            this.proxyServerManager.setTargetUrl(url);
            this.statusBarManager.setStatus("running");
            this.notificationManager.showFlutterReady(url);
        });
        this.flutterManager.on("processStopped", () => {
            this.proxyServerManager.stop();
            this.statusBarManager.setStatus("stopped");
        });
        this.flutterManager.on("error", (error) => {
            this.statusBarManager.setStatus("error");
            this.notificationManager.showFlutterError(error.message);
            Logger_1.Logger.error("Flutter process error", error);
        });
        this.flutterManager.on("hotReloadComplete", () => {
            this.notificationManager.showHotReloadComplete();
        });
        this.flutterManager.on("hotRestartComplete", () => {
            this.notificationManager.showHotRestartComplete();
        });
        // Proxy server events
        this.proxyServerManager.on("serverStarted", (proxyUrl) => {
            this.webViewManager.updatePreviewUrl(proxyUrl);
            this.webViewProvider.updatePreview(proxyUrl);
        });
        this.proxyServerManager.on("error", (error) => {
            Logger_1.Logger.error("Proxy server error", error);
            this.statusBarManager.setStatus("error");
        });
        // Workspace events
        this.workspaceManager.on("workspaceChanged", (workspaceInfo) => {
            Logger_1.Logger.info("Workspace changed", workspaceInfo);
            this.handleWorkspaceChange(workspaceInfo);
        });
        // Configuration changes
        this.configurationManager.on("configurationChanged", () => {
            this.handleConfigurationChange();
        });
        // Performance monitoring
        this.performanceMonitor.on("performanceIssue", (issues) => {
            Logger_1.Logger.warn("Performance issues detected", issues);
        });
    }
    /**
     * Check workspace on startup
     */
    async checkWorkspaceOnStartup() {
        try {
            const workspaceInfo = await this.workspaceManager.getWorkspaceInfo();
            if (!workspaceInfo.hasFlutterProject) {
                this.notificationManager.showProjectNotFound();
                return;
            }
            if (!workspaceInfo.hasWebSupport) {
                this.notificationManager.showWebSupportMissing();
                return;
            }
            Logger_1.Logger.info("Workspace validation completed", workspaceInfo);
        }
        catch (error) {
            Logger_1.Logger.error("Workspace startup check failed", error);
        }
    }
    /**
     * Handle workspace changes
     */
    handleWorkspaceChange(workspaceInfo) {
        // Update status based on workspace state
        if (!workspaceInfo.hasFlutterProject) {
            this.statusBarManager.setStatus("stopped");
        }
        // Restart preview if it was running and project changed
        if (this.flutterManager.isRunning() && workspaceInfo.flutterProjectPath) {
            Logger_1.Logger.info("Restarting preview due to workspace change");
            this.restartPreviewSafely();
        }
    }
    /**
     * Handle configuration changes
     */
    handleConfigurationChange() {
        Logger_1.Logger.info("Configuration changed, updating components...");
        // Validate configuration
        const errors = this.configurationManager.validateConfiguration();
        if (errors.length > 0) {
            Logger_1.Logger.warn("Configuration validation errors", errors);
            errors.forEach((error) => {
                this.notificationManager.showConfigurationError("general", error);
            });
        }
        // Restart services if needed based on configuration changes
        if (this.flutterManager.isRunning()) {
            Logger_1.Logger.info("Restarting services due to configuration change");
            this.restartPreviewSafely();
        }
    }
    /**
     * Safely restart preview without throwing errors
     */
    async restartPreviewSafely() {
        try {
            if (this.flutterManager.isRunning()) {
                await this.flutterManager.stopFlutterProcess();
            }
            await this.flutterManager.startFlutterProcess();
        }
        catch (error) {
            Logger_1.Logger.error("Failed to restart preview", error);
            this.statusBarManager.setStatus("error");
        }
    }
    /**
     * Dispose all resources
     */
    dispose() {
        Logger_1.Logger.info("Disposing SyncView extension manager...");
        this.disposables.forEach((disposable) => disposable.dispose());
        this.disposables.length = 0;
        this.isActivated = false;
    }
}
exports.ExtensionManager = ExtensionManager;
//# sourceMappingURL=ExtensionManager.js.map