"use strict";exports.id=421,exports.ids=[421],exports.modules={7904:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.fetchHTMLDataProviders=function(e,t){const n=e.map((async e=>{try{return function(e,t){let n;try{n=JSON.parse(t)}catch(t){return(0,o.newHTMLDataProvider)(e,{version:1})}return(0,o.newHTMLDataProvider)(e,{version:n.version||1,tags:n.tags||[],globalAttributes:n.globalAttributes||[],valueSets:n.valueSets||[]})}(e,await t.getContent(e))}catch(t){return(0,o.newHTMLDataProvider)(e,{version:1})}}));return Promise.all(n)};const o=n(4957)},6549:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.startServer=function(e,t){const n=new o.TextDocuments(r.TextDocument);n.listen(e);let P,T,R=[],C=!1,x=!1,w=!1,L=!1,A=Number.MAX_VALUE,O=Number.MAX_VALUE;const I={getContent:t=>e.sendRequest(y.type,t)};let k={},E={};function F(t,n){if(w&&n()){let n=E[t.uri];if(!n){const r=t.uri,i={items:["css","html","javascript","js/ts"].map((e=>({scopeUri:r,section:e})))};n=e.sendRequest(o.ConfigurationRequest.type,i).then((e=>({css:e[0],html:e[1],javascript:e[2],"js/ts":e[3]}))),E[t.uri]=n}return n}return Promise.resolve(void 0)}n.onDidClose((e=>{delete E[e.document.uri]})),e.onInitialize((i=>{const a=i.initializationOptions||{};R=i.workspaceFolders,Array.isArray(R)||(R=[],i.rootPath&&R.push({name:"",uri:c.URI.file(i.rootPath).toString()}));const s=a?.handledSchemas??["file"],u=(0,p.getFileSystemProvider)(s,e,t),g={get settings(){return k},get folders(){return R}};P=(0,r.getLanguageModes)(a?.embeddedLanguages||{css:!0,javascript:!0},g,i.capabilities,u);const f=a?.dataPaths||[];function m(e,t){const n=e.split(".");let o=i.capabilities;for(let e=0;o&&e<n.length;e++){if(!o.hasOwnProperty(n[e]))return t;o=o[n[e]]}return o}(0,d.fetchHTMLDataProviders)(f,I).then((e=>{P.updateDataProviders(e)})),n.onDidClose((e=>{P.onDocumentRemoved(e.document)})),e.onShutdown((()=>{P.dispose()})),C=m("textDocument.completion.completionItem.snippetSupport",!1),x=m("textDocument.rangeFormatting.dynamicRegistration",!1)&&"boolean"!=typeof a?.provideFormatter,w=m("workspace.configuration",!1),L=m("workspace.workspaceFolders",!1),A=m("textDocument.foldingRange.rangeLimit",Number.MAX_VALUE),O=a?.customCapabilities?.rangeFormatting?.editLimit||Number.MAX_VALUE;const h=m("textDocument.diagnostic",void 0);return T=void 0===h?(0,l.registerDiagnosticsPushSupport)(n,e,t,K):(0,l.registerDiagnosticsPullSupport)(n,e,t,K),{capabilities:{textDocumentSync:o.TextDocumentSyncKind.Incremental,completionProvider:C?{resolveProvider:!0,triggerCharacters:[".",":","<",'"',"=","/"]}:void 0,hoverProvider:!0,documentHighlightProvider:!0,documentRangeFormattingProvider:!0===a?.provideFormatter,documentFormattingProvider:!0===a?.provideFormatter,documentLinkProvider:{resolveProvider:!1},documentSymbolProvider:!0,definitionProvider:!0,signatureHelpProvider:{triggerCharacters:["("]},referencesProvider:!0,colorProvider:{},foldingRangeProvider:!0,selectionRangeProvider:!0,renameProvider:!0,linkedEditingRangeProvider:!0,diagnosticProvider:{documentSelector:null,interFileDependencies:!1,workspaceDiagnostics:!1},workspace:{textDocumentContent:{schemes:[r.FILE_PROTOCOL]}}}}})),e.onInitialized((()=>{L&&(e.client.register(o.DidChangeWorkspaceFoldersNotification.type),e.onNotification(o.DidChangeWorkspaceFoldersNotification.type,(e=>{const t=e.event.added,n=e.event.removed,o=[];if(R)for(const e of R)n.some((t=>t.uri===e.uri))||t.some((t=>t.uri===e.uri))||o.push(e);R=o.concat(t),T?.requestRefresh()})))}));let M,j=null;function _(e,t=k){const n=t&&t.html&&t.html.validate;return!n||"css"===e&&!1!==n.styles||"javascript"===e&&!1!==n.scripts}async function K(t){try{const e=t.version,o=[];if("html"===t.languageId){const r=P.getAllModesInDocument(t),i=await F(t,(()=>r.some((e=>!!e.doValidation)))),s=n.get(t.uri);if(s&&s.version===e){for(const e of r)e.doValidation&&_(e.getId(),i)&&(0,a.pushAll)(o,await e.doValidation(s,i));return o}}}catch(n){e.console.error((0,u.formatError)(`Error while validating ${t.uri}`,n))}return[]}async function B(e,t,a){const s=n.get(e.uri);if(s){let e=await F(s,(()=>!0));e||(e=k);const n=e&&e.html&&e.html.format&&e.html.format.unformatted||"",c={css:!n.match(/\bstyle\b/),javascript:!n.match(/\bscript\b/)},u=await(0,i.format)(P,s,t??D(s),a,e,c);if(u.length>O){const e=r.TextDocument.applyEdits(s,u);return[o.TextEdit.replace(D(s),e)]}return u}return[]}function N(){return M||(M=(0,m.newSemanticTokenProvider)(P)),M}e.onDidChangeConfiguration((t=>{if(k=t.settings,E={},T?.requestRefresh(),x)if(k&&k.html&&k.html.format&&k.html.format.enable){if(!j){const t=[{language:"html"},{language:"handlebars"}];j=[e.client.register(o.DocumentRangeFormattingRequest.type,{documentSelector:t}),e.client.register(o.DocumentFormattingRequest.type,{documentSelector:t})]}}else j&&(j.forEach((e=>e.then((e=>e.dispose())))),j=null)})),e.onCompletion((async(e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(!t)return null;const o=P.getModeAtPosition(t,e.position);if(!o||!o.doComplete)return{isIncomplete:!0,items:[]};const r=o.doComplete,i=await F(t,(()=>r.length>2)),a=(0,s.getDocumentContext)(t.uri,R);return r(t,e.position,a,i)}),null,`Error while computing completions for ${e.textDocument.uri}`,o))),e.onCompletionResolve(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=e.data;if((0,r.isCompletionItemData)(t)){const o=P.getMode(t.languageId),r=n.get(t.uri);if(o&&o.doResolve&&r)return o.doResolve(r,e)}return e}),e,"Error while resolving completion proposal",o))),e.onHover(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=P.getModeAtPosition(t,e.position),o=n?.doHover;if(o){const n=await F(t,(()=>o.length>2));return o(t,e.position,n)}}return null}),null,`Error while computing hover for ${e.textDocument.uri}`,o))),e.onDocumentHighlight(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=P.getModeAtPosition(t,e.position);if(n&&n.findDocumentHighlight)return n.findDocumentHighlight(t,e.position)}return[]}),[],`Error while computing document highlights for ${e.textDocument.uri}`,o))),e.onDefinition(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=P.getModeAtPosition(t,e.position);if(n&&n.findDefinition)return n.findDefinition(t,e.position)}return[]}),null,`Error while computing definitions for ${e.textDocument.uri}`,o))),e.onReferences(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=P.getModeAtPosition(t,e.position);if(n&&n.findReferences)return n.findReferences(t,e.position)}return[]}),[],`Error while computing references for ${e.textDocument.uri}`,o))),e.onSignatureHelp(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=P.getModeAtPosition(t,e.position);if(n&&n.doSignatureHelp)return n.doSignatureHelp(t,e.position)}return null}),null,`Error while computing signature help for ${e.textDocument.uri}`,o))),e.onDocumentRangeFormatting(((e,n)=>(0,u.runSafe)(t,(()=>B(e.textDocument,e.range,e.options)),[],`Error while formatting range for ${e.textDocument.uri}`,n))),e.onDocumentFormatting(((e,n)=>(0,u.runSafe)(t,(()=>B(e.textDocument,void 0,e.options)),[],`Error while formatting ${e.textDocument.uri}`,n))),e.onDocumentLinks(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri),o=[];if(t){const e=(0,s.getDocumentContext)(t.uri,R);for(const n of P.getAllModesInDocument(t))n.findDocumentLinks&&(0,a.pushAll)(o,await n.findDocumentLinks(t,e))}return o}),[],`Error while document links for ${e.textDocument.uri}`,o))),e.onDocumentSymbol(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri),o=[];if(t)for(const e of P.getAllModesInDocument(t))e.findDocumentSymbols&&(0,a.pushAll)(o,await e.findDocumentSymbols(t));return o}),[],`Error while computing document symbols for ${e.textDocument.uri}`,o))),e.onRequest(o.DocumentColorRequest.type,((e,o)=>(0,u.runSafe)(t,(async()=>{const t=[],o=n.get(e.textDocument.uri);if(o)for(const e of P.getAllModesInDocument(o))e.findDocumentColors&&(0,a.pushAll)(t,await e.findDocumentColors(o));return t}),[],`Error while computing document colors for ${e.textDocument.uri}`,o))),e.onRequest(o.ColorPresentationRequest.type,((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=P.getModeAtPosition(t,e.range.start);if(n&&n.getColorPresentations)return n.getColorPresentations(t,e.color,e.range)}return[]}),[],`Error while computing color presentations for ${e.textDocument.uri}`,o))),e.onRequest(S.type,((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=e.position;if(n.character>0){const o=P.getModeAtPosition(t,r.Position.create(n.line,n.character-1));if(o&&o.doAutoInsert)return o.doAutoInsert(t,n,e.kind)}}return null}),null,`Error while computing auto insert actions for ${e.textDocument.uri}`,o))),e.onFoldingRanges(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);return t?(0,g.getFoldingRanges)(P,t,A,o):null}),null,`Error while computing folding regions for ${e.textDocument.uri}`,o))),e.onSelectionRanges(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);return t?(0,f.getSelectionRanges)(P,t,e.positions):[]}),[],`Error while computing selection ranges for ${e.textDocument.uri}`,o))),e.onRenameRequest(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri),o=e.position;if(t){const n=P.getModeAtPosition(t,e.position);if(n&&n.doRename)return n.doRename(t,o,e.newName)}return null}),null,`Error while computing rename for ${e.textDocument.uri}`,o))),e.languages.onLinkedEditingRange(((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);if(t){const n=e.position;if(n.character>0){const e=P.getModeAtPosition(t,r.Position.create(n.line,n.character-1));if(e&&e.doLinkedEditing){const o=await e.doLinkedEditing(t,n);if(o)return{ranges:o}}}}return null}),null,`Error while computing synced regions for ${e.textDocument.uri}`,o))),e.onRequest(b.type,((e,o)=>(0,u.runSafe)(t,(async()=>{const t=n.get(e.textDocument.uri);return t?N().getSemanticTokens(t,e.ranges):null}),null,`Error while computing semantic tokens for ${e.textDocument.uri}`,o))),e.onRequest(v.type,(e=>(0,u.runSafe)(t,(async()=>N().legend),null,"Error while computing semantic tokens legend",e))),e.onNotification(h.type,(e=>{(0,d.fetchHTMLDataProviders)(e,I).then((e=>{P.updateDataProviders(e)}))})),e.onRequest(o.TextDocumentContentRequest.type,((e,n)=>(0,u.runSafe)(t,(async()=>{for(const t of P.getAllModes()){const n=await(t.getTextDocumentContent?.(e.uri));if(n)return{text:n}}return null}),null,`Error while computing text document content for ${e.uri}`,n))),e.listen()};const o=n(2861),r=n(8618),i=n(3345),a=n(1139),s=n(6529),c=n(7608),u=n(211),l=n(9178),g=n(4986),d=n(7904),f=n(7764),m=n(2048),p=n(6149);var h,y,S,b,v;function D(e){return r.Range.create(r.Position.create(0,0),e.positionAt(e.getText().length))}!function(e){e.type=new o.NotificationType("html/customDataChanged")}(h||(h={})),function(e){e.type=new o.RequestType("html/customDataContent")}(y||(y={})),function(e){e.type=new o.RequestType("html/autoInsert")}(S||(S={})),function(e){e.type=new o.RequestType("html/semanticTokens")}(b||(b={})),function(e){e.type=new o.RequestType0("html/semanticTokenLegend")}(v||(v={}))},5908:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getLanguageModelCache=function(e,t,n){let o,r={},i=0;return t>0&&(o=setInterval((()=>{const e=Date.now()-1e3*t,n=Object.keys(r);for(const t of n)r[t].cTime<e&&(delete r[t],i--)}),1e3*t)),{get(t){const o=t.version,a=t.languageId,s=r[t.uri];if(s&&s.version===o&&s.languageId===a)return s.cTime=Date.now(),s.languageModel;const c=n(t);if(r[t.uri]={languageModel:c,version:o,languageId:a,cTime:Date.now()},s||i++,i===e){let e=Number.MAX_VALUE,t=null;for(const n in r){const o=r[n];o.cTime<e&&(t=n,e=o.cTime)}t&&(delete r[t],i--)}return c},onDocumentRemoved(e){const t=e.uri;r[t]&&(delete r[t],i--)},dispose(){void 0!==o&&(clearInterval(o),o=void 0,r={},i=0)}}}},1212:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getCSSMode=function(e,t,n){const a=(0,o.getLanguageModelCache)(10,60,(e=>t.get(e).getEmbeddedDocument("css"))),s=(0,o.getLanguageModelCache)(10,60,(t=>e.parseStylesheet(t)));return{getId:()=>"css",async doValidation(t,o=n.settings){const r=a.get(t);return e.doValidation(r,s.get(r),o&&o.css)},async doComplete(t,o,i,c=n.settings){const u=a.get(t),l=s.get(u);return e.doComplete2(u,o,l,i,c?.css?.completion)||r.CompletionList.create()},async doHover(t,o,r=n.settings){const i=a.get(t);return e.doHover(i,o,s.get(i),r?.css?.hover)},async findDocumentHighlight(t,n){const o=a.get(t);return e.findDocumentHighlights(o,n,s.get(o))},async findDocumentSymbols(t){const n=a.get(t);return e.findDocumentSymbols(n,s.get(n)).filter((e=>e.name!==i.CSS_STYLE_RULE))},async findDefinition(t,n){const o=a.get(t);return e.findDefinition(o,n,s.get(o))},async findReferences(t,n){const o=a.get(t);return e.findReferences(o,n,s.get(o))},async findDocumentColors(t){const n=a.get(t);return e.findDocumentColors(n,s.get(n))},async getColorPresentations(t,n,o){const r=a.get(t);return e.getColorPresentations(r,s.get(r),n,o)},async getFoldingRanges(t){const n=a.get(t);return e.getFoldingRanges(n,{})},async getSelectionRange(t,n){const o=a.get(t);return e.getSelectionRanges(o,[n],s.get(o))[0]},onDocumentRemoved(e){a.onDocumentRemoved(e),s.onDocumentRemoved(e)},dispose(){a.dispose(),s.dispose()}}};const o=n(5908),r=n(8618),i=n(1157)},1157:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CSS_STYLE_RULE=void 0,t.getDocumentRegions=function(e,t){const n=[],u=e.createScanner(t.getText());let l,g="",d=null;const f=[];let m=u.scan();for(;m!==o.TokenType.EOS;){switch(m){case o.TokenType.StartTag:g=u.getTokenText(),d=null,l="javascript";break;case o.TokenType.Styles:n.push({languageId:"css",start:u.getTokenOffset(),end:u.getTokenEnd()});break;case o.TokenType.Script:n.push({languageId:l,start:u.getTokenOffset(),end:u.getTokenEnd()});break;case o.TokenType.AttributeName:d=u.getTokenText();break;case o.TokenType.AttributeValue:if("src"===d&&"script"===g.toLowerCase()){let e=u.getTokenText();"'"!==e[0]&&'"'!==e[0]||(e=e.substr(1,e.length-1)),f.push(e)}else if("type"===d&&"script"===g.toLowerCase()){const e=u.getTokenText();l=/["'](module|(text|application)\/(java|ecma)script|text\/babel)["']/.test(e)||"module"===e?"javascript":/["']text\/typescript["']/.test(e)?"typescript":void 0}else{const e=c(d);if(e){let o=u.getTokenOffset(),r=u.getTokenEnd();const i=t.getText()[o];"'"!==i&&'"'!==i||(o++,r--),n.push({languageId:e,start:o,end:r,attributeValue:!0})}}d=null}m=u.scan()}return{getLanguageRanges:e=>function(e,t,n){const r=[];let i=n?n.start:o.Position.create(0,0),a=n?e.offsetAt(n.start):0;const s=n?e.offsetAt(n.end):e.getText().length;for(const n of t)if(n.end>a&&n.start<s){const t=Math.max(n.start,a),o=e.positionAt(t);a<n.start&&r.push({start:i,end:o,languageId:"html"});const c=Math.min(n.end,s),u=e.positionAt(c);c>n.start&&r.push({start:o,end:u,languageId:n.languageId,attributeValue:n.attributeValue}),a=c,i=u}if(a<s){const t=n?n.end:e.positionAt(s);r.push({start:i,end:t,languageId:"html"})}return r}(t,n,e),getEmbeddedDocument:(e,c)=>function(e,t,n,c){let u=0;const l=e.getText();let g="",d="";for(const e of t)e.languageId!==n||c&&e.attributeValue||(g=s(g,u,e.start,l,d,r(e)),g+=a(e,l.substring(e.start,e.end)),u=e.end,d=i(e));return g=s(g,u,l.length,l,d,""),o.TextDocument.create(e.uri,n,e.version,g)}(t,n,e,c),getLanguageAtPosition:e=>function(e,t,n){const o=e.offsetAt(n);for(const e of t){if(!(e.start<=o))break;if(o<=e.end)return e.languageId}return"html"}(t,n,e),getLanguagesInDocument:()=>function(e,t){const n=[];for(const e of t)if(e.languageId&&-1===n.indexOf(e.languageId)&&(n.push(e.languageId),3===n.length))return n;return n.push("html"),n}(0,n),getImportedScripts:()=>f}};const o=n(8618);function r(e){return e.attributeValue&&"css"===e.languageId?t.CSS_STYLE_RULE+"{":""}function i(e){if(e.attributeValue)switch(e.languageId){case"css":return"}";case"javascript":return";"}return""}function a(e,t){if(!e.attributeValue&&"javascript"===e.languageId)return t.replace("\x3c!--","/* ").replace("--\x3e"," */");if("css"===e.languageId){const e=/(&quot;|&#34;)/g;return t.replace(e,((e,n,o)=>{const r=" ".repeat(e.length-1),i=t[o+e.length];return!i||i.includes(" ")?`${r}"`:`"${r}`}))}return t}function s(e,t,n,o,r,i){e+=r;let a=-r.length;for(let r=t;r<n;r++){const t=o[r];"\n"===t||"\r"===t?(a=0,e+=t):a++}return(e=function(e,t,n){for(;n>0;)1&n&&(e+=t),n>>=1,t+=t;return e}(e," ",a-i.length))+i}function c(e){const t=e.match(/^(style)$|^(on\w+)$/i);return t?t[1]?"css":"javascript":null}t.CSS_STYLE_RULE="__"},3345:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.format=async function(e,t,n,a,s,c){const u=[],l=n.end;let g=t.offsetAt(l);const d=t.getText();if(0===l.character&&l.line>0&&g!==d.length){const e=t.offsetAt(o.Position.create(l.line-1,0));for(;(0,i.isEOL)(d,g-1)&&g>e;)g--;n=o.Range.create(n.start,t.positionAt(g))}const f=e.getModesInRange(t,n);let m=0,p=n.start;for(;m<f.length&&(!(h=f[m]).mode||"html"!==h.mode.getId());){const e=f[m];if(!e.attributeValue&&e.mode&&e.mode.format){const n=await e.mode.format(t,o.Range.create(p,e.end),a,s);(0,r.pushAll)(u,n)}p=e.end,m++}var h;if(m===f.length)return u;n=o.Range.create(p,n.end);const y=e.getMode("html"),S=await y.format(t,n,a,s);let b=o.TextDocument.applyEdits(t,S);a.insertFinalNewline&&g===d.length&&!b.endsWith("\n")&&(b+="\n",S.push(o.TextEdit.insert(l,"\n")));const v=o.TextDocument.create(t.uri+".tmp",t.languageId,t.version,b);try{const i=t.getText().length-t.offsetAt(n.end),l=o.Range.create(n.start,v.positionAt(b.length-i)),g=e.getModesInRange(v,l),d=[];for(const e of g){const t=e.mode;if(t&&t.format&&c[t.getId()]&&!e.attributeValue){const n=await t.format(v,e,a,s);for(const e of n)d.push(e)}}if(0===d.length)return(0,r.pushAll)(u,S),u;const f=o.TextDocument.applyEdits(v,d),m=f.substring(t.offsetAt(n.start),f.length-i);return u.push(o.TextEdit.replace(n,m)),u}finally{e.onDocumentRemoved(v)}};const o=n(8618),r=n(1139),i=n(247)},4986:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getFoldingRanges=async function(e,t,n,r){const i=e.getMode("html"),a=o.Range.create(o.Position.create(0,0),o.Position.create(t.lineCount,0));let s=[];i&&i.getFoldingRanges&&s.push(...await i.getFoldingRanges(t));const c=Object.create(null),u=async e=>{if(e.getFoldingRanges){let n=c[e.getId()];return Array.isArray(n)||(n=await e.getFoldingRanges(t)||[],c[e.getId()]=n),n}return[]},l=e.getModesInRange(t,a);for(const e of l){const t=e.mode;if(t&&t!==i&&!e.attributeValue){const n=await u(t);s.push(...n.filter((t=>t.startLine>=e.start.line&&t.endLine<e.end.line)))}}return n&&s.length>n&&(s=function(e,t){let n;e=e.sort(((e,t)=>{let n=e.startLine-t.startLine;return 0===n&&(n=e.endLine-t.endLine),n}));const o=[],r=[],i=[],a=(e,t)=>{r[e]=t,t<30&&(i[t]=(i[t]||0)+1)};for(let t=0;t<e.length;t++){const r=e[t];if(n){if(r.startLine>n.startLine)if(r.endLine<=n.endLine)o.push(n),n=r,a(t,o.length);else if(r.startLine>n.endLine){do{n=o.pop()}while(n&&r.startLine>n.endLine);n&&o.push(n),n=r,a(t,o.length)}}else n=r,a(t,0)}let s=0,c=0;for(let e=0;e<i.length;e++){const n=i[e];if(n){if(n+s>t){c=e;break}s+=n}}const u=[];for(let n=0;n<e.length;n++){const o=r[n];"number"==typeof o&&(o<c||o===c&&s++<t)&&u.push(e[n])}return u}(s,n)),s};const o=n(8618)},252:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getHTMLMode=function(e,t){const n=(0,o.getLanguageModelCache)(10,60,(t=>e.parseHTMLDocument(t)));return{getId:()=>"html",getSelectionRange:async(t,n)=>e.getSelectionRanges(t,[n])[0],doComplete(o,i,a,s=t.settings){const c=s?.html,u=r(c?.suggest,{});u.hideAutoCompleteProposals=!0===c?.autoClosingTags,u.attributeDefaultValue=c?.completion?.attributeDefaultValue??"doublequotes";const l=n.get(o);return e.doComplete2(o,i,l,a,u)},doHover:async(t,o,r)=>e.doHover(t,o,n.get(t),r?.html?.hover),findDocumentHighlight:async(t,o)=>e.findDocumentHighlights(t,o,n.get(t)),findDocumentLinks:async(t,n)=>e.findDocumentLinks(t,n),findDocumentSymbols:async t=>e.findDocumentSymbols(t,n.get(t)),async format(n,o,i,a=t.settings){const s=r(a?.html?.format,{});return s.contentUnformatted?s.contentUnformatted=s.contentUnformatted+",script":s.contentUnformatted="script",r(i,s),e.format(n,o,s)},getFoldingRanges:async t=>e.getFoldingRanges(t),async doAutoInsert(o,i,a,s=t.settings){const c=o.offsetAt(i),u=o.getText();if("autoQuote"===a){if(c>0&&"="===u.charAt(c-1)){const t=s?.html,a=r(t?.suggest,{});return a.attributeDefaultValue=t?.completion?.attributeDefaultValue??"doublequotes",e.doQuoteComplete(o,i,n.get(o),a)}}else if("autoClose"===a&&c>0&&u.charAt(c-1).match(/[>\/]/g))return e.doTagComplete(o,i,n.get(o));return null},async doRename(t,o,r){const i=n.get(t);return e.doRename(t,o,r,i)},async onDocumentRemoved(e){n.onDocumentRemoved(e)},async findMatchingTagPosition(t,o){const r=n.get(t);return e.findMatchingTagPosition(t,o,r)},async doLinkedEditing(t,o){const r=n.get(t);return e.findLinkedEditingRanges(t,o,r)},dispose(){n.dispose()}}};const o=n(5908);function r(e,t){if(e)for(const n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}},2854:function(e,t,n){var o,r=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},o(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=o(e),a=0;a<n.length;a++)"default"!==n[a]&&r(t,e,n[a]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getJavaScriptMode=function(e,t,o){const r=(0,s.getLanguageModelCache)(10,60,(n=>e.get(n).getEmbeddedDocument(t))),i=function(e){const t={allowNonTsExtensions:!0,allowJs:!0,lib:["lib.es2020.full.d.ts"],target:l.ScriptTarget.Latest,moduleResolution:l.ModuleResolutionKind.Classic,experimentalDecorators:!1};let o=c.TextDocument.create("init","javascript",1,"");const r=n.e(490).then(n.bind(n,1609)).then((n=>{const r={getCompilationSettings:()=>t,getScriptFileNames:()=>[o.uri,"jquery"],getScriptKind:t=>t===o.uri?e:"ts"===t.substr(t.length-2)?l.ScriptKind.TS:l.ScriptKind.JS,getScriptVersion:e=>e===o.uri?String(o.version):"1",getScriptSnapshot:e=>{let t="";return t=e===o.uri?o.getText():n.loadLibrary(e),{getText:(e,n)=>t.substring(e,n),getLength:()=>t.length,getChangeRange:()=>{}}},getCurrentDirectory:()=>"",getDefaultLibFileName:e=>"es2020.full",readFile:(e,t)=>e===o.uri?o.getText():n.loadLibrary(e),fileExists:e=>e===o.uri||!!n.loadLibrary(e),directoryExists:e=>!e.startsWith("node_modules")};return{service:l.createLanguageService(r),loadLibrary:n.loadLibrary}}));return{getLanguageService:async e=>(o=e,(await r).service),getCompilationSettings:()=>t,loadLibrary:async e=>(await r).loadLibrary(e),dispose(){r.then((e=>e.service.dispose()))}}}("javascript"===t?l.ScriptKind.JS:l.ScriptKind.TS),a={},y=`${c.FILE_PROTOCOL}://${t}/libs/`;return{getId:()=>t,async doValidation(e,n=o.settings){!function(e){const t=i.getCompilationSettings();t.experimentalDecorators=e?.["js/ts"]?.implicitProjectConfig?.experimentalDecorators,t.strictNullChecks=e?.["js/ts"]?.implicitProjectConfig.strictNullChecks}(n);const a=r.get(e),s=await i.getLanguageService(a),u=s.getSyntacticDiagnostics(a.uri),g=s.getSemanticDiagnostics(a.uri);return u.concat(g).filter((e=>!f.includes(e.code))).map((e=>({range:m(a,e),severity:c.DiagnosticSeverity.Error,source:t,message:l.flattenDiagnosticMessageText(e.messageText,"\n")})))},async doComplete(e,n,o){const a=r.get(e),s=await i.getLanguageService(a),l=a.offsetAt(n),g=s.getCompletionsAtPosition(a.uri,l,{includeExternalModuleExports:!1,includeInsertTextCompletions:!1});if(!g)return{isIncomplete:!1,items:[]};const f=m(a,(0,u.getWordAtText)(a.getText(),l,d));return{isIncomplete:!1,items:g.entries.map((o=>{const r={languageId:t,uri:e.uri,offset:l};return{uri:e.uri,position:n,label:o.name,sortText:o.sortText,kind:p(o.kind),textEdit:c.TextEdit.replace(f,o.name),data:r}}))}},async doResolve(e,t){if((0,c.isCompletionItemData)(t.data)){const n=r.get(e),o=(await i.getLanguageService(n)).getCompletionEntryDetails(n.uri,t.data.offset,t.label,void 0,void 0,void 0,void 0);o&&(t.detail=l.displayPartsToString(o.displayParts),t.documentation=l.displayPartsToString(o.documentation),delete t.data)}return t},async doHover(e,t){const n=r.get(e),o=(await i.getLanguageService(n)).getQuickInfoAtPosition(n.uri,n.offsetAt(t));if(o){const e=l.displayPartsToString(o.displayParts);return{range:m(n,o.textSpan),contents:["```typescript",e,"```"].join("\n")}}return null},async doSignatureHelp(e,t){const n=r.get(e),o=(await i.getLanguageService(n)).getSignatureHelpItems(n.uri,n.offsetAt(t),void 0);if(o){const e={activeSignature:o.selectedItemIndex,activeParameter:o.argumentIndex,signatures:[]};return o.items.forEach((t=>{const n={label:"",documentation:void 0,parameters:[]};n.label+=l.displayPartsToString(t.prefixDisplayParts),t.parameters.forEach(((e,o,r)=>{const i=l.displayPartsToString(e.displayParts),a={label:i,documentation:l.displayPartsToString(e.documentation)};n.label+=i,n.parameters.push(a),o<r.length-1&&(n.label+=l.displayPartsToString(t.separatorDisplayParts))})),n.label+=l.displayPartsToString(t.suffixDisplayParts),e.signatures.push(n)})),e}return null},async doRename(e,t,n){const o=r.get(e),a=await i.getLanguageService(o),s=o.offsetAt(t),{canRename:c}=a.getRenameInfo(o.uri,s);if(!c)return null;const u=a.findRenameLocations(o.uri,s,!1,!1),l=[];return u?.map((e=>{l.push({range:m(o,e.textSpan),newText:n})})),{changes:{[e.uri]:l}}},async findDocumentHighlight(e,t){const n=r.get(e),o=(await i.getLanguageService(n)).getDocumentHighlights(n.uri,n.offsetAt(t),[n.uri]),a=[];for(const e of o||[])for(const t of e.highlightSpans)a.push({range:m(n,t.textSpan),kind:"writtenReference"===t.kind?c.DocumentHighlightKind.Write:c.DocumentHighlightKind.Text});return a},async findDocumentSymbols(e){const t=r.get(e),n=(await i.getLanguageService(t)).getNavigationBarItems(t.uri);if(n){const o=[],r=Object.create(null),i=(n,a)=>{const s=n.text+n.kind+n.spans[0].start;if("script"!==n.kind&&!r[s]){const i={name:n.text,kind:h(n.kind),location:{uri:e.uri,range:m(t,n.spans[0])},containerName:a};r[s]=!0,o.push(i),a=n.text}if(n.childItems&&n.childItems.length>0)for(const e of n.childItems)i(e,a)};return n.forEach((e=>i(e))),o}return[]},async findDefinition(e,n){const o=r.get(e),a=(await i.getLanguageService(o)).getDefinitionAtPosition(o.uri,o.offsetAt(n));return a?(await Promise.all(a.map((async n=>{if(n.fileName===o.uri)return{uri:e.uri,range:m(o,n.textSpan)};{const e=y+n.fileName,o=await i.loadLibrary(n.fileName);if(!o)return;return{uri:e,range:m(c.TextDocument.create(e,t,1,o),n.textSpan)}}})))).filter((e=>!!e)):null},async findReferences(e,t){const n=r.get(e),o=(await i.getLanguageService(n)).getReferencesAtPosition(n.uri,n.offsetAt(t));return o?o.filter((e=>e.fileName===n.uri)).map((t=>({uri:e.uri,range:m(n,t.textSpan)}))):[]},async getSelectionRange(e,t){const n=r.get(e);return function e(t){const o=t.parent?e(t.parent):void 0;return c.SelectionRange.create(m(n,t.textSpan),o)}((await i.getLanguageService(n)).getSmartSelectionRange(n.uri,n.offsetAt(t)))},async format(t,n,o,r=a){const s=e.get(t).getEmbeddedDocument("javascript",!0),g=await i.getLanguageService(s),d=r&&r.javascript&&r.javascript.format,f=function(e,t,n){const o=e.offsetAt(c.Position.create(t.start.line,0)),r=e.getText();let i=o,a=0;const s=n.tabSize||4;for(;i<r.length;){const e=r.charAt(i);if(" "===e)a++;else{if("\t"!==e)break;a+=s}i++}return Math.floor(a/s)}(t,n,o),p=function(e,t,n){return{convertTabsToSpaces:e.insertSpaces,tabSize:e.tabSize,indentSize:e.tabSize,indentStyle:l.IndentStyle.Smart,newLineCharacter:"\n",baseIndentSize:e.tabSize*n,insertSpaceAfterCommaDelimiter:Boolean(!t||t.insertSpaceAfterCommaDelimiter),insertSpaceAfterConstructor:Boolean(t&&t.insertSpaceAfterConstructor),insertSpaceAfterSemicolonInForStatements:Boolean(!t||t.insertSpaceAfterSemicolonInForStatements),insertSpaceBeforeAndAfterBinaryOperators:Boolean(!t||t.insertSpaceBeforeAndAfterBinaryOperators),insertSpaceAfterKeywordsInControlFlowStatements:Boolean(!t||t.insertSpaceAfterKeywordsInControlFlowStatements),insertSpaceAfterFunctionKeywordForAnonymousFunctions:Boolean(!t||t.insertSpaceAfterFunctionKeywordForAnonymousFunctions),insertSpaceBeforeFunctionParenthesis:Boolean(t&&t.insertSpaceBeforeFunctionParenthesis),insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:Boolean(t&&t.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis),insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:Boolean(t&&t.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets),insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces:Boolean(t&&t.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces),insertSpaceAfterOpeningAndBeforeClosingEmptyBraces:Boolean(!t||t.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces),insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:Boolean(t&&t.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces),insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces:Boolean(t&&t.insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces),insertSpaceAfterTypeAssertion:Boolean(t&&t.insertSpaceAfterTypeAssertion),placeOpenBraceOnNewLineForControlBlocks:Boolean(t&&t.placeOpenBraceOnNewLineForFunctions),placeOpenBraceOnNewLineForFunctions:Boolean(t&&t.placeOpenBraceOnNewLineForControlBlocks),semicolons:t?.semicolons}}(o,d,f+1),h=s.offsetAt(n.start);let y=s.offsetAt(n.end),S=null;n.end.line>n.start.line&&(0===n.end.character||(0,u.isWhitespaceOnly)(s.getText().substr(y-n.end.character,n.end.character)))&&(y-=n.end.character,S=c.Range.create(c.Position.create(n.end.line,0),n.end));const b=g.getFormattingEditsForRange(s.uri,h,y,p);if(b){const e=[];for(const t of b)t.span.start>=h&&t.span.start+t.span.length<=y&&e.push({range:m(s,t.span),newText:t.newText});return S&&e.push({range:S,newText:(v=f,D=o,D.insertSpaces?(0,u.repeat)(" ",v*D.tabSize):(0,u.repeat)("\t",v))}),e}var v,D;return[]},async getFoldingRanges(e){const t=r.get(e),n=(await i.getLanguageService(t)).getOutliningSpans(t.uri),o=[];for(const r of n){const n=m(t,r.textSpan),i=n.start.line,a=n.end.line;if(i<a){const t={startLine:i,endLine:a},r=e.getText(n).match(/^\s*\/(?:(\/\s*#(?:end)?region\b)|(\*|\/))/);r&&(t.kind=r[1]?c.FoldingRangeKind.Region:c.FoldingRangeKind.Comment),o.push(t)}}return o},onDocumentRemoved(e){r.onDocumentRemoved(e)},async getSemanticTokens(e){const t=r.get(e),n=await i.getLanguageService(t);return[...(0,g.getSemanticTokens)(n,t,t.uri)]},getSemanticTokenLegend:()=>(0,g.getSemanticTokenLegend)(),async getTextDocumentContent(e){if(e.startsWith(y))return i.loadLibrary(e.substring(y.length))},dispose(){i.dispose(),r.dispose()}}};const s=n(5908),c=n(8618),u=n(247),l=a(n(9899)),g=n(6539),d=/(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,f=[1108,2792];function m(e,t){if(void 0===t.start){const t=e.positionAt(0);return c.Range.create(t,t)}const n=e.positionAt(t.start),o=e.positionAt(t.start+(t.length||0));return c.Range.create(n,o)}function p(e){switch(e){case"primitive type":case"keyword":return c.CompletionItemKind.Keyword;case"const":case"let":case"var":case"local var":case"alias":case"parameter":return c.CompletionItemKind.Variable;case"property":case"getter":case"setter":return c.CompletionItemKind.Field;case"function":case"local function":return c.CompletionItemKind.Function;case"method":case"construct":case"call":case"index":return c.CompletionItemKind.Method;case"enum":return c.CompletionItemKind.Enum;case"enum member":return c.CompletionItemKind.EnumMember;case"module":case"external module name":return c.CompletionItemKind.Module;case"class":case"type":return c.CompletionItemKind.Class;case"interface":return c.CompletionItemKind.Interface;case"warning":return c.CompletionItemKind.Text;case"script":return c.CompletionItemKind.File;case"directory":return c.CompletionItemKind.Folder;case"string":return c.CompletionItemKind.Constant;default:return c.CompletionItemKind.Property}}function h(e){switch(e){case"module":return c.SymbolKind.Module;case"class":return c.SymbolKind.Class;case"enum":return c.SymbolKind.Enum;case"enum member":return c.SymbolKind.EnumMember;case"interface":return c.SymbolKind.Interface;case"index":case"call":case"method":return c.SymbolKind.Method;case"property":case"getter":case"setter":return c.SymbolKind.Property;case"var":case"let":case"const":case"local var":case"alias":default:return c.SymbolKind.Variable;case"function":case"local function":return c.SymbolKind.Function;case"construct":case"constructor":return c.SymbolKind.Constructor;case"type parameter":return c.SymbolKind.TypeParameter;case"string":return c.SymbolKind.String}}},6539:(e,t)=>{function n(e){if(e>255)return(e>>8)-1}function o(e){return 255&e}Object.defineProperty(t,"__esModule",{value:!0}),t.getSemanticTokenLegend=function(){return 12!==r.length&&console.warn("TokenType has added new entries."),6!==i.length&&console.warn("TokenModifier has added new entries."),{types:r,modifiers:i}},t.getSemanticTokens=function*(e,t,r){const{spans:i}=e.getEncodedSemanticClassifications(r,{start:0,length:t.getText().length},"2020");for(let e=0;e<i.length;){const r=i[e++],a=i[e++],s=i[e++],c=n(s);if(void 0===c)continue;const u=o(s),l=t.positionAt(r);yield{start:l,length:a,typeIdx:c,modifierSet:u}}};const r=[];r[0]="class",r[1]="enum",r[2]="interface",r[3]="namespace",r[4]="typeParameter",r[5]="type",r[6]="parameter",r[7]="variable",r[8]="enumMember",r[9]="property",r[10]="function",r[11]="method";const i=[];i[2]="async",i[0]="declaration",i[3]="readonly",i[1]="static",i[5]="local",i[4]="defaultLibrary"},8618:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FILE_PROTOCOL=t.TextDocument=t.TokenType=t.ClientCapabilities=t.TextDocumentIdentifier=t.SelectionRange=t.DiagnosticSeverity=t.ParameterInformation=t.SignatureInformation=t.WorkspaceEdit=t.ColorPresentation=t.ColorInformation=t.Color=t.TextEdit=t.SymbolKind=t.SymbolInformation=t.Range=t.Position=t.Location=t.Hover=t.FormattingOptions=t.FoldingRangeKind=t.FoldingRange=t.DocumentLink=t.DocumentHighlightKind=t.DocumentHighlight=t.Diagnostic=t.CompletionItemKind=t.CompletionList=t.CompletionItem=t.WorkspaceFolder=void 0,t.isCompletionItemData=function(e){return e&&"string"==typeof e.languageId&&"string"==typeof e.uri&&"number"==typeof e.offset},t.getLanguageModes=function(e,t,n,l){const g=(0,r.getLanguageService)({clientCapabilities:n,fileSystemProvider:l}),d=(0,o.getCSSLanguageService)({clientCapabilities:n,fileSystemProvider:l}),f=(0,i.getLanguageModelCache)(10,60,(e=>(0,s.getDocumentRegions)(g,e)));let m=[];m.push(f);let p=Object.create(null);return p.html=(0,c.getHTMLMode)(g,t),e.css&&(p.css=(0,a.getCSSMode)(d,f,t)),e.javascript&&(p.javascript=(0,u.getJavaScriptMode)(f,"javascript",t),p.typescript=(0,u.getJavaScriptMode)(f,"typescript",t)),{async updateDataProviders(e){g.setDataProviders(!0,e)},getModeAtPosition(e,t){const n=f.get(e).getLanguageAtPosition(t);if(n)return p[n]},getModesInRange:(e,t)=>f.get(e).getLanguageRanges(t).map((e=>({start:e.start,end:e.end,mode:e.languageId&&p[e.languageId],attributeValue:e.attributeValue}))),getAllModesInDocument(e){const t=[];for(const n of f.get(e).getLanguagesInDocument()){const e=p[n];e&&t.push(e)}return t},getAllModes(){const e=[];for(const t in p){const n=p[t];n&&e.push(n)}return e},getMode:e=>p[e],onDocumentRemoved(e){m.forEach((t=>t.onDocumentRemoved(e)));for(const t in p)p[t].onDocumentRemoved(e)},dispose(){m.forEach((e=>e.dispose())),m=[];for(const e in p)p[e].dispose();p={}}}};const o=n(3815),r=n(4957),i=n(5908),a=n(1212),s=n(1157),c=n(252),u=n(2854);var l=n(2861);Object.defineProperty(t,"WorkspaceFolder",{enumerable:!0,get:function(){return l.WorkspaceFolder}}),Object.defineProperty(t,"CompletionItem",{enumerable:!0,get:function(){return l.CompletionItem}}),Object.defineProperty(t,"CompletionList",{enumerable:!0,get:function(){return l.CompletionList}}),Object.defineProperty(t,"CompletionItemKind",{enumerable:!0,get:function(){return l.CompletionItemKind}}),Object.defineProperty(t,"Diagnostic",{enumerable:!0,get:function(){return l.Diagnostic}}),Object.defineProperty(t,"DocumentHighlight",{enumerable:!0,get:function(){return l.DocumentHighlight}}),Object.defineProperty(t,"DocumentHighlightKind",{enumerable:!0,get:function(){return l.DocumentHighlightKind}}),Object.defineProperty(t,"DocumentLink",{enumerable:!0,get:function(){return l.DocumentLink}}),Object.defineProperty(t,"FoldingRange",{enumerable:!0,get:function(){return l.FoldingRange}}),Object.defineProperty(t,"FoldingRangeKind",{enumerable:!0,get:function(){return l.FoldingRangeKind}}),Object.defineProperty(t,"FormattingOptions",{enumerable:!0,get:function(){return l.FormattingOptions}}),Object.defineProperty(t,"Hover",{enumerable:!0,get:function(){return l.Hover}}),Object.defineProperty(t,"Location",{enumerable:!0,get:function(){return l.Location}}),Object.defineProperty(t,"Position",{enumerable:!0,get:function(){return l.Position}}),Object.defineProperty(t,"Range",{enumerable:!0,get:function(){return l.Range}}),Object.defineProperty(t,"SymbolInformation",{enumerable:!0,get:function(){return l.SymbolInformation}}),Object.defineProperty(t,"SymbolKind",{enumerable:!0,get:function(){return l.SymbolKind}}),Object.defineProperty(t,"TextEdit",{enumerable:!0,get:function(){return l.TextEdit}}),Object.defineProperty(t,"Color",{enumerable:!0,get:function(){return l.Color}}),Object.defineProperty(t,"ColorInformation",{enumerable:!0,get:function(){return l.ColorInformation}}),Object.defineProperty(t,"ColorPresentation",{enumerable:!0,get:function(){return l.ColorPresentation}}),Object.defineProperty(t,"WorkspaceEdit",{enumerable:!0,get:function(){return l.WorkspaceEdit}}),Object.defineProperty(t,"SignatureInformation",{enumerable:!0,get:function(){return l.SignatureInformation}}),Object.defineProperty(t,"ParameterInformation",{enumerable:!0,get:function(){return l.ParameterInformation}}),Object.defineProperty(t,"DiagnosticSeverity",{enumerable:!0,get:function(){return l.DiagnosticSeverity}}),Object.defineProperty(t,"SelectionRange",{enumerable:!0,get:function(){return l.SelectionRange}}),Object.defineProperty(t,"TextDocumentIdentifier",{enumerable:!0,get:function(){return l.TextDocumentIdentifier}});var g=n(4957);Object.defineProperty(t,"ClientCapabilities",{enumerable:!0,get:function(){return g.ClientCapabilities}}),Object.defineProperty(t,"TokenType",{enumerable:!0,get:function(){return g.TokenType}});var d=n(5172);Object.defineProperty(t,"TextDocument",{enumerable:!0,get:function(){return d.TextDocument}}),t.FILE_PROTOCOL="html-server"},7764:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSelectionRanges=async function(e,t,n){const i=e.getMode("html");return Promise.all(n.map((async n=>{const a=await i.getSelectionRange(t,n),s=e.getModeAtPosition(t,n);if(s&&s.getSelectionRange){const e=await s.getSelectionRange(t,n);let o=e;for(;o.parent&&(0,r.insideRangeButNotSame)(a.range,o.parent.range);)o=o.parent;return o.parent=a,e}return a||o.SelectionRange.create(o.Range.create(n,n))})))};const o=n(8618),r=n(4705)},2048:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.newSemanticTokenProvider=function(e){const t={types:[],modifiers:[]},n={};for(const o of e.getAllModes())if(o.getSemanticTokenLegend&&o.getSemanticTokens){const e=o.getSemanticTokenLegend();n[o.getId()]={types:i(e.types,t.types),modifiers:i(e.modifiers,t.modifiers)}}return{legend:t,async getSemanticTokens(t,i){const c=[];for(const o of e.getAllModesInDocument(t))if(o.getSemanticTokens){const e=n[o.getId()],r=await o.getSemanticTokens(t);a(r,e.types),s(r,e.modifiers);for(const e of r)c.push(e)}return function(e,t,n){const i=e.sort(((e,t)=>e.start.line-t.start.line||e.start.character-t.start.character));let a=0,s=(t=t?t.sort(((e,t)=>e.start.line-t.start.line||e.start.character-t.start.character)):[o.Range.create(o.Position.create(0,0),o.Position.create(n.lineCount,0))])[a++],c=0,u=0;const l=[];for(let e=0;e<i.length&&s;e++){const n=i[e],o=n.start;for(;s&&(0,r.beforeOrSame)(s.end,o);)s=t[a++];s&&(0,r.beforeOrSame)(s.start,o)&&(0,r.beforeOrSame)({line:o.line,character:o.character+n.length},s.end)&&(c!==o.line&&(u=0),l.push(o.line-c),l.push(o.character-u),l.push(n.length),l.push(n.typeIdx),l.push(n.modifierSet),c=o.line,u=o.character)}return l}(c,i,t)}}};const o=n(8618),r=n(4705);function i(e,t){const n=[];let o=!1;for(let r=0;r<e.length;r++){const i=e[r];let a=t.indexOf(i);-1===a&&(a=t.length,t.push(i)),n.push(a),o=o||a!==r}return o?n:void 0}function a(e,t){if(t)for(const n of e)n.typeIdx=t[n.typeIdx]}function s(e,t){if(t)for(const n of e){let e=n.modifierSet;if(e){let o=0,r=0;for(;e>0;)1&e&&(r+=1<<t[o]),o++,e>>=1;n.modifierSet=r}}}},7421:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});const o=n(1327),r=n(211),i=n(6549),a=n(613),s=(0,o.createConnection)();console.log=s.console.log.bind(s.console),console.error=s.console.error.bind(s.console),process.on("unhandledRejection",(e=>{s.console.error((0,r.formatError)("Unhandled exception",e))}));const c={timer:{setImmediate(e,...t){const n=setImmediate(e,...t);return{dispose:()=>clearImmediate(n)}},setTimeout(e,t,...n){const o=setTimeout(e,t,...n);return{dispose:()=>clearTimeout(o)}}},fileFs:(0,a.getNodeFileFS)()};(0,i.startServer)(s,c)},613:function(e,t,n){var o,r=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n);var r=Object.getOwnPropertyDescriptor(t,n);r&&!("get"in r?!t.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,o,r)}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||(o=function(e){return o=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},o(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=o(e),a=0;a<n.length;a++)"default"!==n[a]&&r(t,e,n[a]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getNodeFileFS=function(){function e(e){if(!e.startsWith("file:"))throw new Error("fileSystemProvider can only handle file URLs")}return{stat:t=>(e(t),new Promise(((e,n)=>{const o=s.URI.parse(t);c.stat(o.fsPath,((t,o)=>{if(t)return"ENOENT"===t.code?e({type:u.FileType.Unknown,ctime:-1,mtime:-1,size:-1}):n(t);let r=u.FileType.Unknown;o.isFile()?r=u.FileType.File:o.isDirectory()?r=u.FileType.Directory:o.isSymbolicLink()&&(r=u.FileType.SymbolicLink),e({type:r,ctime:o.ctime.getTime(),mtime:o.mtime.getTime(),size:o.size})}))}))),readDirectory:t=>(e(t),new Promise(((e,n)=>{const o=s.URI.parse(t).fsPath;c.readdir(o,{withFileTypes:!0},((t,o)=>{if(t)return n(t);e(o.map((e=>e.isSymbolicLink()?[e.name,u.FileType.SymbolicLink]:e.isDirectory()?[e.name,u.FileType.Directory]:e.isFile()?[e.name,u.FileType.File]:[e.name,u.FileType.Unknown])))}))})))}};const s=n(7608),c=a(n(9896)),u=n(3815)},6149:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FileType=t.FsReadDirRequest=t.FsStatRequest=void 0,t.getFileSystemProvider=function(e,t,n){const o=n.fileFs&&-1!==e.indexOf("file")?n.fileFs:void 0;return{stat:async e=>o&&e.startsWith("file:")?o.stat(e):await t.sendRequest(r.type,e.toString()),readDirectory:e=>o&&e.startsWith("file:")?o.readDirectory(e):t.sendRequest(i.type,e.toString())}};const o=n(2861);var r,i,a;!function(e){e.type=new o.RequestType("fs/stat")}(r||(t.FsStatRequest=r={})),function(e){e.type=new o.RequestType("fs/readDir")}(i||(t.FsReadDirRequest=i={})),function(e){e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink"}(a||(t.FileType=a={}))},1139:(e,t)=>{function n(e,t){if(e.length<=1)return;const o=e.length/2|0,r=e.slice(0,o),i=e.slice(o);n(r,t),n(i,t);let a=0,s=0,c=0;for(;a<r.length&&s<i.length;){const n=t(r[a],i[s]);e[c++]=n<=0?r[a++]:i[s++]}for(;a<r.length;)e[c++]=r[a++];for(;s<i.length;)e[c++]=i[s++]}Object.defineProperty(t,"__esModule",{value:!0}),t.pushAll=function(e,t){if(t)for(const n of t)e.push(n)},t.contains=function(e,t){return-1!==e.indexOf(t)},t.mergeSort=function(e,t){return n(e,t),e},t.binarySearch=function(e,t,n){let o=0,r=e.length-1;for(;o<=r;){const i=(o+r)/2|0,a=n(e[i],t);if(a<0)o=i+1;else{if(!(a>0))return i;r=i-1}}return-(o+1)}},6529:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getDocumentContext=function(e,t){return{resolveReference:(n,i=e)=>{if(n.match(/^\w[\w\d+.-]*:/))return n;if("/"===n[0]){const r=function(){for(const n of t){let t=n.uri;if((0,o.endsWith)(t,"/")||(t+="/"),(0,o.startsWith)(e,t))return t}}();if(r)return r+n.substr(1)}const a=r.URI.parse(i),s=a.path.endsWith("/")?a:r.Utils.dirname(a);return r.Utils.resolvePath(s,n).toString(!0)}}};const o=n(247),r=n(7608)},4705:(e,t)=>{function n(e,t){return e.line<t.line||e.line===t.line&&e.character<=t.character}function o(e,t){return e.start.line===t.start.line&&e.start.character===t.start.character&&e.end.line===t.end.line&&e.end.character===t.end.character}Object.defineProperty(t,"__esModule",{value:!0}),t.beforeOrSame=n,t.insideRangeButNotSame=function(e,t){return n(e.start,t.start)&&n(t.end,e.end)&&!o(e,t)},t.equalRange=o},211:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.formatError=r,t.runSafe=function(e,t,n,o,a){return new Promise((s=>{e.timer.setImmediate((()=>{if(!a.isCancellationRequested)return t().then((e=>{a.isCancellationRequested?s(i()):s(e)}),(e=>{console.error(r(o,e)),s(n)}));s(i())}))}))};const o=n(2861);function r(e,t){if(t instanceof Error){const n=t;return`${e}: ${n.message}\n${n.stack}`}return"string"==typeof t?`${e}: ${t}`:t?`${e}: ${t.toString()}`:e}function i(){return new o.ResponseError(o.LSPErrorCodes.RequestCancelled,"Request cancelled")}},247:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getWordAtText=function(e,t,n){let o=t;for(;o>0&&!r(e.charCodeAt(o-1));)o--;const i=t-o,a=e.substr(o),s=n.ignoreCase?"gi":"g";let c=(n=new RegExp(n.source,s)).exec(a);for(;c&&c.index+c[0].length<i;)c=n.exec(a);return c&&c.index<=i?{start:c.index+o,length:c[0].length}:{start:t,length:0}},t.startsWith=function(e,t){if(e.length<t.length)return!1;for(let n=0;n<t.length;n++)if(e[n]!==t[n])return!1;return!0},t.endsWith=function(e,t){const n=e.length-t.length;return n>0?e.indexOf(t,n)===n:0===n&&e===t},t.repeat=function(e,t){let n="";for(;t>0;)1&~t||(n+=e),e+=e,t>>>=1;return n},t.isWhitespaceOnly=function(e){return/^\s*$/.test(e)},t.isEOL=function(e,t){return r(e.charCodeAt(t))},t.isNewlineCharacter=r;const n="\r".charCodeAt(0),o="\n".charCodeAt(0);function r(e){return e===n||e===o}},9178:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.registerDiagnosticsPushSupport=function(e,t,n,o){const i={},a=[];function s(e){const t=i[e.uri];t&&(t.dispose(),delete i[e.uri])}function c(e){s(e);const a=i[e.uri]=n.timer.setTimeout((async()=>{if(a===i[e.uri])try{const n=await o(e);a===i[e.uri]&&t.sendDiagnostics({uri:e.uri,diagnostics:n}),delete i[e.uri]}catch(n){t.console.error((0,r.formatError)(`Error while validating ${e.uri}`,n))}}),500)}return e.onDidChangeContent((e=>{c(e.document)}),void 0,a),e.onDidClose((e=>{s(e.document),t.sendDiagnostics({uri:e.document.uri,diagnostics:[]})}),void 0,a),{requestRefresh:()=>{e.all().forEach(c)},dispose:()=>{a.forEach((e=>e.dispose())),a.length=0;const e=Object.keys(i);for(const t of e)i[t].dispose(),delete i[t]}}},t.registerDiagnosticsPullSupport=function(e,t,n,i){function a(e){return{kind:o.DocumentDiagnosticReportKind.Full,items:e}}const s=t.languages.diagnostics.on((async(t,o)=>(0,r.runSafe)(n,(async()=>{const n=e.get(t.textDocument.uri);return a(n?await i(n):[])}),a([]),`Error while computing diagnostics for ${t.textDocument.uri}`,o)));return{requestRefresh:function(){t.languages.diagnostics.refresh()},dispose:()=>{s.dispose()}}};const o=n(2861),r=n(211)}};
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/848b80aeb52026648a8ff9f7c45a9b0a80641e2e/extensions/html-language-features/server/dist/node/421.htmlServerMain.js.map