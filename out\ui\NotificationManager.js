"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationManager = void 0;
const vscode = __importStar(require("vscode"));
const Logger_1 = require("../utils/Logger");
/**
 * Manages user notifications and progress indicators
 */
class NotificationManager {
    static instance;
    activeProgress = new Map();
    constructor() { }
    static getInstance() {
        if (!NotificationManager.instance) {
            NotificationManager.instance = new NotificationManager();
        }
        return NotificationManager.instance;
    }
    /**
     * Show an information message
     */
    async showInfo(message, options) {
        Logger_1.Logger.info(`Notification: ${message}`);
        if (options?.actions && options.actions.length > 0) {
            const actionTitles = options.actions.map((action) => action.title);
            const selection = await vscode.window.showInformationMessage(message, { modal: options.modal, detail: options.detail }, ...actionTitles);
            if (selection) {
                const selectedAction = options.actions.find((action) => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    }
                    catch (error) {
                        Logger_1.Logger.error("Error executing notification action", error);
                    }
                }
            }
            return selection;
        }
        else {
            return vscode.window.showInformationMessage(message, {
                modal: options?.modal,
                detail: options?.detail,
            });
        }
    }
    /**
     * Show a warning message
     */
    async showWarning(message, options) {
        Logger_1.Logger.warn(`Warning: ${message}`);
        if (options?.actions && options.actions.length > 0) {
            const actionTitles = options.actions.map((action) => action.title);
            const selection = await vscode.window.showWarningMessage(message, { modal: options.modal, detail: options.detail }, ...actionTitles);
            if (selection) {
                const selectedAction = options.actions.find((action) => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    }
                    catch (error) {
                        Logger_1.Logger.error("Error executing warning action", error);
                    }
                }
            }
            return selection;
        }
        else {
            return vscode.window.showWarningMessage(message, {
                modal: options?.modal,
                detail: options?.detail,
            });
        }
    }
    /**
     * Show an error message
     */
    async showError(message, options) {
        Logger_1.Logger.error(`Error notification: ${message}`);
        if (options?.actions && options.actions.length > 0) {
            const actionTitles = options.actions.map((action) => action.title);
            const selection = await vscode.window.showErrorMessage(message, { modal: options.modal, detail: options.detail }, ...actionTitles);
            if (selection) {
                const selectedAction = options.actions.find((action) => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    }
                    catch (error) {
                        Logger_1.Logger.error("Error executing error action", error);
                    }
                }
            }
            return selection;
        }
        else {
            return vscode.window.showErrorMessage(message, {
                modal: options?.modal,
                detail: options?.detail,
            });
        }
    }
    /**
     * Show a progress notification
     */
    async showProgress(title, task, options) {
        const progressOptions = {
            location: options?.location || vscode.ProgressLocation.Notification,
            title,
            cancellable: options?.cancellable || false,
        };
        return vscode.window.withProgress(progressOptions, async (progress, token) => {
            const progressId = `${Date.now()}-${Math.random()}`;
            this.activeProgress.set(progressId, progress);
            try {
                const result = await task(progress, token);
                return result;
            }
            finally {
                this.activeProgress.delete(progressId);
            }
        });
    }
    /**
     * Show a quick pick selection
     */
    async showQuickPick(items, options) {
        return vscode.window.showQuickPick(items, options);
    }
    /**
     * Show an input box
     */
    async showInputBox(options) {
        return vscode.window.showInputBox(options);
    }
    /**
     * Show Flutter-specific notifications
     */
    async showFlutterStarting() {
        await this.showInfo("Starting Flutter development server...", {
            actions: [
                {
                    title: "Show Logs",
                    action: async () => {
                        Logger_1.Logger.show();
                    },
                },
            ],
        });
    }
    async showFlutterReady(url) {
        await this.showInfo(`Flutter preview is ready at ${url}`, {
            actions: [
                {
                    title: "Open Preview",
                    action: async () => {
                        await vscode.commands.executeCommand("syncview.showPreview");
                    },
                },
            ],
        });
    }
    async showFlutterError(error) {
        await this.showError(`Flutter error: ${error}`, {
            actions: [
                {
                    title: "Restart",
                    action: async () => {
                        await vscode.commands.executeCommand("syncview.restartPreview");
                    },
                },
                {
                    title: "Show Logs",
                    action: async () => {
                        Logger_1.Logger.show();
                    },
                },
                {
                    title: "Open Settings",
                    action: async () => {
                        await vscode.commands.executeCommand("syncview.openSettings");
                    },
                },
            ],
        });
    }
    async showHotReloadComplete() {
        // Show a subtle notification for hot reload
        vscode.window.setStatusBarMessage("$(check) Hot reload completed", 2000);
    }
    async showHotRestartComplete() {
        // Show a subtle notification for hot restart
        vscode.window.setStatusBarMessage("$(refresh) Hot restart completed", 2000);
    }
    /**
     * Show project setup notifications
     */
    async showProjectNotFound() {
        return this.showWarning("No Flutter project found in the current workspace.", {
            actions: [
                {
                    title: "Create New Project",
                    action: async () => {
                        await vscode.commands.executeCommand("flutter.createProject");
                    },
                },
                {
                    title: "Open Folder",
                    action: async () => {
                        await vscode.commands.executeCommand("vscode.openFolder");
                    },
                },
            ],
        });
    }
    async showWebSupportMissing() {
        return this.showWarning("Flutter web support is not enabled for this project.", {
            detail: "Web support is required for SyncView preview functionality.",
            actions: [
                {
                    title: "Enable Web Support",
                    action: async () => {
                        await vscode.commands.executeCommand("syncview.enableWebSupport");
                    },
                },
                {
                    title: "Learn More",
                    action: async () => {
                        await vscode.env.openExternal(vscode.Uri.parse("https://flutter.dev/docs/get-started/web"));
                    },
                },
            ],
        });
    }
    async showFlutterNotInstalled() {
        return this.showError("Flutter SDK not found.", {
            detail: "Please install Flutter SDK and configure the path in settings.",
            actions: [
                {
                    title: "Download Flutter",
                    action: async () => {
                        await vscode.env.openExternal(vscode.Uri.parse("https://flutter.dev/docs/get-started/install"));
                    },
                },
                {
                    title: "Configure Path",
                    action: async () => {
                        await vscode.commands.executeCommand("workbench.action.openSettings", "syncview.flutter.sdkPath");
                    },
                },
            ],
        });
    }
    /**
     * Show configuration-related notifications
     */
    async showConfigurationError(setting, error) {
        await this.showError(`Configuration error in ${setting}: ${error}`, {
            actions: [
                {
                    title: "Open Settings",
                    action: async () => {
                        await vscode.commands.executeCommand("workbench.action.openSettings", `syncview.${setting}`);
                    },
                },
                {
                    title: "Reset to Default",
                    action: async () => {
                        const config = vscode.workspace.getConfiguration("syncview");
                        await config.update(setting, undefined, vscode.ConfigurationTarget.Global);
                    },
                },
            ],
        });
    }
    /**
     * Show update notifications
     */
    async showExtensionUpdated(version) {
        await this.showInfo(`SyncView has been updated to version ${version}`, {
            actions: [
                {
                    title: "View Changelog",
                    action: async () => {
                        await vscode.commands.executeCommand("vscode.open", vscode.Uri.parse("https://github.com/your-repo/syncview/releases"));
                    },
                },
            ],
        });
    }
    /**
     * Clear all active progress indicators
     */
    clearAllProgress() {
        this.activeProgress.clear();
    }
    /**
     * Get the number of active progress indicators
     */
    getActiveProgressCount() {
        return this.activeProgress.size;
    }
}
exports.NotificationManager = NotificationManager;
//# sourceMappingURL=NotificationManager.js.map