// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from "vscode";
import { ExtensionManager } from "./core/ExtensionManager";
import { Logger } from "./utils/Logger";

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {
  try {
    // Initialize logger
    Logger.initialize(context);
    Logger.info("SyncView extension is activating...");

    // Initialize the main extension manager
    const extensionManager = new ExtensionManager(context);
    extensionManager.activate();

    Logger.info("SyncView extension activated successfully!");
  } catch (error) {
    Logger.error("Failed to activate SyncView extension", error);
    vscode.window.showErrorMessage(
      "Failed to activate SyncView extension. Check the output panel for details."
    );
  }
}

// This method is called when your extension is deactivated
export function deactivate() {
  try {
    Logger.info("SyncView extension is deactivating...");
    // Cleanup will be handled by ExtensionManager dispose methods
  } catch (error) {
    Logger.error("Error during extension deactivation", error);
  }
}
