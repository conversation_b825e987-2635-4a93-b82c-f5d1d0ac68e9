"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyServer = void 0;
const events_1 = require("events");
const http = __importStar(require("http"));
const Logger_1 = require("../utils/Logger");
/**
 * Simple HTTP proxy server for Flutter web preview
 * This is a basic implementation without external dependencies
 */
class ProxyServer extends events_1.EventEmitter {
    corsHandler;
    server;
    targetUrl;
    proxyPort;
    isRunning = false;
    requestCount = 0;
    errorCount = 0;
    constructor(corsHandler) {
        super();
        this.corsHandler = corsHandler;
    }
    /**
     * Start the proxy server
     */
    async start(port) {
        if (this.isRunning) {
            throw new Error("Proxy server is already running");
        }
        try {
            // Create HTTP server
            this.server = http.createServer((req, res) => {
                this.handleRequest(req, res);
            });
            // Start server
            const serverPort = port || (await this.findAvailablePort());
            await this.startServer(serverPort);
            this.proxyPort = serverPort;
            this.isRunning = true;
            const proxyUrl = `http://localhost:${serverPort}`;
            Logger_1.Logger.info(`Proxy server started on ${proxyUrl}`);
            return proxyUrl;
        }
        catch (error) {
            this.isRunning = false;
            Logger_1.Logger.error("Failed to start proxy server", error);
            throw error;
        }
    }
    /**
     * Stop the proxy server
     */
    async stop() {
        if (!this.isRunning) {
            return;
        }
        try {
            if (this.server) {
                await new Promise((resolve, reject) => {
                    this.server.close((error) => {
                        if (error) {
                            reject(error);
                        }
                        else {
                            resolve();
                        }
                    });
                });
            }
            this.isRunning = false;
            this.server = undefined;
            this.proxyPort = undefined;
            Logger_1.Logger.info("Proxy server stopped");
        }
        catch (error) {
            Logger_1.Logger.error("Error stopping proxy server", error);
            throw error;
        }
    }
    /**
     * Set the target URL for proxying
     */
    setTargetUrl(url) {
        this.targetUrl = url;
        Logger_1.Logger.info(`Proxy target URL set to: ${url}`);
    }
    /**
     * Handle HTTP requests
     */
    handleRequest(req, res) {
        this.requestCount++;
        this.emit("request", req);
        // Apply CORS headers
        this.corsHandler.applyCorsHeaders(req, res);
        // Handle preflight requests
        if (req.method === "OPTIONS") {
            res.writeHead(200);
            res.end();
            return;
        }
        // Check if target URL is set
        if (!this.targetUrl) {
            res.writeHead(503, { "Content-Type": "text/plain" });
            res.end("Flutter development server not ready");
            return;
        }
        // Simple proxy implementation
        this.proxyRequest(req, res);
    }
    /**
     * Simple proxy request implementation
     */
    proxyRequest(req, res) {
        try {
            const targetUrl = new URL(this.targetUrl);
            const options = {
                hostname: targetUrl.hostname,
                port: targetUrl.port,
                path: req.url,
                method: req.method,
                headers: { ...req.headers },
            };
            // Remove host header to avoid conflicts
            if (options.headers &&
                typeof options.headers === "object" &&
                !Array.isArray(options.headers)) {
                delete options.headers.host;
            }
            const proxyReq = http.request(options, (proxyRes) => {
                // Copy status and headers
                res.writeHead(proxyRes.statusCode || 200, proxyRes.headers);
                // Pipe response
                proxyRes.pipe(res);
                this.emit("proxyResponse", proxyRes);
            });
            proxyReq.on("error", (error) => {
                this.handleProxyError(error, req, res);
            });
            // Pipe request body
            req.pipe(proxyReq);
        }
        catch (error) {
            this.handleProxyError(error, req, res);
        }
    }
    /**
     * Handle proxy errors
     */
    handleProxyError(error, _req, res) {
        this.errorCount++;
        Logger_1.Logger.error("Proxy request error", error);
        if (!res.headersSent) {
            res.writeHead(502, { "Content-Type": "text/plain" });
            res.end("Bad Gateway: Flutter development server unavailable");
        }
        this.emit("targetError", error);
    }
    /**
     * Start the HTTP server
     */
    startServer(port) {
        return new Promise((resolve, reject) => {
            if (!this.server) {
                reject(new Error("Server not created"));
                return;
            }
            this.server.listen(port, "localhost", () => {
                resolve();
            });
            this.server.on("error", (error) => {
                reject(error);
            });
        });
    }
    /**
     * Find an available port
     */
    async findAvailablePort(startPort = 8081) {
        return new Promise((resolve, reject) => {
            const server = http.createServer();
            server.listen(0, "localhost", () => {
                const address = server.address();
                const port = typeof address === "object" && address ? address.port : startPort;
                server.close(() => {
                    resolve(port);
                });
            });
            server.on("error", (error) => {
                reject(error);
            });
        });
    }
    /**
     * Get proxy server statistics
     */
    getStats() {
        return {
            isRunning: this.isRunning,
            port: this.proxyPort,
            targetUrl: this.targetUrl,
            requestCount: this.requestCount,
            errorCount: this.errorCount,
        };
    }
    /**
     * Check if server is running
     */
    isServerRunning() {
        return this.isRunning;
    }
    /**
     * Get the proxy port
     */
    getPort() {
        return this.proxyPort;
    }
}
exports.ProxyServer = ProxyServer;
//# sourceMappingURL=ProxyServer.js.map