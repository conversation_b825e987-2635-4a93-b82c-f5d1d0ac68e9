import * as assert from 'assert';
import * as vscode from 'vscode';
import { ConfigurationManager } from '../../config/ConfigurationManager';
import { Logger } from '../../utils/Logger';

suite('ConfigurationManager Tests', () => {
    let configManager: ConfigurationManager;
    let mockContext: vscode.ExtensionContext;

    suiteSetup(async () => {
        // Create mock context
        mockContext = {
            subscriptions: [],
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => []
            },
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                setKeysForSync: () => {},
                keys: () => []
            },
            extensionPath: __dirname,
            extensionUri: vscode.Uri.file(__dirname),
            environmentVariableCollection: {} as any,
            extensionMode: vscode.ExtensionMode.Test,
            storageUri: vscode.Uri.file(__dirname),
            globalStorageUri: vscode.Uri.file(__dirname),
            logUri: vscode.Uri.file(__dirname),
            secrets: {} as any,
            asAbsolutePath: (relativePath: string) => relativePath
        };

        Logger.initialize(mockContext);
    });

    setup(() => {
        configManager = new ConfigurationManager();
    });

    teardown(() => {
        if (configManager) {
            configManager.dispose();
        }
    });

    test('Should create ConfigurationManager instance', () => {
        assert.ok(configManager);
    });

    test('Should get configuration values', () => {
        const config = configManager.getConfiguration();
        assert.ok(config);
        assert.ok(config.flutter);
        assert.ok(config.proxy);
        assert.ok(config.ui);
        assert.ok(config.performance);
    });

    test('Should validate default configuration', () => {
        const errors = configManager.validateConfiguration();
        assert.strictEqual(errors.length, 0, 'Default configuration should be valid');
    });

    test('Should handle hot reload settings', () => {
        const hotReloadEnabled = configManager.isHotReloadOnSaveEnabled();
        assert.strictEqual(typeof hotReloadEnabled, 'boolean');
    });

    test('Should handle debug mode settings', () => {
        const debugMode = configManager.isDebugModeEnabled();
        assert.strictEqual(typeof debugMode, 'boolean');
    });

    test('Should handle status bar settings', () => {
        const showStatusBar = configManager.shouldShowStatusBar();
        assert.strictEqual(typeof showStatusBar, 'boolean');
    });

    test('Should handle auto show preview settings', () => {
        const autoShow = configManager.shouldAutoShowPreview();
        assert.strictEqual(typeof autoShow, 'boolean');
    });

    test('Should get preview position', () => {
        const position = configManager.getPreviewPosition();
        assert.ok(['beside', 'active'].includes(position));
    });

    test('Should get device frame setting', () => {
        const frame = configManager.getDeviceFrame();
        assert.ok(['none', 'ios', 'android'].includes(frame));
    });

    test('Should handle CORS settings', () => {
        const corsEnabled = configManager.isCorsEnabled();
        assert.strictEqual(typeof corsEnabled, 'boolean');
        
        const origins = configManager.getCorsOrigins();
        assert.ok(Array.isArray(origins));
    });

    test('Should handle performance settings', () => {
        const throttling = configManager.isThrottlingEnabled();
        assert.strictEqual(typeof throttling, 'boolean');
        
        const frameRate = configManager.getMaxFrameRate();
        assert.strictEqual(typeof frameRate, 'number');
        assert.ok(frameRate > 0);
        
        const memoryLimit = configManager.getMemoryLimit();
        assert.strictEqual(typeof memoryLimit, 'number');
        assert.ok(memoryLimit > 0);
    });

    test('Should get additional Flutter args', () => {
        const args = configManager.getFlutterAdditionalArgs();
        assert.ok(Array.isArray(args));
    });

    test('Should dispose cleanly', () => {
        assert.doesNotThrow(() => {
            configManager.dispose();
        });
    });
});
