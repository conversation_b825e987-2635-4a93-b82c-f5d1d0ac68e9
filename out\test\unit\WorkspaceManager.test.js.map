{"version": 3, "file": "WorkspaceManager.test.js", "sourceRoot": "", "sources": ["../../../src/test/unit/WorkspaceManager.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,kEAA+D;AAC/D,+CAA4C;AAE5C,KAAK,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACnC,IAAI,gBAAkC,CAAC;IACvC,IAAI,WAAoC,CAAC;IAEzC,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,sBAAsB;QACtB,WAAW,GAAG;YACZ,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE;gBACd,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS;gBACpB,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE;gBAC/B,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;aACf;YACD,WAAW,EAAE;gBACX,GAAG,EAAE,GAAG,EAAE,CAAC,SAAS;gBACpB,MAAM,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE;gBAC/B,cAAc,EAAE,GAAG,EAAE,GAAE,CAAC;gBACxB,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE;aACf;YACD,aAAa,EAAE,SAAS;YACxB,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;YACxC,6BAA6B,EAAE,EAAS;YACxC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,IAAI;YACxC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;YACtC,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;YAC5C,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;YAClC,OAAO,EAAE,EAAS;YAClB,cAAc,EAAE,CAAC,YAAoB,EAAE,EAAE,CAAC,YAAY;YACtD,WAAW,EAAE,SAAS;YACtB,iBAAiB,EAAE,SAAS;YAC5B,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,EAAS;YACpB,8BAA8B,EAAE,EAAS;SACf,CAAC;QAE7B,eAAM,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,GAAG,EAAE;QACT,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;IAC5C,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,GAAG,EAAE;QACZ,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACnD,MAAM,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QAC3C,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;QACvD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QAC7D,MAAM,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QACzD,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;QAC9D,MAAM,CAAC,WAAW,CAAC,OAAO,UAAU,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;QAC1C,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,aAAa,EAAE,CAAC;QAC7D,MAAM,CAAC,WAAW,CAAC,OAAO,aAAa,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;QACjD,MAAM,QAAQ,GAAG,MAAM,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;QAChE,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wCAAwC,EAAE,GAAG,EAAE;QAClD,MAAM,YAAY,GAAG,gBAAgB,CAAC,wBAAwB,CAC5D,qBAAqB,CACtB,CAAC;QACF,wEAAwE;QACxE,MAAM,CAAC,EAAE,CAAC,YAAY,KAAK,SAAS,IAAI,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC1C,MAAM,YAAY,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAC;QAC5E,wEAAwE;QACxE,MAAM,CAAC,EAAE,CAAC,YAAY,KAAK,SAAS,IAAI,OAAO,YAAY,KAAK,QAAQ,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qCAAqC,EAAE,GAAG,EAAE;QAC/C,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;YACvB,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;YACrC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,8BAA8B,EAAE,CAAC,IAAI,EAAE,EAAE;QAC5C,IAAI,aAAa,GAAG,KAAK,CAAC;QAE1B,gBAAgB,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE,EAAE;YAC/C,aAAa,GAAG,IAAI,CAAC;YACrB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAChB,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACxC,iBAAiB,EAAE,KAAK;YACxB,aAAa,EAAE,KAAK;YACpB,gBAAgB,EAAE,EAAE;SACrB,CAAC,CAAC;QAEH,mBAAmB;QACnB,UAAU,CAAC,GAAG,EAAE;YACd,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,IAAI,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,CAAC;IACX,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wBAAwB,EAAE,GAAG,EAAE;QAClC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE;YACvB,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}