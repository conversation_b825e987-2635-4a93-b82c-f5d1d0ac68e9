import * as vscode from 'vscode';
import * as path from 'path';
import { ProxyServerManager } from '../proxy/ProxyServerManager';
import { Logger } from '../utils/Logger';

/**
 * Manages the WebView panel for Flutter app preview
 */
export class WebViewManager implements vscode.Disposable {
    private previewPanel: vscode.WebviewPanel | undefined;
    private currentPreviewUrl: string | undefined;
    private readonly disposables: vscode.Disposable[] = [];

    constructor(
        private readonly context: vscode.ExtensionContext,
        private readonly proxyServerManager: ProxyServerManager
    ) {}

    /**
     * Create and show the preview panel
     */
    public async createPreviewPanel(): Promise<void> {
        if (this.previewPanel) {
            this.previewPanel.reveal();
            return;
        }

        this.previewPanel = vscode.window.createWebviewPanel(
            'syncviewPreview',
            'Flutter Preview',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.file(path.join(this.context.extensionPath, 'media'))
                ]
            }
        );

        // Set initial content
        this.previewPanel.webview.html = this.getLoadingHtml();

        // Handle panel disposal
        this.previewPanel.onDidDispose(() => {
            this.previewPanel = undefined;
        }, null, this.disposables);

        // Handle messages from webview
        this.previewPanel.webview.onDidReceiveMessage(
            message => this.handleWebViewMessage(message),
            undefined,
            this.disposables
        );

        Logger.info('Flutter preview panel created');
    }

    /**
     * Update the preview URL and refresh the webview
     */
    public updatePreviewUrl(url: string): void {
        this.currentPreviewUrl = url;
        if (this.previewPanel) {
            this.previewPanel.webview.html = this.getPreviewHtml(url);
            Logger.info(`Preview URL updated: ${url}`);
        }
    }

    /**
     * Show the preview panel
     */
    public showPreviewPanel(): void {
        if (this.previewPanel) {
            this.previewPanel.reveal();
        } else {
            vscode.window.showWarningMessage('Preview panel is not active. Start the preview first.');
        }
    }

    /**
     * Hide the preview panel
     */
    public hidePreviewPanel(): void {
        if (this.previewPanel) {
            // Note: VS Code doesn't have a direct hide method, so we'll minimize to the panel
            this.previewPanel.reveal(vscode.ViewColumn.Active, false);
        }
    }

    /**
     * Refresh the preview
     */
    public async refreshPreview(): Promise<void> {
        if (this.previewPanel && this.currentPreviewUrl) {
            this.previewPanel.webview.html = this.getLoadingHtml();
            // Small delay to show loading state
            setTimeout(() => {
                if (this.previewPanel && this.currentPreviewUrl) {
                    this.previewPanel.webview.html = this.getPreviewHtml(this.currentPreviewUrl);
                }
            }, 500);
        }
    }

    /**
     * Dispose the preview panel
     */
    public disposePreviewPanel(): void {
        if (this.previewPanel) {
            this.previewPanel.dispose();
            this.previewPanel = undefined;
        }
    }

    /**
     * Handle messages from the webview
     */
    private handleWebViewMessage(message: any): void {
        switch (message.command) {
            case 'error':
                Logger.error('WebView error', message.error);
                vscode.window.showErrorMessage(`Preview error: ${message.error}`);
                break;
            case 'loaded':
                Logger.info('Flutter app loaded in preview');
                break;
            case 'reload':
                this.refreshPreview();
                break;
            default:
                Logger.debug('Unknown webview message', message);
        }
    }

    /**
     * Get the HTML content for loading state
     */
    private getLoadingHtml(): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Flutter Preview</title>
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        background-color: #1e1e1e;
                        color: #ffffff;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                        flex-direction: column;
                    }
                    .loading {
                        text-align: center;
                    }
                    .spinner {
                        border: 4px solid #333;
                        border-top: 4px solid #007acc;
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        animation: spin 1s linear infinite;
                        margin: 0 auto 20px;
                    }
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </head>
            <body>
                <div class="loading">
                    <div class="spinner"></div>
                    <h2>Loading Flutter Preview...</h2>
                    <p>Starting Flutter development server...</p>
                </div>
            </body>
            </html>
        `;
    }

    /**
     * Get the HTML content for the Flutter preview
     */
    private getPreviewHtml(url: string): string {
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Flutter Preview</title>
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        overflow: hidden;
                    }
                    iframe {
                        width: 100%;
                        height: 100vh;
                        border: none;
                    }
                    .error {
                        padding: 20px;
                        background-color: #1e1e1e;
                        color: #ffffff;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        text-align: center;
                    }
                </style>
            </head>
            <body>
                <iframe 
                    src="${url}" 
                    onload="handleLoad()" 
                    onerror="handleError()">
                </iframe>
                
                <script>
                    const vscode = acquireVsCodeApi();
                    
                    function handleLoad() {
                        vscode.postMessage({ command: 'loaded' });
                    }
                    
                    function handleError() {
                        vscode.postMessage({ 
                            command: 'error', 
                            error: 'Failed to load Flutter preview' 
                        });
                    }
                    
                    // Handle iframe load errors
                    window.addEventListener('message', function(event) {
                        if (event.data.type === 'flutter-error') {
                            vscode.postMessage({ 
                                command: 'error', 
                                error: event.data.message 
                            });
                        }
                    });
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Dispose all resources
     */
    public dispose(): void {
        this.disposePreviewPanel();
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
    }
}
