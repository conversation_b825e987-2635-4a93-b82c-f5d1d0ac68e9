"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkspaceManager = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const events_1 = require("events");
const FlutterProjectDetector_1 = require("../flutter/FlutterProjectDetector");
const FileUtils_1 = require("../utils/FileUtils");
const Logger_1 = require("../utils/Logger");
/**
 * Manages workspace-related functionality and Flutter project detection
 */
class WorkspaceManager extends events_1.EventEmitter {
    disposables = [];
    projectDetector;
    currentWorkspaceInfo;
    fileWatcher;
    constructor() {
        super();
        this.projectDetector = new FlutterProjectDetector_1.FlutterProjectDetector();
        this.setupWorkspaceWatchers();
        this.initializeWorkspace();
    }
    /**
     * Get current workspace information
     */
    async getWorkspaceInfo() {
        if (!this.currentWorkspaceInfo) {
            await this.refreshWorkspaceInfo();
        }
        return this.currentWorkspaceInfo;
    }
    /**
     * Refresh workspace information
     */
    async refreshWorkspaceInfo() {
        const workspaceFolders = vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath) || [];
        let hasFlutterProject = false;
        let flutterProjectPath;
        let projectName;
        let hasWebSupport = false;
        if (workspaceFolders.length > 0) {
            // Find Flutter project
            flutterProjectPath = await this.projectDetector.findFlutterProject();
            if (flutterProjectPath) {
                hasFlutterProject = true;
                hasWebSupport = await this.projectDetector.isWebSupported(flutterProjectPath);
                // Get project name from pubspec.yaml
                const projectInfo = await this.projectDetector.getProjectInfo(flutterProjectPath);
                projectName = projectInfo?.name;
            }
        }
        const oldInfo = this.currentWorkspaceInfo;
        this.currentWorkspaceInfo = {
            hasFlutterProject,
            flutterProjectPath,
            projectName,
            hasWebSupport,
            workspaceFolders
        };
        // Emit change event if workspace info changed
        if (!oldInfo || this.hasWorkspaceInfoChanged(oldInfo, this.currentWorkspaceInfo)) {
            this.emit('workspaceChanged', this.currentWorkspaceInfo);
            Logger_1.Logger.info('Workspace information updated', this.currentWorkspaceInfo);
        }
    }
    /**
     * Get the primary Flutter project path
     */
    async getFlutterProjectPath() {
        const info = await this.getWorkspaceInfo();
        return info.flutterProjectPath;
    }
    /**
     * Check if workspace has a Flutter project
     */
    async hasFlutterProject() {
        const info = await this.getWorkspaceInfo();
        return info.hasFlutterProject;
    }
    /**
     * Check if Flutter project has web support
     */
    async hasWebSupport() {
        const info = await this.getWorkspaceInfo();
        return info.hasWebSupport;
    }
    /**
     * Get all Flutter projects in workspace
     */
    async getAllFlutterProjects() {
        return this.projectDetector.getAllFlutterProjects();
    }
    /**
     * Create a new Flutter project in workspace
     */
    async createFlutterProject(projectName) {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            Logger_1.Logger.error('No workspace folder available for creating Flutter project');
            return false;
        }
        try {
            const workspacePath = workspaceFolders[0].uri.fsPath;
            const projectPath = path.join(workspacePath, projectName);
            // Check if directory already exists
            if (await FileUtils_1.FileUtils.directoryExists(projectPath)) {
                Logger_1.Logger.error(`Directory ${projectPath} already exists`);
                return false;
            }
            // This would typically use FlutterCliWrapper to create the project
            // For now, we'll just create the basic structure
            await FileUtils_1.FileUtils.createDirectory(projectPath);
            // Refresh workspace info after creation
            await this.refreshWorkspaceInfo();
            Logger_1.Logger.info(`Flutter project created at: ${projectPath}`);
            return true;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to create Flutter project', error);
            return false;
        }
    }
    /**
     * Enable web support for the current Flutter project
     */
    async enableWebSupport() {
        const projectPath = await this.getFlutterProjectPath();
        if (!projectPath) {
            Logger_1.Logger.error('No Flutter project found to enable web support');
            return false;
        }
        try {
            // This would typically use FlutterCliWrapper to enable web support
            // For now, we'll just create the web directory structure
            const webDir = path.join(projectPath, 'web');
            await FileUtils_1.FileUtils.createDirectory(webDir);
            // Create basic index.html
            const indexHtml = `<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flutter_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <title>Flutter App</title>
  <link rel="manifest" href="manifest.json">
  <script>
    var serviceWorkerVersion = null;
    var scriptLoaded = false;
    function loadMainDartJs() {
      if (scriptLoaded) {
        return;
      }
      scriptLoaded = true;
      var scriptTag = document.createElement('script');
      scriptTag.src = 'main.dart.js';
      scriptTag.type = 'application/javascript';
      document.body.append(scriptTag);
    }

    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function () {
        navigator.serviceWorker.register('flutter_service_worker.js');
      });
    }
  </script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        }
      }).then(function(engineInitializer) {
        return engineInitializer.initializeEngine();
      }).then(function(appRunner) {
        return appRunner.runApp();
      });
    });
  </script>
</body>
</html>`;
            await FileUtils_1.FileUtils.writeFile(path.join(webDir, 'index.html'), indexHtml);
            // Refresh workspace info
            await this.refreshWorkspaceInfo();
            Logger_1.Logger.info('Web support enabled for Flutter project');
            return true;
        }
        catch (error) {
            Logger_1.Logger.error('Failed to enable web support', error);
            return false;
        }
    }
    /**
     * Get workspace relative path
     */
    getWorkspaceRelativePath(absolutePath) {
        return FileUtils_1.FileUtils.getWorkspaceRelativePath(absolutePath);
    }
    /**
     * Resolve path relative to workspace
     */
    resolveWorkspacePath(relativePath) {
        return FileUtils_1.FileUtils.resolveWorkspacePath(relativePath);
    }
    /**
     * Watch for important file changes
     */
    startFileWatching() {
        if (this.fileWatcher) {
            return;
        }
        // Watch for pubspec.yaml changes
        this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/pubspec.yaml', false, // Don't ignore creates
        false, // Don't ignore changes
        false // Don't ignore deletes
        );
        this.fileWatcher.onDidCreate(uri => {
            Logger_1.Logger.debug(`pubspec.yaml created: ${uri.fsPath}`);
            this.refreshWorkspaceInfo();
        });
        this.fileWatcher.onDidChange(uri => {
            Logger_1.Logger.debug(`pubspec.yaml changed: ${uri.fsPath}`);
            this.refreshWorkspaceInfo();
        });
        this.fileWatcher.onDidDelete(uri => {
            Logger_1.Logger.debug(`pubspec.yaml deleted: ${uri.fsPath}`);
            this.refreshWorkspaceInfo();
        });
        this.disposables.push(this.fileWatcher);
        Logger_1.Logger.info('File watching started for pubspec.yaml files');
    }
    /**
     * Stop file watching
     */
    stopFileWatching() {
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
            this.fileWatcher = undefined;
            Logger_1.Logger.info('File watching stopped');
        }
    }
    /**
     * Setup workspace event listeners
     */
    setupWorkspaceWatchers() {
        // Watch for workspace folder changes
        const workspaceWatcher = vscode.workspace.onDidChangeWorkspaceFolders(event => {
            Logger_1.Logger.info('Workspace folders changed', {
                added: event.added.map(folder => folder.uri.fsPath),
                removed: event.removed.map(folder => folder.uri.fsPath)
            });
            this.refreshWorkspaceInfo();
        });
        this.disposables.push(workspaceWatcher);
    }
    /**
     * Initialize workspace on startup
     */
    async initializeWorkspace() {
        try {
            await this.refreshWorkspaceInfo();
            this.startFileWatching();
            Logger_1.Logger.info('Workspace manager initialized');
        }
        catch (error) {
            Logger_1.Logger.error('Failed to initialize workspace manager', error);
        }
    }
    /**
     * Check if workspace info has changed
     */
    hasWorkspaceInfoChanged(oldInfo, newInfo) {
        return (oldInfo.hasFlutterProject !== newInfo.hasFlutterProject ||
            oldInfo.flutterProjectPath !== newInfo.flutterProjectPath ||
            oldInfo.projectName !== newInfo.projectName ||
            oldInfo.hasWebSupport !== newInfo.hasWebSupport ||
            JSON.stringify(oldInfo.workspaceFolders) !== JSON.stringify(newInfo.workspaceFolders));
    }
    /**
     * Dispose all resources
     */
    dispose() {
        this.stopFileWatching();
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
        this.removeAllListeners();
        Logger_1.Logger.info('Workspace manager disposed');
    }
}
exports.WorkspaceManager = WorkspaceManager;
//# sourceMappingURL=WorkspaceManager.js.map