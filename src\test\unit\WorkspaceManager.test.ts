import * as assert from "assert";
import * as vscode from "vscode";
import { WorkspaceManager } from "../../core/WorkspaceManager";
import { Logger } from "../../utils/Logger";

suite("WorkspaceManager Tests", () => {
  let workspaceManager: WorkspaceManager;
  let mockContext: vscode.ExtensionContext;

  suiteSetup(async () => {
    // Create mock context
    mockContext = {
      subscriptions: [],
      workspaceState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        keys: () => [],
      },
      globalState: {
        get: () => undefined,
        update: () => Promise.resolve(),
        setKeysForSync: () => {},
        keys: () => [],
      },
      extensionPath: __dirname,
      extensionUri: vscode.Uri.file(__dirname),
      environmentVariableCollection: {} as any,
      extensionMode: vscode.ExtensionMode.Test,
      storageUri: vscode.Uri.file(__dirname),
      globalStorageUri: vscode.Uri.file(__dirname),
      logUri: vscode.Uri.file(__dirname),
      secrets: {} as any,
      asAbsolutePath: (relativePath: string) => relativePath,
      storagePath: __dirname,
      globalStoragePath: __dirname,
      logPath: __dirname,
      extension: {} as any,
      languageModelAccessInformation: {} as any,
    } as vscode.ExtensionContext;

    Logger.initialize(mockContext);
  });

  setup(() => {
    workspaceManager = new WorkspaceManager();
  });

  teardown(() => {
    if (workspaceManager) {
      workspaceManager.dispose();
    }
  });

  test("Should create WorkspaceManager instance", () => {
    assert.ok(workspaceManager);
  });

  test("Should get workspace info", async () => {
    const info = await workspaceManager.getWorkspaceInfo();
    assert.ok(info);
    assert.strictEqual(typeof info.hasFlutterProject, "boolean");
    assert.strictEqual(typeof info.hasWebSupport, "boolean");
    assert.ok(Array.isArray(info.workspaceFolders));
  });

  test("Should check Flutter project existence", async () => {
    const hasProject = await workspaceManager.hasFlutterProject();
    assert.strictEqual(typeof hasProject, "boolean");
  });

  test("Should check web support", async () => {
    const hasWebSupport = await workspaceManager.hasWebSupport();
    assert.strictEqual(typeof hasWebSupport, "boolean");
  });

  test("Should get all Flutter projects", async () => {
    const projects = await workspaceManager.getAllFlutterProjects();
    assert.ok(Array.isArray(projects));
  });

  test("Should handle workspace relative paths", () => {
    const relativePath = workspaceManager.getWorkspaceRelativePath(
      "/some/absolute/path"
    );
    // Should return undefined if no workspace or string if workspace exists
    assert.ok(relativePath === undefined || typeof relativePath === "string");
  });

  test("Should resolve workspace paths", () => {
    const resolvedPath = workspaceManager.resolveWorkspacePath("relative/path");
    // Should return undefined if no workspace or string if workspace exists
    assert.ok(resolvedPath === undefined || typeof resolvedPath === "string");
  });

  test("Should start and stop file watching", () => {
    assert.doesNotThrow(() => {
      workspaceManager.startFileWatching();
      workspaceManager.stopFileWatching();
    });
  });

  test("Should emit events correctly", (done) => {
    let eventReceived = false;

    workspaceManager.on("workspaceChanged", (info) => {
      eventReceived = true;
      assert.ok(info);
      done();
    });

    // Simulate a workspace change event
    workspaceManager.emit("workspaceChanged", {
      hasFlutterProject: false,
      hasWebSupport: false,
      workspaceFolders: [],
    });

    // Fallback timeout
    setTimeout(() => {
      if (!eventReceived) {
        done(new Error("Event not received"));
      }
    }, 1000);
  });

  test("Should dispose cleanly", () => {
    assert.doesNotThrow(() => {
      workspaceManager.dispose();
    });
  });
});
