"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FlutterManager = void 0;
const child_process_1 = require("child_process");
const events_1 = require("events");
const FlutterProjectDetector_1 = require("./FlutterProjectDetector");
const FlutterProcessHandler_1 = require("./FlutterProcessHandler");
const HotReloadHandler_1 = require("./HotReloadHandler");
const Logger_1 = require("../utils/Logger");
/**
 * Manages Flutter CLI processes and integration
 */
class FlutterManager extends events_1.EventEmitter {
    flutterProcess;
    processHandler;
    hotReloadHandler;
    projectDetector;
    isFlutterRunning = false;
    currentServerUrl;
    constructor() {
        super();
        this.processHandler = new FlutterProcessHandler_1.FlutterProcessHandler();
        this.hotReloadHandler = new HotReloadHandler_1.HotReloadHandler();
        this.projectDetector = new FlutterProjectDetector_1.FlutterProjectDetector();
        this.setupEventListeners();
    }
    /**
     * Start the Flutter development server
     */
    async startFlutterProcess() {
        if (this.isFlutterRunning) {
            Logger_1.Logger.warn('Flutter process is already running');
            return;
        }
        try {
            // Detect Flutter project
            const projectPath = await this.projectDetector.findFlutterProject();
            if (!projectPath) {
                throw new Error('No Flutter project found in workspace');
            }
            Logger_1.Logger.info(`Starting Flutter process in: ${projectPath}`);
            // Start Flutter web server
            const args = [
                'run',
                '-d', 'web-server',
                '--web-port=0', // Use random available port
                '--web-hostname=localhost',
                '--no-sound-null-safety' // For compatibility
            ];
            this.flutterProcess = (0, child_process_1.spawn)('flutter', args, {
                cwd: projectPath,
                stdio: ['pipe', 'pipe', 'pipe']
            });
            this.processHandler.attachToProcess(this.flutterProcess);
            this.hotReloadHandler.attachToProcess(this.flutterProcess);
            this.isFlutterRunning = true;
            Logger_1.Logger.info('Flutter process started successfully');
        }
        catch (error) {
            this.isFlutterRunning = false;
            Logger_1.Logger.error('Failed to start Flutter process', error);
            throw error;
        }
    }
    /**
     * Stop the Flutter development server
     */
    async stopFlutterProcess() {
        if (!this.isFlutterRunning || !this.flutterProcess) {
            Logger_1.Logger.warn('Flutter process is not running');
            return;
        }
        try {
            Logger_1.Logger.info('Stopping Flutter process...');
            // Send quit command to Flutter
            if (this.flutterProcess.stdin && !this.flutterProcess.stdin.destroyed) {
                this.flutterProcess.stdin.write('q\n');
            }
            // Wait for graceful shutdown or force kill after timeout
            await this.waitForProcessExit(5000);
            this.isFlutterRunning = false;
            this.currentServerUrl = undefined;
            this.flutterProcess = undefined;
            this.emit('processStopped');
            Logger_1.Logger.info('Flutter process stopped successfully');
        }
        catch (error) {
            Logger_1.Logger.error('Failed to stop Flutter process gracefully', error);
            this.forceKillProcess();
        }
    }
    /**
     * Trigger hot reload
     */
    async hotReload() {
        if (!this.isFlutterRunning || !this.flutterProcess) {
            throw new Error('Flutter process is not running');
        }
        return this.hotReloadHandler.triggerHotReload();
    }
    /**
     * Trigger hot restart
     */
    async hotRestart() {
        if (!this.isFlutterRunning || !this.flutterProcess) {
            throw new Error('Flutter process is not running');
        }
        return this.hotReloadHandler.triggerHotRestart();
    }
    /**
     * Check if Flutter process is running
     */
    isRunning() {
        return this.isFlutterRunning;
    }
    /**
     * Get the current server URL
     */
    getServerUrl() {
        return this.currentServerUrl;
    }
    /**
     * Setup event listeners for process handlers
     */
    setupEventListeners() {
        this.processHandler.on('serverStarted', (url) => {
            this.currentServerUrl = url;
            this.emit('processStarted', url);
            Logger_1.Logger.info(`Flutter server started at: ${url}`);
        });
        this.processHandler.on('error', (error) => {
            this.emit('error', error);
            Logger_1.Logger.error('Flutter process error', error);
        });
        this.processHandler.on('exit', (code) => {
            this.isFlutterRunning = false;
            this.currentServerUrl = undefined;
            this.flutterProcess = undefined;
            this.emit('processStopped');
            Logger_1.Logger.info(`Flutter process exited with code: ${code}`);
        });
        this.hotReloadHandler.on('hotReloadComplete', () => {
            this.emit('hotReloadComplete');
            Logger_1.Logger.info('Hot reload completed');
        });
        this.hotReloadHandler.on('hotRestartComplete', () => {
            this.emit('hotRestartComplete');
            Logger_1.Logger.info('Hot restart completed');
        });
    }
    /**
     * Wait for process to exit gracefully
     */
    waitForProcessExit(timeoutMs) {
        return new Promise((resolve, reject) => {
            if (!this.flutterProcess) {
                resolve();
                return;
            }
            const timeout = setTimeout(() => {
                reject(new Error('Process exit timeout'));
            }, timeoutMs);
            this.flutterProcess.on('exit', () => {
                clearTimeout(timeout);
                resolve();
            });
        });
    }
    /**
     * Force kill the Flutter process
     */
    forceKillProcess() {
        if (this.flutterProcess && !this.flutterProcess.killed) {
            Logger_1.Logger.warn('Force killing Flutter process');
            this.flutterProcess.kill('SIGKILL');
            this.isFlutterRunning = false;
            this.currentServerUrl = undefined;
            this.flutterProcess = undefined;
            this.emit('processStopped');
        }
    }
    /**
     * Dispose all resources
     */
    dispose() {
        this.stopFlutterProcess().catch(error => {
            Logger_1.Logger.error('Error during Flutter manager disposal', error);
        });
        this.processHandler.dispose();
        this.hotReloadHandler.dispose();
        this.removeAllListeners();
    }
}
exports.FlutterManager = FlutterManager;
//# sourceMappingURL=FlutterManager.js.map