"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceMonitor = void 0;
const events_1 = require("events");
const Logger_1 = require("./Logger");
/**
 * Performance monitoring utility for the SyncView extension
 */
class PerformanceMonitor extends events_1.EventEmitter {
    static instance;
    isMonitoring = false;
    monitoringInterval;
    componentMetrics = new Map();
    operationTimers = new Map();
    metricsHistory = [];
    maxHistorySize = 100;
    constructor() {
        super();
    }
    static getInstance() {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }
    /**
     * Start performance monitoring
     */
    startMonitoring(intervalMs = 30000) {
        if (this.isMonitoring) {
            return;
        }
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, intervalMs);
        Logger_1.Logger.info(`Performance monitoring started (interval: ${intervalMs}ms)`);
    }
    /**
     * Stop performance monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = undefined;
        }
        Logger_1.Logger.info("Performance monitoring stopped");
    }
    /**
     * Start timing an operation
     */
    startOperation(operationId) {
        this.operationTimers.set(operationId, Date.now());
    }
    /**
     * End timing an operation and record metrics
     */
    endOperation(operationId, componentName, success = true) {
        const startTime = this.operationTimers.get(operationId);
        if (!startTime) {
            Logger_1.Logger.warn(`No start time found for operation: ${operationId}`);
            return 0;
        }
        const duration = Date.now() - startTime;
        this.operationTimers.delete(operationId);
        // Update component metrics
        this.updateComponentMetrics(componentName, duration, success);
        return duration;
    }
    /**
     * Record a timed operation
     */
    async timeOperation(operationId, componentName, operation) {
        this.startOperation(operationId);
        try {
            const result = await operation();
            this.endOperation(operationId, componentName, true);
            return result;
        }
        catch (error) {
            this.endOperation(operationId, componentName, false);
            throw error;
        }
    }
    /**
     * Get current performance metrics
     */
    getCurrentMetrics() {
        return {
            memoryUsage: process.memoryUsage(),
            cpuUsage: process.cpuUsage(),
            uptime: process.uptime(),
            timestamp: Date.now(),
        };
    }
    /**
     * Get component metrics
     */
    getComponentMetrics(componentName) {
        if (componentName) {
            return (this.componentMetrics.get(componentName) ||
                this.createEmptyComponentMetrics(componentName));
        }
        return Array.from(this.componentMetrics.values());
    }
    /**
     * Get metrics history
     */
    getMetricsHistory() {
        return [...this.metricsHistory];
    }
    /**
     * Get performance summary
     */
    getPerformanceSummary() {
        const currentMetrics = this.getCurrentMetrics();
        const components = Array.from(this.componentMetrics.values());
        return {
            current: currentMetrics,
            components: components.map((comp) => ({
                name: comp.name,
                operationCount: comp.operationCount,
                averageDuration: Math.round(comp.averageDuration * 100) / 100,
                errorRate: comp.operationCount > 0
                    ? (comp.errors / comp.operationCount) * 100
                    : 0,
                lastOperation: comp.lastOperation,
            })),
            memory: {
                heapUsed: Math.round((currentMetrics.memoryUsage.heapUsed / 1024 / 1024) * 100) / 100,
                heapTotal: Math.round((currentMetrics.memoryUsage.heapTotal / 1024 / 1024) * 100) / 100,
                external: Math.round((currentMetrics.memoryUsage.external / 1024 / 1024) * 100) / 100,
            },
            uptime: Math.round(currentMetrics.uptime),
        };
    }
    /**
     * Check if performance is within acceptable limits
     */
    checkPerformanceHealth() {
        const issues = [];
        const currentMetrics = this.getCurrentMetrics();
        // Check memory usage (warn if heap > 500MB)
        const heapUsedMB = currentMetrics.memoryUsage.heapUsed / 1024 / 1024;
        if (heapUsedMB > 500) {
            issues.push(`High memory usage: ${Math.round(heapUsedMB)}MB`);
        }
        // Check component error rates
        for (const [name, metrics] of this.componentMetrics) {
            if (metrics.operationCount > 10) {
                const errorRate = (metrics.errors / metrics.operationCount) * 100;
                if (errorRate > 10) {
                    issues.push(`High error rate in ${name}: ${Math.round(errorRate)}%`);
                }
            }
            // Check for slow operations (> 5 seconds average)
            if (metrics.averageDuration > 5000) {
                issues.push(`Slow operations in ${name}: ${Math.round(metrics.averageDuration)}ms average`);
            }
        }
        return {
            healthy: issues.length === 0,
            issues,
        };
    }
    /**
     * Reset all metrics
     */
    resetMetrics() {
        this.componentMetrics.clear();
        this.operationTimers.clear();
        this.metricsHistory.length = 0;
        Logger_1.Logger.info("Performance metrics reset");
    }
    /**
     * Export metrics to JSON
     */
    exportMetrics() {
        const data = {
            timestamp: Date.now(),
            summary: this.getPerformanceSummary(),
            history: this.metricsHistory,
            components: Array.from(this.componentMetrics.entries()),
        };
        return JSON.stringify(data, null, 2);
    }
    /**
     * Log performance summary
     */
    logPerformanceSummary() {
        const summary = this.getPerformanceSummary();
        const health = this.checkPerformanceHealth();
        Logger_1.Logger.info("Performance Summary", {
            memory: summary.memory,
            uptime: summary.uptime,
            componentCount: summary.components.length,
            healthy: health.healthy,
            issues: health.issues,
        });
        if (summary.components.length > 0) {
            Logger_1.Logger.debug("Component Performance", summary.components);
        }
    }
    /**
     * Collect current metrics
     */
    collectMetrics() {
        const metrics = this.getCurrentMetrics();
        // Add to history
        this.metricsHistory.push(metrics);
        // Limit history size
        if (this.metricsHistory.length > this.maxHistorySize) {
            this.metricsHistory.shift();
        }
        // Emit metrics event
        this.emit("metrics", metrics);
        // Check performance health periodically
        const health = this.checkPerformanceHealth();
        if (!health.healthy) {
            this.emit("performanceIssue", health.issues);
            Logger_1.Logger.warn("Performance issues detected", health.issues);
        }
    }
    /**
     * Update component metrics
     */
    updateComponentMetrics(componentName, duration, success) {
        let metrics = this.componentMetrics.get(componentName);
        if (!metrics) {
            metrics = this.createEmptyComponentMetrics(componentName);
            this.componentMetrics.set(componentName, metrics);
        }
        metrics.operationCount++;
        metrics.totalDuration += duration;
        metrics.averageDuration = metrics.totalDuration / metrics.operationCount;
        metrics.lastOperation = Date.now();
        if (!success) {
            metrics.errors++;
        }
    }
    /**
     * Create empty component metrics
     */
    createEmptyComponentMetrics(name) {
        return {
            name,
            operationCount: 0,
            totalDuration: 0,
            averageDuration: 0,
            lastOperation: 0,
            errors: 0,
        };
    }
    /**
     * Dispose the monitor
     */
    dispose() {
        this.stopMonitoring();
        this.resetMetrics();
        this.removeAllListeners();
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
//# sourceMappingURL=PerformanceMonitor.js.map