{"version": 3, "file": "FlutterManager.js", "sourceRoot": "", "sources": ["../../src/flutter/FlutterManager.ts"], "names": [], "mappings": ";;;AACA,iDAAoD;AACpD,mCAAsC;AACtC,qEAAkE;AAClE,mEAAgE;AAChE,yDAAsD;AACtD,4CAAyC;AAEzC;;GAEG;AACH,MAAa,cAAe,SAAQ,qBAAY;IACpC,cAAc,CAA2B;IACzC,cAAc,CAAwB;IACtC,gBAAgB,CAAmB;IACnC,eAAe,CAAyB;IACxC,gBAAgB,GAAG,KAAK,CAAC;IACzB,gBAAgB,CAAqB;IAE7C;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,cAAc,GAAG,IAAI,6CAAqB,EAAE,CAAC;QAClD,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAC/C,IAAI,CAAC,eAAe,GAAG,IAAI,+CAAsB,EAAE,CAAC;QAEpD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB;QAC5B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YAClD,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,yBAAyB;YACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,CAAC;YACpE,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC7D,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,EAAE,CAAC,CAAC;YAE3D,2BAA2B;YAC3B,MAAM,IAAI,GAAG;gBACT,KAAK;gBACL,IAAI,EAAE,YAAY;gBAClB,cAAc,EAAE,4BAA4B;gBAC5C,0BAA0B;gBAC1B,wBAAwB,CAAC,oBAAoB;aAChD,CAAC;YAEF,IAAI,CAAC,cAAc,GAAG,IAAA,qBAAK,EAAC,SAAS,EAAE,IAAI,EAAE;gBACzC,GAAG,EAAE,WAAW;gBAChB,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;aAClC,CAAC,CAAC;YAEH,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACzD,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAE3D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC3B,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,eAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC9C,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAE3C,+BAA+B;YAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;YAED,yDAAyD;YACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAEpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAEhC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5B,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS;QAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACnB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,mBAAmB;QACvB,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,GAAW,EAAE,EAAE;YACpD,IAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;YACjC,eAAM,CAAC,IAAI,CAAC,8BAA8B,GAAG,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC1B,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YAC5C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC5B,eAAM,CAAC,IAAI,CAAC,qCAAqC,IAAI,EAAE,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,mBAAmB,EAAE,GAAG,EAAE;YAC/C,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAC/B,eAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAChD,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAiB;QACxC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO,EAAE,CAAC;gBACV,OAAO;YACX,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAC9C,CAAC,EAAE,SAAS,CAAC,CAAC;YAEd,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;gBAChC,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,OAAO,EAAE,CAAC;YACd,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,gBAAgB;QACpB,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YACrD,eAAM,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;YAC7C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAChC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACpC,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;CACJ;AApND,wCAoNC"}