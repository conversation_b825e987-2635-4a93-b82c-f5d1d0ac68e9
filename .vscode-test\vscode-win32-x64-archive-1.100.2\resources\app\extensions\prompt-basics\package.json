{"name": "prompt", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.20.0"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "prompt", "aliases": ["Prompt", "prompt"], "extensions": [".prompt.md", "copilot-instructions.md"], "configuration": "./language-configuration.json"}, {"id": "instructions", "aliases": ["Instructions", "instructions"], "extensions": [".instructions.md", "copilot-instructions.md"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "prompt", "path": "./syntaxes/prompt.tmLanguage.json", "scopeName": "text.html.markdown.prompt", "unbalancedBracketScopes": ["markup.underline.link.markdown", "punctuation.definition.list.begin.markdown"]}, {"language": "instructions", "path": "./syntaxes/prompt.tmLanguage.json", "scopeName": "text.html.markdown.prompt", "unbalancedBracketScopes": ["markup.underline.link.markdown", "punctuation.definition.list.begin.markdown"]}], "configurationDefaults": {"[prompt]": {"editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.invisibleCharacters": false, "diffEditor.ignoreTrimWhitespace": false}, "[instructions]": {"editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.invisibleCharacters": false, "diffEditor.ignoreTrimWhitespace": false}}, "snippets": [{"language": "prompt", "path": "./snippets/prompt.code-snippets"}, {"language": "instructions", "path": "./snippets/instructions.code-snippets"}]}, "scripts": {}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}