{"version": 3, "file": "NotificationManager.js", "sourceRoot": "", "sources": ["../../src/ui/NotificationManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,4CAAyC;AAczC;;GAEG;AACH,MAAa,mBAAmB;IACtB,MAAM,CAAC,QAAQ,CAAsB;IACrC,cAAc,GAGlB,IAAI,GAAG,EAAE,CAAC;IAEd,gBAAuB,CAAC;IAEjB,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YAClC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAC3D,CAAC;QACD,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CACnB,OAAe,EACf,OAA6B;QAE7B,eAAM,CAAC,IAAI,CAAC,iBAAiB,OAAO,EAAE,CAAC,CAAC;QAExC,IAAI,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,OAAO,EACP,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAChD,GAAG,YAAY,CAChB,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CACvC,CAAC;gBACF,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC;oBAChC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE;gBACnD,KAAK,EAAE,OAAO,EAAE,KAAK;gBACrB,MAAM,EAAE,OAAO,EAAE,MAAM;aACxB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CACtB,OAAe,EACf,OAA6B;QAE7B,eAAM,CAAC,IAAI,CAAC,YAAY,OAAO,EAAE,CAAC,CAAC;QAEnC,IAAI,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACtD,OAAO,EACP,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAChD,GAAG,YAAY,CAChB,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CACvC,CAAC;gBACF,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC;oBAChC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE;gBAC/C,KAAK,EAAE,OAAO,EAAE,KAAK;gBACrB,MAAM,EAAE,OAAO,EAAE,MAAM;aACxB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS,CACpB,OAAe,EACf,OAA6B;QAE7B,eAAM,CAAC,KAAK,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;QAE/C,IAAI,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,CACpD,OAAO,EACP,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAChD,GAAG,YAAY,CAChB,CAAC;YAEF,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,CACvC,CAAC;gBACF,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC;oBAChC,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;oBACtD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC7C,KAAK,EAAE,OAAO,EAAE,KAAK;gBACrB,MAAM,EAAE,OAAO,EAAE,MAAM;aACxB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CACvB,KAAa,EACb,IAGe,EACf,OAAuE;QAEvE,MAAM,eAAe,GAA2B;YAC9C,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAAC,YAAY;YACnE,KAAK;YACL,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,KAAK;SAC3C,CAAC;QAEF,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAC/B,eAAe,EACf,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;YACxB,MAAM,UAAU,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACpD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAE9C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC3C,OAAO,MAAM,CAAC;YAChB,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACxB,KAAU,EACV,OAAiC;QAEjC,OAAO,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CACvB,OAAgC;QAEhC,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB;QAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,wCAAwC,EAAE;YAC5D,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,WAAW;oBAClB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,eAAM,CAAC,IAAI,EAAE,CAAC;oBAChB,CAAC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,GAAW;QACvC,MAAM,IAAI,CAAC,QAAQ,CAAC,+BAA+B,GAAG,EAAE,EAAE;YACxD,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,cAAc;oBACrB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC;oBAC/D,CAAC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,KAAa;QACzC,MAAM,IAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,EAAE,EAAE;YAC9C,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,SAAS;oBAChB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;oBAClE,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,WAAW;oBAClB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,eAAM,CAAC,IAAI,EAAE,CAAC;oBAChB,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,eAAe;oBACtB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;oBAChE,CAAC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,qBAAqB;QAChC,4CAA4C;QAC5C,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,sBAAsB;QACjC,6CAA6C;QAC7C,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,kCAAkC,EAAE,IAAI,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB;QAC9B,OAAO,IAAI,CAAC,WAAW,CACrB,oDAAoD,EACpD;YACE,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,oBAAoB;oBAC3B,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;oBAChE,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,aAAa;oBACpB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;oBAC5D,CAAC;iBACF;aACF;SACF,CACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,qBAAqB;QAChC,OAAO,IAAI,CAAC,WAAW,CACrB,sDAAsD,EACtD;YACE,MAAM,EAAE,6DAA6D;YACrE,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,oBAAoB;oBAC3B,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,2BAA2B,CAAC,CAAC;oBACpE,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,YAAY;oBACnB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAC3B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAC7D,CAAC;oBACJ,CAAC;iBACF;aACF;SACF,CACF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,uBAAuB;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,wBAAwB,EAAE;YAC9C,MAAM,EAAE,gEAAgE;YACxE,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,kBAAkB;oBACzB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAC3B,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,8CAA8C,CAAC,CACjE,CAAC;oBACJ,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,gBAAgB;oBACvB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAClC,+BAA+B,EAC/B,0BAA0B,CAC3B,CAAC;oBACJ,CAAC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CACjC,OAAe,EACf,KAAa;QAEb,MAAM,IAAI,CAAC,SAAS,CAAC,0BAA0B,OAAO,KAAK,KAAK,EAAE,EAAE;YAClE,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,eAAe;oBACtB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAClC,+BAA+B,EAC/B,YAAY,OAAO,EAAE,CACtB,CAAC;oBACJ,CAAC;iBACF;gBACD;oBACE,KAAK,EAAE,kBAAkB;oBACzB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;wBAC7D,MAAM,MAAM,CAAC,MAAM,CACjB,OAAO,EACP,SAAS,EACT,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAClC,CAAC;oBACJ,CAAC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB,CAAC,OAAe;QAC/C,MAAM,IAAI,CAAC,QAAQ,CAAC,wCAAwC,OAAO,EAAE,EAAE;YACrE,OAAO,EAAE;gBACP;oBACE,KAAK,EAAE,gBAAgB;oBACvB,MAAM,EAAE,KAAK,IAAI,EAAE;wBACjB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAClC,aAAa,EACb,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,gDAAgD,CAAC,CACnE,CAAC;oBACJ,CAAC;iBACF;aACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,sBAAsB;QAC3B,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IAClC,CAAC;CACF;AArYD,kDAqYC"}