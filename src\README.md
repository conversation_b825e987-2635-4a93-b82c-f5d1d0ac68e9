# SyncView Extension Source Code Structure

This document outlines the comprehensive folder structure for the SyncView VS Code extension, organized according to the multi-layer architecture described in the main README.

## 📁 Folder Structure Overview

```
src/
├── core/                    # Core Extension Components
│   └── ExtensionManager.ts  # Main extension coordinator
├── commands/                # VS Code Commands
│   └── CommandManager.ts    # Command registration and handling
├── webview/                 # WebView Management
│   └── WebViewManager.ts    # Flutter preview panel management
├── flutter/                 # Flutter Integration Layer
│   ├── FlutterManager.ts    # Main Flutter process management
│   ├── FlutterProjectDetector.ts  # Project detection and validation
│   ├── FlutterProcessHandler.ts   # CLI output processing
│   └── HotReloadHandler.ts  # Hot reload functionality
├── proxy/                   # Proxy Server Components
│   ├── ProxyServerManager.ts  # Proxy server coordination
│   ├── ProxyServer.ts       # HTTP proxy implementation
│   └── CorsHandler.ts       # CORS handling
├── ui/                      # UI Components
│   └── StatusBarManager.ts  # Status bar controls
├── config/                  # Configuration Management
│   └── ConfigurationManager.ts  # Settings and preferences
├── utils/                   # Utilities and Common Functions
│   ├── Logger.ts           # Centralized logging
│   ├── ErrorHandler.ts     # Error handling and user feedback
│   ├── FileUtils.ts        # File system utilities
│   └── ProcessUtils.ts     # Process management utilities
├── test/                    # Testing Structure
│   ├── extension.test.ts    # Main extension tests
│   ├── unit/               # Unit tests
│   │   ├── FlutterManager.test.ts
│   │   └── ProxyServerManager.test.ts
│   └── integration/        # Integration tests
│       └── ExtensionManager.test.ts
└── extension.ts            # Main extension entry point
```

## 🏗️ Architecture Layers

### 1. Core Extension Components (`core/`)
- **ExtensionManager.ts**: Central coordinator that manages all extension components, handles lifecycle, and coordinates communication between different layers.

### 2. Flutter Integration Layer (`flutter/`)
- **FlutterManager.ts**: Main interface for Flutter operations, manages process lifecycle
- **FlutterProjectDetector.ts**: Detects and validates Flutter projects in workspace
- **FlutterProcessHandler.ts**: Processes Flutter CLI output and events
- **HotReloadHandler.ts**: Handles hot reload and hot restart functionality

### 3. Proxy Server Components (`proxy/`)
- **ProxyServerManager.ts**: High-level proxy server management
- **ProxyServer.ts**: Simple HTTP proxy implementation (no external dependencies)
- **CorsHandler.ts**: CORS configuration and header management

### 4. UI and Commands (`commands/`, `ui/`)
- **CommandManager.ts**: Registers and handles all VS Code commands
- **StatusBarManager.ts**: Manages status bar indicators and controls

### 5. WebView Management (`webview/`)
- **WebViewManager.ts**: Creates and manages the Flutter preview panel, handles WebView communication

### 6. Configuration (`config/`)
- **ConfigurationManager.ts**: Manages extension settings, provides typed configuration access

### 7. Utilities (`utils/`)
- **Logger.ts**: Centralized logging with output channel integration
- **ErrorHandler.ts**: User-friendly error handling with actionable feedback
- **FileUtils.ts**: File system operations and workspace utilities
- **ProcessUtils.ts**: Process spawning, monitoring, and management

### 8. Testing (`test/`)
- **extension.test.ts**: Main extension test suite
- **unit/**: Component-specific unit tests
- **integration/**: Cross-component integration tests

## 🔄 Component Interactions

### Event Flow
1. **ExtensionManager** coordinates all components
2. **CommandManager** handles user actions
3. **FlutterManager** spawns and manages Flutter processes
4. **ProxyServerManager** creates proxy for CORS handling
5. **WebViewManager** displays Flutter app in VS Code panel
6. **StatusBarManager** provides visual feedback

### Communication Patterns
- **Event-driven**: Components use EventEmitter for loose coupling
- **Dependency injection**: Managers receive dependencies in constructors
- **Centralized logging**: All components use the Logger utility
- **Configuration-driven**: Settings managed through ConfigurationManager

## 🚀 Key Features Supported

### Current Implementation
- ✅ Extension activation and lifecycle management
- ✅ Command registration and handling
- ✅ Flutter project detection
- ✅ Process management utilities
- ✅ Simple HTTP proxy server
- ✅ WebView panel creation
- ✅ Status bar integration
- ✅ Comprehensive logging
- ✅ Error handling with user feedback
- ✅ Configuration management
- ✅ Unit and integration testing

### Ready for Implementation
- 🔄 Flutter CLI integration
- 🔄 Hot reload functionality
- 🔄 Live preview updates
- 🔄 CORS proxy communication
- 🔄 File watching for auto-reload

## 📝 Development Guidelines

### Adding New Features
1. Identify the appropriate layer (core, flutter, proxy, ui, etc.)
2. Create new files following the naming convention
3. Implement proper error handling using ErrorHandler
4. Add logging using the Logger utility
5. Write corresponding tests
6. Update this documentation

### Code Organization Principles
- **Single Responsibility**: Each class has one clear purpose
- **Dependency Injection**: Dependencies passed through constructors
- **Event-Driven**: Use EventEmitter for component communication
- **Error Handling**: Consistent error handling with user feedback
- **Testing**: Unit tests for individual components, integration tests for workflows

This structure provides a solid foundation for implementing the complete SyncView functionality while maintaining clean architecture and extensibility.
