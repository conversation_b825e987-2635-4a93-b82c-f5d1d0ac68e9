{"$schema": "http://json-schema.org/draft-07/schema#", "description": "Configures an attached to container", "allowComments": true, "allowTrailingCommas": true, "type": "object", "definitions": {"attachContainer": {"type": "object", "properties": {"workspaceFolder": {"type": "string", "description": "The path of the workspace folder inside the container."}, "forwardPorts": {"type": "array", "description": "Ports that are forwarded from the container to the local machine. Can be an integer port number, or a string of the format \"host:port_number\".", "items": {"oneOf": [{"type": "integer", "maximum": 65535, "minimum": 0}, {"type": "string", "pattern": "^([a-z0-9_-]+):(\\d{1,5})$"}]}}, "portsAttributes": {"type": "object", "patternProperties": {"(^\\d+(-\\d+)?$)|(.+)": {"type": "object", "description": "A port, range of ports (ex. \"40000-55000\"), or regular expression (ex. \".+\\\\/server.js\").  For a port number or range, the attributes will apply to that port number or range of port numbers. Attributes which use a regular expression will apply to ports whose associated process command line matches the expression.", "properties": {"onAutoForward": {"type": "string", "enum": ["notify", "openBrowser", "openBrowserOnce", "openPreview", "silent", "ignore"], "enumDescriptions": ["Shows a notification when a port is automatically forwarded.", "Opens the browser when the port is automatically forwarded. Depending on your settings, this could open an embedded browser.", "Opens the browser when the port is automatically forwarded, but only the first time the port is forward during a session. Depending on your settings, this could open an embedded browser.", "Opens a preview in the same window when the port is automatically forwarded.", "Shows no notification and takes no action when this port is automatically forwarded.", "This port will not be automatically forwarded."], "description": "Defines the action that occurs when the port is discovered for automatic forwarding", "default": "notify"}, "elevateIfNeeded": {"type": "boolean", "description": "Automatically prompt for elevation (if needed) when this port is forwarded. Elevate is required if the local port is a privileged port.", "default": false}, "label": {"type": "string", "description": "Label that will be shown in the UI for this port.", "default": "Application"}, "requireLocalPort": {"type": "boolean", "markdownDescription": "When true, a modal dialog will show if the chosen local port isn't used for forwarding.", "default": false}, "protocol": {"type": "string", "enum": ["http", "https"], "description": "The protocol to use when forwarding this port."}}, "default": {"label": "Application", "onAutoForward": "notify"}}}, "markdownDescription": "Set default properties that are applied when a specific port number is forwarded. For example:\n\n```\n\"3000\": {\n  \"label\": \"Application\"\n},\n\"40000-55000\": {\n  \"onAutoForward\": \"ignore\"\n},\n\".+\\\\/server.js\": {\n \"onAutoForward\": \"openPreview\"\n}\n```", "defaultSnippets": [{"body": {"${1:3000}": {"label": "${2:Application}", "onAutoForward": "notify"}}}], "additionalProperties": false}, "otherPortsAttributes": {"type": "object", "properties": {"onAutoForward": {"type": "string", "enum": ["notify", "openBrowser", "openPreview", "silent", "ignore"], "enumDescriptions": ["Shows a notification when a port is automatically forwarded.", "Opens the browser when the port is automatically forwarded. Depending on your settings, this could open an embedded browser.", "Opens a preview in the same window when the port is automatically forwarded.", "Shows no notification and takes no action when this port is automatically forwarded.", "This port will not be automatically forwarded."], "description": "Defines the action that occurs when the port is discovered for automatic forwarding", "default": "notify"}, "elevateIfNeeded": {"type": "boolean", "description": "Automatically prompt for elevation (if needed) when this port is forwarded. Elevate is required if the local port is a privileged port.", "default": false}, "label": {"type": "string", "description": "Label that will be shown in the UI for this port.", "default": "Application"}, "requireLocalPort": {"type": "boolean", "markdownDescription": "When true, a modal dialog will show if the chosen local port isn't used for forwarding.", "default": false}, "protocol": {"type": "string", "enum": ["http", "https"], "description": "The protocol to use when forwarding this port."}}, "defaultSnippets": [{"body": {"onAutoForward": "ignore"}}], "markdownDescription": "Set default properties that are applied to all ports that don't get properties from the setting `remote.portsAttributes`. For example:\n\n```\n{\n  \"onAutoForward\": \"ignore\"\n}\n```", "additionalProperties": false}, "settings": {"$ref": "vscode://schemas/settings/machine", "description": "Machine specific settings that should be copied into the container. These are only copied when connecting to the container for the first time."}, "remoteEnv": {"type": "object", "additionalProperties": {"type": ["string", "null"]}, "description": "Remote environment variables. If these are used in the Integrated Terminal, make sure the 'Terminal > Integrated: Inherit Env' setting is enabled."}, "remoteUser": {"type": "string", "description": "The user VS Code Server will be started with. The default is the same user as the container."}, "extensions": {"type": "array", "description": "An array of extensions that should be installed into the container.", "items": {"type": "string", "pattern": "^([a-z0-9A-Z][a-z0-9A-Z-]*)\\.([a-z0-9A-Z][a-z0-9A-Z-]*)(@(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?)?$", "errorMessage": "Expected format: '${publisher}.${name}' or '${publisher}.${name}@${version}'. Example: 'ms-dotnettools.csharp'."}}, "userEnvProbe": {"type": "string", "enum": ["none", "loginShell", "loginInteractiveShell", "interactiveShell"], "description": "User environment probe to run. The default is none."}, "postAttachCommand": {"type": ["string", "array"], "description": "A command to run after attaching to the container. If this is a single string, it will be run in a shell. If this is an array of strings, it will be run as a single command without shell.", "items": {"type": "string"}}}}}, "allOf": [{"$ref": "#/definitions/attachContainer"}]}