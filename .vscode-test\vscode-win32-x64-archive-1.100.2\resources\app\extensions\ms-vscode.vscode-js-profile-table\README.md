# `vscode-js-profile-table`

This is a custom editor that provides tablular information from for V8-style `.cpuprofile` files. Usage:

1. Install this extension,
2. Open a `.cpuprofile` file in VS Code,
3. If you already have another editor, such as the default table view, hit `F1` and then `Reopen With` this extension.

![Screenshot of the table](https://raw.githubusercontent.com/microsoft/vscode-js-profile-visualizer/master/table.png)
