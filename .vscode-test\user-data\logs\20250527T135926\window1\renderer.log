2025-05-27 13:59:28.093 [info] Started local extension host with pid 13076.
2025-05-27 13:59:28.309 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-05-27 13:59:28.589 [info] ComputeTargetPlatform: win32-x64
2025-05-27 13:59:28.623 [info] Loading development extension at d:\DevGen\devgen-syncview
2025-05-27 13:59:28.767 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-05-27 13:59:29.400 [info] [0m%s%s[0m  
2025-05-27 13:59:29.403 [info] [0m%s%s[0m    SyncView Extension Test Suite
2025-05-27 13:59:29.441 [info] [0m%s%s[0m      Logger Tests
2025-05-27 13:59:29.473 [info]     [32m  ✔[0m[90m %s[0m Logger should log messages
2025-05-27 13:59:29.475 [info] [0m%s%s[0m      FileUtils Tests
2025-05-27 13:59:29.476 [info]     [32m  ✔[0m[90m %s[0m Should normalize paths correctly
2025-05-27 13:59:29.480 [info]     [32m  ✔[0m[90m %s[0m Should get file extension correctly
2025-05-27 13:59:29.483 [info]     [32m  ✔[0m[90m %s[0m Should get file name without extension
2025-05-27 13:59:29.484 [info]     [32m  ✔[0m[90m %s[0m Should check if path is absolute
2025-05-27 13:59:29.484 [info] [0m%s%s[0m      ProcessUtils Tests
2025-05-27 13:59:29.578 [info]     [32m  ✔[0m[90m %s[0m[31m (%dms)[0m Should check command availability 93
2025-05-27 13:59:29.678 [info]     [32m  ✔[0m[90m %s[0m[31m (%dms)[0m Should handle non-existent commands 100
2025-05-27 13:59:29.679 [info] [0m%s%s[0m      FlutterProjectDetector Tests
2025-05-27 13:59:29.681 [info]     [32m  ✔[0m[90m %s[0m Should create detector instance
2025-05-27 13:59:29.681 [info]     [32m  ✔[0m[90m %s[0m Should validate Flutter project structure
2025-05-27 13:59:29.682 [info] [0m%s%s[0m      Extension Commands
2025-05-27 13:59:29.682 [info]     [32m  ✔[0m[90m %s[0m Extension should be present
2025-05-27 13:59:29.686 [info]     [32m  ✔[0m[90m %s[0m Commands should be registered
2025-05-27 13:59:29.686 [info] [0m%s%s[0m    WorkspaceManager Tests
2025-05-27 13:59:29.687 [info]   [32m  ✔[0m[90m %s[0m Should create WorkspaceManager instance
2025-05-27 13:59:29.690 [info]   [32m  ✔[0m[90m %s[0m Should get workspace info
2025-05-27 13:59:29.691 [info]   [32m  ✔[0m[90m %s[0m Should check Flutter project existence
2025-05-27 13:59:29.691 [info]   [32m  ✔[0m[90m %s[0m Should check web support
2025-05-27 13:59:29.692 [info]   [32m  ✔[0m[90m %s[0m Should get all Flutter projects
2025-05-27 13:59:29.694 [info]   [32m  ✔[0m[90m %s[0m Should handle workspace relative paths
2025-05-27 13:59:29.695 [info]   [32m  ✔[0m[90m %s[0m Should resolve workspace paths
2025-05-27 13:59:29.696 [info]   [32m  ✔[0m[90m %s[0m Should start and stop file watching
2025-05-27 13:59:29.696 [info]   [32m  ✔[0m[90m %s[0m Should emit events correctly
2025-05-27 13:59:29.697 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:59:29.697 [info] [0m%s%s[0m    ProxyServerManager Tests
2025-05-27 13:59:29.698 [info]   [32m  ✔[0m[90m %s[0m Should create ProxyServerManager instance
2025-05-27 13:59:29.699 [info]   [32m  ✔[0m[90m %s[0m Should handle server state correctly
2025-05-27 13:59:29.703 [info]   [32m  ✔[0m[90m %s[0m Should set target URL
2025-05-27 13:59:29.704 [info]   [32m  ✔[0m[90m %s[0m Should emit events correctly
2025-05-27 13:59:29.705 [info]   [32m  ✔[0m[90m %s[0m Should get stats
2025-05-27 13:59:29.705 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:59:29.707 [info] [0m%s%s[0m    FlutterManager Tests
2025-05-27 13:59:29.712 [info]   [32m  ✔[0m[90m %s[0m Should create FlutterManager instance
2025-05-27 13:59:29.712 [info]   [32m  ✔[0m[90m %s[0m Should handle process state correctly
2025-05-27 13:59:29.713 [info]   [32m  ✔[0m[90m %s[0m Should emit events correctly
2025-05-27 13:59:29.714 [info]   [32m  ✔[0m[90m %s[0m Should handle hot reload when not running
2025-05-27 13:59:29.716 [info]   [32m  ✔[0m[90m %s[0m Should handle hot restart when not running
2025-05-27 13:59:29.716 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:59:29.717 [info] [0m%s%s[0m    ConfigurationManager Tests
2025-05-27 13:59:29.718 [info]   [32m  ✔[0m[90m %s[0m Should create ConfigurationManager instance
2025-05-27 13:59:29.718 [info]   [32m  ✔[0m[90m %s[0m Should get configuration values
2025-05-27 13:59:29.719 [info]   [32m  ✔[0m[90m %s[0m Should validate default configuration
2025-05-27 13:59:29.720 [info]   [32m  ✔[0m[90m %s[0m Should handle hot reload settings
2025-05-27 13:59:29.720 [info]   [32m  ✔[0m[90m %s[0m Should handle debug mode settings
2025-05-27 13:59:29.722 [info]   [32m  ✔[0m[90m %s[0m Should handle status bar settings
2025-05-27 13:59:29.722 [info]   [32m  ✔[0m[90m %s[0m Should handle auto show preview settings
2025-05-27 13:59:29.723 [info]   [32m  ✔[0m[90m %s[0m Should get preview position
2025-05-27 13:59:29.723 [info]   [32m  ✔[0m[90m %s[0m Should get device frame setting
2025-05-27 13:59:29.724 [info]   [32m  ✔[0m[90m %s[0m Should handle CORS settings
2025-05-27 13:59:29.725 [info]   [32m  ✔[0m[90m %s[0m Should handle performance settings
2025-05-27 13:59:29.725 [info]   [32m  ✔[0m[90m %s[0m Should get additional Flutter args
2025-05-27 13:59:29.726 [info]   [32m  ✔[0m[90m %s[0m Should dispose cleanly
2025-05-27 13:59:29.726 [info] [0m%s%s[0m    ExtensionManager Integration Tests
2025-05-27 13:59:29.728 [info]   [32m  ✔[0m[90m %s[0m Should create ExtensionManager instance
2025-05-27 13:59:29.732 [info]   [32m  ✔[0m[90m %s[0m Should activate without errors
2025-05-27 13:59:29.745 [info]   [31m  %d) %s[0m 1 Should dispose cleanly
2025-05-27 13:59:29.746 [info]   [31m  %d) %s[0m 2 Should handle multiple activations gracefully
2025-05-27 13:59:29.746 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 48 340ms
2025-05-27 13:59:29.747 [info] [31m  %d failing[0m 2
2025-05-27 13:59:29.747 [info] [0m  %s) %s:
[0m[31m     %s[0m[90m
%s
[0m 1 ExtensionManager Integration Tests
       Should dispose cleanly Error: View provider for 'syncview.flutterPreview' already registered   	at pG.registerWebviewViewProvider (file:///d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:170:24055)
  	at Object.registerWebviewViewProvider (file:///d:/DevGen/devgen-syncview/.vscode-test/vscode-win32-x64-archive-1.100.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:170:42961)
  	at ExtensionManager.activate (d:\DevGen\devgen-syncview\out\core\ExtensionManager.js:95:55)
  	at Context.<anonymous> (d:\DevGen\devgen-syncview\out\test\integration\ExtensionManager.test.js:92:26)
  	at process.processImmediate (node:internal/timers:483:21)
2025-05-27 13:59:29.748 [info] [0m  %s) %s:
[0m[31m     %s[0m[90m
%s
[0m 2 ExtensionManager Integration Tests
       Should handle multiple activations gracefully AssertionError [ERR_ASSERTION]: Got unwanted exception.
Actual message: "View provider for 'syncview.flutterPreview' already registered"   	at Context.<anonymous> (d:\DevGen\devgen-syncview\out\test\integration\ExtensionManager.test.js:98:16)
  	at process.processImmediate (node:internal/timers:483:21)
2025-05-27 13:59:29.749 [error] 2 tests failed.
