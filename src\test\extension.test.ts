import * as assert from "assert";
import * as vscode from "vscode";

// Import extension modules for testing
import { Logger } from "../utils/Logger";
import { FileUtils } from "../utils/FileUtils";
import { ProcessUtils } from "../utils/ProcessUtils";
import { FlutterProjectDetector } from "../flutter/FlutterProjectDetector";

suite("SyncView Extension Test Suite", () => {
  vscode.window.showInformationMessage("Starting SyncView tests...");

  suiteSetup(async () => {
    // Initialize logger for tests
    const context = {
      subscriptions: [],
    } as any;
    Logger.initialize(context);
  });

  suite("Logger Tests", () => {
    test("Logger should log messages", () => {
      // Test that logger doesn't throw errors
      assert.doesNotThrow(() => {
        Logger.info("Test info message");
        Logger.warn("Test warning message");
        Logger.debug("Test debug message");
      });
    });
  });

  suite("FileUtils Tests", () => {
    test("Should normalize paths correctly", () => {
      const testPath = "folder\\subfolder\\file.txt";
      const normalized = FileUtils.normalizePath(testPath);
      assert.strictEqual(normalized, "folder/subfolder/file.txt");
    });

    test("Should get file extension correctly", () => {
      assert.strictEqual(FileUtils.getFileExtension("test.dart"), ".dart");
      assert.strictEqual(FileUtils.getFileExtension("test.yaml"), ".yaml");
      assert.strictEqual(FileUtils.getFileExtension("test"), "");
    });

    test("Should get file name without extension", () => {
      assert.strictEqual(
        FileUtils.getFileNameWithoutExtension("test.dart"),
        "test"
      );
      assert.strictEqual(
        FileUtils.getFileNameWithoutExtension("my-file.test.js"),
        "my-file.test"
      );
    });

    test("Should check if path is absolute", () => {
      if (process.platform === "win32") {
        assert.strictEqual(FileUtils.isAbsolutePath("C:\\test"), true);
        assert.strictEqual(FileUtils.isAbsolutePath("test"), false);
      } else {
        assert.strictEqual(FileUtils.isAbsolutePath("/test"), true);
        assert.strictEqual(FileUtils.isAbsolutePath("test"), false);
      }
    });
  });

  suite("ProcessUtils Tests", () => {
    test("Should check command availability", async () => {
      // Test with a command that should exist on all systems
      const nodeAvailable = await ProcessUtils.isCommandAvailable("node");
      assert.strictEqual(nodeAvailable, true);
    });

    test("Should handle non-existent commands", async () => {
      const fakeCommandAvailable = await ProcessUtils.isCommandAvailable(
        "this-command-does-not-exist-12345"
      );
      assert.strictEqual(fakeCommandAvailable, false);
    });
  });

  suite("FlutterProjectDetector Tests", () => {
    let detector: FlutterProjectDetector;

    setup(() => {
      detector = new FlutterProjectDetector();
    });

    test("Should create detector instance", () => {
      assert.ok(detector);
    });

    test("Should validate Flutter project structure", async () => {
      // This test would need a mock Flutter project structure
      // For now, just test that the method doesn't throw
      const result = await detector.validateFlutterProject(
        "/non-existent-path"
      );
      assert.strictEqual(result, false);
    });
  });

  suite("Extension Commands", () => {
    test("Extension should be present", () => {
      const extension = vscode.extensions.getExtension(
        "undefined_publisher.devgen-syncview"
      );
      // Extension might not be loaded in test environment, so just check it doesn't throw
      assert.doesNotThrow(() => {
        if (extension) {
          assert.ok(extension);
        }
      });
    });

    test("Commands should be registered", async () => {
      const commands = await vscode.commands.getCommands(true);

      // Check for some of our main commands
      const syncViewCommands = commands.filter((cmd) =>
        cmd.startsWith("syncview.")
      );

      // In test environment, commands might not be registered yet
      // So we just verify the test doesn't throw an error
      assert.doesNotThrow(() => {
        assert.ok(Array.isArray(syncViewCommands));
      });
    });
  });
});
