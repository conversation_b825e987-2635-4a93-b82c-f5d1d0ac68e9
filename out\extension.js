"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
const vscode = __importStar(require("vscode"));
const ExtensionManager_1 = require("./core/ExtensionManager");
const Logger_1 = require("./utils/Logger");
// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
function activate(context) {
    try {
        // Initialize logger
        Logger_1.Logger.initialize(context);
        Logger_1.Logger.info("SyncView extension is activating...");
        // Initialize the main extension manager
        const extensionManager = new ExtensionManager_1.ExtensionManager(context);
        extensionManager.activate();
        Logger_1.Logger.info("SyncView extension activated successfully!");
    }
    catch (error) {
        Logger_1.Logger.error("Failed to activate SyncView extension", error);
        vscode.window.showErrorMessage("Failed to activate SyncView extension. Check the output panel for details.");
    }
}
// This method is called when your extension is deactivated
function deactivate() {
    try {
        Logger_1.Logger.info("SyncView extension is deactivating...");
        // Cleanup will be handled by ExtensionManager dispose methods
    }
    catch (error) {
        Logger_1.Logger.error("Error during extension deactivation", error);
    }
}
//# sourceMappingURL=extension.js.map