"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const WorkspaceManager_1 = require("../../core/WorkspaceManager");
const Logger_1 = require("../../utils/Logger");
suite("WorkspaceManager Tests", () => {
    let workspaceManager;
    let mockContext;
    suiteSetup(async () => {
        // Create mock context
        mockContext = {
            subscriptions: [],
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => [],
            },
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                setKeysForSync: () => { },
                keys: () => [],
            },
            extensionPath: __dirname,
            extensionUri: vscode.Uri.file(__dirname),
            environmentVariableCollection: {},
            extensionMode: vscode.ExtensionMode.Test,
            storageUri: vscode.Uri.file(__dirname),
            globalStorageUri: vscode.Uri.file(__dirname),
            logUri: vscode.Uri.file(__dirname),
            secrets: {},
            asAbsolutePath: (relativePath) => relativePath,
            storagePath: __dirname,
            globalStoragePath: __dirname,
            logPath: __dirname,
            extension: {},
            languageModelAccessInformation: {},
        };
        Logger_1.Logger.initialize(mockContext);
    });
    setup(() => {
        workspaceManager = new WorkspaceManager_1.WorkspaceManager();
    });
    teardown(() => {
        if (workspaceManager) {
            workspaceManager.dispose();
        }
    });
    test("Should create WorkspaceManager instance", () => {
        assert.ok(workspaceManager);
    });
    test("Should get workspace info", async () => {
        const info = await workspaceManager.getWorkspaceInfo();
        assert.ok(info);
        assert.strictEqual(typeof info.hasFlutterProject, "boolean");
        assert.strictEqual(typeof info.hasWebSupport, "boolean");
        assert.ok(Array.isArray(info.workspaceFolders));
    });
    test("Should check Flutter project existence", async () => {
        const hasProject = await workspaceManager.hasFlutterProject();
        assert.strictEqual(typeof hasProject, "boolean");
    });
    test("Should check web support", async () => {
        const hasWebSupport = await workspaceManager.hasWebSupport();
        assert.strictEqual(typeof hasWebSupport, "boolean");
    });
    test("Should get all Flutter projects", async () => {
        const projects = await workspaceManager.getAllFlutterProjects();
        assert.ok(Array.isArray(projects));
    });
    test("Should handle workspace relative paths", () => {
        const relativePath = workspaceManager.getWorkspaceRelativePath("/some/absolute/path");
        // Should return undefined if no workspace or string if workspace exists
        assert.ok(relativePath === undefined || typeof relativePath === "string");
    });
    test("Should resolve workspace paths", () => {
        const resolvedPath = workspaceManager.resolveWorkspacePath("relative/path");
        // Should return undefined if no workspace or string if workspace exists
        assert.ok(resolvedPath === undefined || typeof resolvedPath === "string");
    });
    test("Should start and stop file watching", () => {
        assert.doesNotThrow(() => {
            workspaceManager.startFileWatching();
            workspaceManager.stopFileWatching();
        });
    });
    test("Should emit events correctly", (done) => {
        let eventReceived = false;
        workspaceManager.on("workspaceChanged", (info) => {
            eventReceived = true;
            assert.ok(info);
            done();
        });
        // Simulate a workspace change event
        workspaceManager.emit("workspaceChanged", {
            hasFlutterProject: false,
            hasWebSupport: false,
            workspaceFolders: [],
        });
        // Fallback timeout
        setTimeout(() => {
            if (!eventReceived) {
                done(new Error("Event not received"));
            }
        }, 1000);
    });
    test("Should dispose cleanly", () => {
        assert.doesNotThrow(() => {
            workspaceManager.dispose();
        });
    });
});
//# sourceMappingURL=WorkspaceManager.test.js.map