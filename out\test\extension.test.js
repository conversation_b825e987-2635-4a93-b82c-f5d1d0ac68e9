"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
// Import extension modules for testing
const Logger_1 = require("../utils/Logger");
const FileUtils_1 = require("../utils/FileUtils");
const ProcessUtils_1 = require("../utils/ProcessUtils");
const FlutterProjectDetector_1 = require("../flutter/FlutterProjectDetector");
suite("SyncView Extension Test Suite", () => {
    vscode.window.showInformationMessage("Starting SyncView tests...");
    suiteSetup(async () => {
        // Initialize logger for tests
        const context = {
            subscriptions: [],
        };
        Logger_1.Logger.initialize(context);
    });
    suite("Logger Tests", () => {
        test("Logger should log messages", () => {
            // Test that logger doesn't throw errors
            assert.doesNotThrow(() => {
                Logger_1.Logger.info("Test info message");
                Logger_1.Logger.warn("Test warning message");
                Logger_1.Logger.debug("Test debug message");
            });
        });
    });
    suite("FileUtils Tests", () => {
        test("Should normalize paths correctly", () => {
            const testPath = "folder\\subfolder\\file.txt";
            const normalized = FileUtils_1.FileUtils.normalizePath(testPath);
            assert.strictEqual(normalized, "folder/subfolder/file.txt");
        });
        test("Should get file extension correctly", () => {
            assert.strictEqual(FileUtils_1.FileUtils.getFileExtension("test.dart"), ".dart");
            assert.strictEqual(FileUtils_1.FileUtils.getFileExtension("test.yaml"), ".yaml");
            assert.strictEqual(FileUtils_1.FileUtils.getFileExtension("test"), "");
        });
        test("Should get file name without extension", () => {
            assert.strictEqual(FileUtils_1.FileUtils.getFileNameWithoutExtension("test.dart"), "test");
            assert.strictEqual(FileUtils_1.FileUtils.getFileNameWithoutExtension("my-file.test.js"), "my-file.test");
        });
        test("Should check if path is absolute", () => {
            if (process.platform === "win32") {
                assert.strictEqual(FileUtils_1.FileUtils.isAbsolutePath("C:\\test"), true);
                assert.strictEqual(FileUtils_1.FileUtils.isAbsolutePath("test"), false);
            }
            else {
                assert.strictEqual(FileUtils_1.FileUtils.isAbsolutePath("/test"), true);
                assert.strictEqual(FileUtils_1.FileUtils.isAbsolutePath("test"), false);
            }
        });
    });
    suite("ProcessUtils Tests", () => {
        test("Should check command availability", async () => {
            // Test with a command that should exist on all systems
            const nodeAvailable = await ProcessUtils_1.ProcessUtils.isCommandAvailable("node");
            assert.strictEqual(nodeAvailable, true);
        });
        test("Should handle non-existent commands", async () => {
            const fakeCommandAvailable = await ProcessUtils_1.ProcessUtils.isCommandAvailable("this-command-does-not-exist-12345");
            assert.strictEqual(fakeCommandAvailable, false);
        });
    });
    suite("FlutterProjectDetector Tests", () => {
        let detector;
        setup(() => {
            detector = new FlutterProjectDetector_1.FlutterProjectDetector();
        });
        test("Should create detector instance", () => {
            assert.ok(detector);
        });
        test("Should validate Flutter project structure", async () => {
            // This test would need a mock Flutter project structure
            // For now, just test that the method doesn't throw
            const result = await detector.validateFlutterProject("/non-existent-path");
            assert.strictEqual(result, false);
        });
    });
    suite("Extension Commands", () => {
        test("Extension should be present", () => {
            const extension = vscode.extensions.getExtension("undefined_publisher.devgen-syncview");
            // Extension might not be loaded in test environment, so just check it doesn't throw
            assert.doesNotThrow(() => {
                if (extension) {
                    assert.ok(extension);
                }
            });
        });
        test("Commands should be registered", async () => {
            const commands = await vscode.commands.getCommands(true);
            // Check for some of our main commands
            const syncViewCommands = commands.filter((cmd) => cmd.startsWith("syncview."));
            // In test environment, commands might not be registered yet
            // So we just verify the test doesn't throw an error
            assert.doesNotThrow(() => {
                assert.ok(Array.isArray(syncViewCommands));
            });
        });
    });
});
//# sourceMappingURL=extension.test.js.map