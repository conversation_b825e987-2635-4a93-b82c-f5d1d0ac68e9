{"name": "vscode-css-languageserver", "description": "CSS/LESS/SCSS language server", "version": "1.0.0", "author": "Microsoft Corporation", "license": "MIT", "engines": {"node": "*"}, "main": "./out/node/cssServerMain", "browser": "./dist/browser/cssServerMain", "dependencies": {"@vscode/l10n": "^0.0.18", "vscode-css-languageservice": "^6.3.5", "vscode-languageserver": "^10.0.0-next.11", "vscode-uri": "^3.1.0"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "20.x"}, "scripts": {"compile": "gulp compile-extension:css-language-features-server", "watch": "gulp watch-extension:css-language-features-server", "install-service-next": "npm install vscode-css-languageservice", "install-service-local": "npm link vscode-css-languageservice", "install-server-next": "npm install vscode-languageserver@next", "install-server-local": "npm install vscode-languageserver", "test": "node ./test/index.js"}}