import * as http from 'http';
import { Logger } from '../utils/Logger';

export interface CorsOptions {
    origins: string[];
    methods: string[];
    headers: string[];
    credentials: boolean;
    maxAge: number;
}

/**
 * Handles CORS (Cross-Origin Resource Sharing) for the proxy server
 */
export class CorsHandler {
    private options: CorsOptions;

    constructor(options?: Partial<CorsOptions>) {
        this.options = {
            origins: ['*'],
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
            headers: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
            credentials: true,
            maxAge: 86400, // 24 hours
            ...options
        };
    }

    /**
     * Apply CORS headers to the response
     */
    public applyCorsHeaders(req: http.IncomingMessage, res: http.ServerResponse): void {
        const origin = req.headers.origin;
        
        // Set Access-Control-Allow-Origin
        if (this.isOriginAllowed(origin)) {
            res.setHeader('Access-Control-Allow-Origin', origin || '*');
        } else if (this.options.origins.includes('*')) {
            res.setHeader('Access-Control-Allow-Origin', '*');
        }

        // Set Access-Control-Allow-Methods
        res.setHeader('Access-Control-Allow-Methods', this.options.methods.join(', '));

        // Set Access-Control-Allow-Headers
        const requestHeaders = req.headers['access-control-request-headers'];
        if (requestHeaders) {
            res.setHeader('Access-Control-Allow-Headers', requestHeaders);
        } else {
            res.setHeader('Access-Control-Allow-Headers', this.options.headers.join(', '));
        }

        // Set Access-Control-Allow-Credentials
        if (this.options.credentials) {
            res.setHeader('Access-Control-Allow-Credentials', 'true');
        }

        // Set Access-Control-Max-Age
        res.setHeader('Access-Control-Max-Age', this.options.maxAge.toString());

        // Additional headers for better compatibility
        res.setHeader('Access-Control-Expose-Headers', 'Content-Length, Content-Type');
        
        Logger.debug(`CORS headers applied for origin: ${origin || 'none'}`);
    }

    /**
     * Check if the origin is allowed
     */
    private isOriginAllowed(origin: string | undefined): boolean {
        if (!origin) {
            return true; // Allow requests without origin (e.g., same-origin)
        }

        if (this.options.origins.includes('*')) {
            return true;
        }

        return this.options.origins.some(allowedOrigin => {
            if (allowedOrigin === origin) {
                return true;
            }

            // Support wildcard subdomains (e.g., *.example.com)
            if (allowedOrigin.startsWith('*.')) {
                const domain = allowedOrigin.substring(2);
                return origin.endsWith('.' + domain) || origin === domain;
            }

            return false;
        });
    }

    /**
     * Configure CORS options
     */
    public configure(options: Partial<CorsOptions>): void {
        this.options = { ...this.options, ...options };
        Logger.info('CORS configuration updated', this.options);
    }

    /**
     * Add allowed origin
     */
    public addOrigin(origin: string): void {
        if (!this.options.origins.includes(origin)) {
            this.options.origins.push(origin);
            Logger.info(`Added CORS origin: ${origin}`);
        }
    }

    /**
     * Remove allowed origin
     */
    public removeOrigin(origin: string): void {
        const index = this.options.origins.indexOf(origin);
        if (index > -1) {
            this.options.origins.splice(index, 1);
            Logger.info(`Removed CORS origin: ${origin}`);
        }
    }

    /**
     * Add allowed method
     */
    public addMethod(method: string): void {
        const upperMethod = method.toUpperCase();
        if (!this.options.methods.includes(upperMethod)) {
            this.options.methods.push(upperMethod);
            Logger.info(`Added CORS method: ${upperMethod}`);
        }
    }

    /**
     * Remove allowed method
     */
    public removeMethod(method: string): void {
        const upperMethod = method.toUpperCase();
        const index = this.options.methods.indexOf(upperMethod);
        if (index > -1) {
            this.options.methods.splice(index, 1);
            Logger.info(`Removed CORS method: ${upperMethod}`);
        }
    }

    /**
     * Add allowed header
     */
    public addHeader(header: string): void {
        if (!this.options.headers.includes(header)) {
            this.options.headers.push(header);
            Logger.info(`Added CORS header: ${header}`);
        }
    }

    /**
     * Remove allowed header
     */
    public removeHeader(header: string): void {
        const index = this.options.headers.indexOf(header);
        if (index > -1) {
            this.options.headers.splice(index, 1);
            Logger.info(`Removed CORS header: ${header}`);
        }
    }

    /**
     * Get current CORS configuration
     */
    public getConfiguration(): CorsOptions {
        return { ...this.options };
    }

    /**
     * Reset CORS configuration to defaults
     */
    public reset(): void {
        this.options = {
            origins: ['*'],
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
            headers: ['Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization'],
            credentials: true,
            maxAge: 86400
        };
        Logger.info('CORS configuration reset to defaults');
    }

    /**
     * Create a middleware function for Express-like servers
     */
    public middleware() {
        return (req: http.IncomingMessage, res: http.ServerResponse, next?: () => void) => {
            this.applyCorsHeaders(req, res);
            
            // Handle preflight requests
            if (req.method === 'OPTIONS') {
                res.writeHead(200);
                res.end();
                return;
            }

            if (next) {
                next();
            }
        };
    }
}
