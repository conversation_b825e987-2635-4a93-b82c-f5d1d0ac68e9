import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { Logger } from '../utils/Logger';

export interface SyncViewConfiguration {
    flutter: {
        sdkPath?: string;
        webPort?: number;
        hotReloadOnSave: boolean;
        debugMode: boolean;
        additionalArgs: string[];
    };
    proxy: {
        port?: number;
        corsEnabled: boolean;
        corsOrigins: string[];
    };
    ui: {
        showStatusBar: boolean;
        autoShowPreview: boolean;
        previewPosition: 'beside' | 'active';
        deviceFrame: 'none' | 'ios' | 'android';
    };
    performance: {
        enableThrottling: boolean;
        maxFrameRate: number;
        memoryLimit: number;
    };
}

/**
 * Manages extension configuration and settings
 */
export class ConfigurationManager extends EventEmitter implements vscode.Disposable {
    private readonly configSection = 'syncview';
    private disposables: vscode.Disposable[] = [];
    private currentConfig: SyncViewConfiguration;

    constructor() {
        super();
        this.currentConfig = this.loadConfiguration();
        this.setupConfigurationWatcher();
    }

    /**
     * Get the current configuration
     */
    public getConfiguration(): SyncViewConfiguration {
        return { ...this.currentConfig };
    }

    /**
     * Get a specific configuration value
     */
    public get<T>(key: string, defaultValue?: T): T {
        const config = vscode.workspace.getConfiguration(this.configSection);
        return config.get<T>(key, defaultValue as T);
    }

    /**
     * Update a configuration value
     */
    public async update(key: string, value: any, target?: vscode.ConfigurationTarget): Promise<void> {
        const config = vscode.workspace.getConfiguration(this.configSection);
        await config.update(key, value, target);
        Logger.info(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
    }

    /**
     * Get Flutter SDK path
     */
    public getFlutterSdkPath(): string | undefined {
        return this.currentConfig.flutter.sdkPath || this.detectFlutterSdkPath();
    }

    /**
     * Get Flutter web port
     */
    public getFlutterWebPort(): number | undefined {
        return this.currentConfig.flutter.webPort;
    }

    /**
     * Check if hot reload on save is enabled
     */
    public isHotReloadOnSaveEnabled(): boolean {
        return this.currentConfig.flutter.hotReloadOnSave;
    }

    /**
     * Check if debug mode is enabled
     */
    public isDebugModeEnabled(): boolean {
        return this.currentConfig.flutter.debugMode;
    }

    /**
     * Get additional Flutter arguments
     */
    public getFlutterAdditionalArgs(): string[] {
        return this.currentConfig.flutter.additionalArgs;
    }

    /**
     * Get proxy port
     */
    public getProxyPort(): number | undefined {
        return this.currentConfig.proxy.port;
    }

    /**
     * Check if CORS is enabled
     */
    public isCorsEnabled(): boolean {
        return this.currentConfig.proxy.corsEnabled;
    }

    /**
     * Get CORS origins
     */
    public getCorsOrigins(): string[] {
        return this.currentConfig.proxy.corsOrigins;
    }

    /**
     * Check if status bar should be shown
     */
    public shouldShowStatusBar(): boolean {
        return this.currentConfig.ui.showStatusBar;
    }

    /**
     * Check if preview should auto-show
     */
    public shouldAutoShowPreview(): boolean {
        return this.currentConfig.ui.autoShowPreview;
    }

    /**
     * Get preview position
     */
    public getPreviewPosition(): 'beside' | 'active' {
        return this.currentConfig.ui.previewPosition;
    }

    /**
     * Get device frame setting
     */
    public getDeviceFrame(): 'none' | 'ios' | 'android' {
        return this.currentConfig.ui.deviceFrame;
    }

    /**
     * Check if performance throttling is enabled
     */
    public isThrottlingEnabled(): boolean {
        return this.currentConfig.performance.enableThrottling;
    }

    /**
     * Get maximum frame rate
     */
    public getMaxFrameRate(): number {
        return this.currentConfig.performance.maxFrameRate;
    }

    /**
     * Get memory limit
     */
    public getMemoryLimit(): number {
        return this.currentConfig.performance.memoryLimit;
    }

    /**
     * Load configuration from VS Code settings
     */
    private loadConfiguration(): SyncViewConfiguration {
        const config = vscode.workspace.getConfiguration(this.configSection);

        return {
            flutter: {
                sdkPath: config.get('flutter.sdkPath'),
                webPort: config.get('flutter.webPort'),
                hotReloadOnSave: config.get('flutter.hotReloadOnSave', true),
                debugMode: config.get('flutter.debugMode', false),
                additionalArgs: config.get('flutter.additionalArgs', [])
            },
            proxy: {
                port: config.get('proxy.port'),
                corsEnabled: config.get('proxy.corsEnabled', true),
                corsOrigins: config.get('proxy.corsOrigins', ['*'])
            },
            ui: {
                showStatusBar: config.get('ui.showStatusBar', true),
                autoShowPreview: config.get('ui.autoShowPreview', true),
                previewPosition: config.get('ui.previewPosition', 'beside'),
                deviceFrame: config.get('ui.deviceFrame', 'none')
            },
            performance: {
                enableThrottling: config.get('performance.enableThrottling', false),
                maxFrameRate: config.get('performance.maxFrameRate', 60),
                memoryLimit: config.get('performance.memoryLimit', 512)
            }
        };
    }

    /**
     * Setup configuration change watcher
     */
    private setupConfigurationWatcher(): void {
        const watcher = vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration(this.configSection)) {
                const oldConfig = this.currentConfig;
                this.currentConfig = this.loadConfiguration();
                
                this.emit('configurationChanged', {
                    oldConfig,
                    newConfig: this.currentConfig
                });

                Logger.info('Configuration changed');
            }
        });

        this.disposables.push(watcher);
    }

    /**
     * Detect Flutter SDK path from environment
     */
    private detectFlutterSdkPath(): string | undefined {
        // Try to detect Flutter SDK from PATH or common locations
        const possiblePaths = [
            process.env.FLUTTER_ROOT,
            process.env.FLUTTER_SDK,
            '/usr/local/flutter',
            '/opt/flutter',
            'C:\\flutter',
            'C:\\tools\\flutter'
        ];

        // This is a simplified detection - in a real implementation,
        // you would check if the flutter binary exists at these paths
        return possiblePaths.find(path => path && path.length > 0);
    }

    /**
     * Validate configuration
     */
    public validateConfiguration(): string[] {
        const errors: string[] = [];

        // Validate Flutter configuration
        if (this.currentConfig.flutter.webPort && 
            (this.currentConfig.flutter.webPort < 1024 || this.currentConfig.flutter.webPort > 65535)) {
            errors.push('Flutter web port must be between 1024 and 65535');
        }

        // Validate proxy configuration
        if (this.currentConfig.proxy.port && 
            (this.currentConfig.proxy.port < 1024 || this.currentConfig.proxy.port > 65535)) {
            errors.push('Proxy port must be between 1024 and 65535');
        }

        // Validate performance configuration
        if (this.currentConfig.performance.maxFrameRate < 1 || this.currentConfig.performance.maxFrameRate > 120) {
            errors.push('Max frame rate must be between 1 and 120');
        }

        if (this.currentConfig.performance.memoryLimit < 128 || this.currentConfig.performance.memoryLimit > 4096) {
            errors.push('Memory limit must be between 128 and 4096 MB');
        }

        return errors;
    }

    /**
     * Reset configuration to defaults
     */
    public async resetToDefaults(): Promise<void> {
        const config = vscode.workspace.getConfiguration(this.configSection);
        const keys = [
            'flutter.sdkPath',
            'flutter.webPort',
            'flutter.hotReloadOnSave',
            'flutter.debugMode',
            'flutter.additionalArgs',
            'proxy.port',
            'proxy.corsEnabled',
            'proxy.corsOrigins',
            'ui.showStatusBar',
            'ui.autoShowPreview',
            'ui.previewPosition',
            'ui.deviceFrame',
            'performance.enableThrottling',
            'performance.maxFrameRate',
            'performance.memoryLimit'
        ];

        for (const key of keys) {
            await config.update(key, undefined, vscode.ConfigurationTarget.Global);
        }

        Logger.info('Configuration reset to defaults');
    }

    /**
     * Dispose all resources
     */
    public dispose(): void {
        this.disposables.forEach(disposable => disposable.dispose());
        this.disposables.length = 0;
        this.removeAllListeners();
    }
}
