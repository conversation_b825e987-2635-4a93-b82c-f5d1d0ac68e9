{"name": "make", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "*"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin fadeevab/make.tmbundle Syntaxes/Makefile.plist ./syntaxes/make.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "makefile", "aliases": ["<PERSON><PERSON><PERSON>", "makefile"], "extensions": [".mak", ".mk"], "filenames": ["<PERSON><PERSON><PERSON>", "makefile", "GNUmakefile", "OCamlMakefile"], "firstLine": "^#!\\s*/usr/bin/make", "configuration": "./language-configuration.json"}], "grammars": [{"language": "makefile", "scopeName": "source.makefile", "path": "./syntaxes/make.tmLanguage.json", "tokenTypes": {"string.interpolated": "other"}}], "configurationDefaults": {"[makefile]": {"editor.insertSpaces": false}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}