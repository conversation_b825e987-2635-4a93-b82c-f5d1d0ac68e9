{"version": 3, "file": "ProxyServerManager.js", "sourceRoot": "", "sources": ["../../src/proxy/ProxyServerManager.ts"], "names": [], "mappings": ";;;AACA,mCAAsC;AACtC,+CAA4C;AAC5C,+CAA4C;AAC5C,4CAAyC;AAEzC;;GAEG;AACH,MAAa,kBAAmB,SAAQ,qBAAY;IACxC,WAAW,CAA0B;IACrC,WAAW,CAAc;IACzB,eAAe,GAAG,KAAK,CAAC;IACxB,eAAe,CAAqB;IACpC,SAAS,CAAqB;IAEtC;QACI,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK;QACd,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC,eAAgB,CAAC;QACjC,CAAC;QAED,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAExC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAEhD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;YAC5B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;YAEhC,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAErC,eAAM,CAAC,IAAI,CAAC,4BAA4B,QAAQ,EAAE,CAAC,CAAC;YACpD,OAAO,QAAQ,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,IAAI;QACb,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YAC7C,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YAC3C,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YAExC,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAE9B,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACjC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;YAE7B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC3B,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAErD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,GAAW;QAC3B,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;QAErB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACnC,eAAM,CAAC,IAAI,CAAC,6BAA6B,GAAG,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,4CAA4C;QAC5C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACvB,eAAM,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED;;OAEG;IACI,WAAW;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,YAAY;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO;QAChB,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACtB,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,yBAAyB;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO;QACX,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,GAAQ,EAAE,EAAE;YACxC,eAAM,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YAC1C,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,KAAY,EAAE,EAAE;YAChD,eAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,QAAa,EAAE,EAAE;YACnD,eAAM,CAAC,KAAK,CAAC,mBAAmB,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAY;QAC7B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACpC,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACtB,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;CACJ;AAnLD,gDAmLC"}