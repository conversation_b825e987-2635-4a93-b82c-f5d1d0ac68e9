"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandManager = void 0;
const vscode = __importStar(require("vscode"));
const Logger_1 = require("../utils/Logger");
/**
 * Manages all VS Code commands for the SyncView extension
 */
class CommandManager {
    webViewManager;
    flutterManager;
    statusBarManager;
    disposables = [];
    constructor(webViewManager, flutterManager, statusBarManager) {
        this.webViewManager = webViewManager;
        this.flutterManager = flutterManager;
        this.statusBarManager = statusBarManager;
    }
    /**
     * Register all extension commands
     */
    registerCommands(context) {
        const commands = [
            // Main preview commands
            vscode.commands.registerCommand("syncview.startPreview", () => this.startPreview()),
            vscode.commands.registerCommand("syncview.stopPreview", () => this.stopPreview()),
            vscode.commands.registerCommand("syncview.restartPreview", () => this.restartPreview()),
            vscode.commands.registerCommand("syncview.togglePreview", () => this.togglePreview()),
            // WebView commands
            vscode.commands.registerCommand("syncview.showPreview", () => this.showPreview()),
            vscode.commands.registerCommand("syncview.hidePreview", () => this.hidePreview()),
            vscode.commands.registerCommand("syncview.refreshPreview", () => this.refreshPreview()),
            // Flutter commands
            vscode.commands.registerCommand("syncview.hotReload", () => this.hotReload()),
            vscode.commands.registerCommand("syncview.hotRestart", () => this.hotRestart()),
            // Utility commands
            vscode.commands.registerCommand("syncview.openSettings", () => this.openSettings()),
            vscode.commands.registerCommand("syncview.showLogs", () => this.showLogs()),
            vscode.commands.registerCommand("syncview.clearLogs", () => this.clearLogs()),
            vscode.commands.registerCommand("syncview.checkFlutter", () => this.checkFlutterInstallation()),
            // Legacy command (for backward compatibility)
            vscode.commands.registerCommand("devgen-syncview.helloWorld", () => this.showWelcome()),
        ];
        this.disposables.push(...commands);
        context.subscriptions.push(...commands);
        Logger_1.Logger.info("SyncView commands registered successfully");
    }
    /**
     * Start the Flutter preview
     */
    async startPreview() {
        try {
            Logger_1.Logger.info("Starting Flutter preview...");
            this.statusBarManager.setStatus("starting");
            await this.flutterManager.startFlutterProcess();
            await this.webViewManager.createPreviewPanel();
            Logger_1.Logger.info("Flutter preview started successfully");
        }
        catch (error) {
            Logger_1.Logger.error("Failed to start Flutter preview", error);
            this.statusBarManager.setStatus("error");
            vscode.window.showErrorMessage("Failed to start Flutter preview. Check the output panel for details.");
        }
    }
    /**
     * Stop the Flutter preview
     */
    async stopPreview() {
        try {
            Logger_1.Logger.info("Stopping Flutter preview...");
            this.statusBarManager.setStatus("stopping");
            await this.flutterManager.stopFlutterProcess();
            this.webViewManager.disposePreviewPanel();
            this.statusBarManager.setStatus("stopped");
            Logger_1.Logger.info("Flutter preview stopped successfully");
        }
        catch (error) {
            Logger_1.Logger.error("Failed to stop Flutter preview", error);
            vscode.window.showErrorMessage("Failed to stop Flutter preview. Check the output panel for details.");
        }
    }
    /**
     * Restart the Flutter preview
     */
    async restartPreview() {
        try {
            Logger_1.Logger.info("Restarting Flutter preview...");
            await this.stopPreview();
            await this.startPreview();
        }
        catch (error) {
            Logger_1.Logger.error("Failed to restart Flutter preview", error);
            vscode.window.showErrorMessage("Failed to restart Flutter preview. Check the output panel for details.");
        }
    }
    /**
     * Toggle the Flutter preview on/off
     */
    async togglePreview() {
        const isRunning = this.flutterManager.isRunning();
        if (isRunning) {
            await this.stopPreview();
        }
        else {
            await this.startPreview();
        }
    }
    /**
     * Show the preview panel
     */
    showPreview() {
        this.webViewManager.showPreviewPanel();
    }
    /**
     * Hide the preview panel
     */
    hidePreview() {
        this.webViewManager.hidePreviewPanel();
    }
    /**
     * Refresh the preview
     */
    async refreshPreview() {
        try {
            Logger_1.Logger.info("Refreshing Flutter preview...");
            await this.webViewManager.refreshPreview();
            Logger_1.Logger.info("Flutter preview refreshed successfully");
        }
        catch (error) {
            Logger_1.Logger.error("Failed to refresh Flutter preview", error);
            vscode.window.showErrorMessage("Failed to refresh Flutter preview.");
        }
    }
    /**
     * Trigger hot reload
     */
    async hotReload() {
        try {
            Logger_1.Logger.info("Triggering hot reload...");
            await this.flutterManager.hotReload();
            Logger_1.Logger.info("Hot reload completed successfully");
        }
        catch (error) {
            Logger_1.Logger.error("Failed to trigger hot reload", error);
            vscode.window.showErrorMessage("Failed to trigger hot reload.");
        }
    }
    /**
     * Trigger hot restart
     */
    async hotRestart() {
        try {
            Logger_1.Logger.info("Triggering hot restart...");
            await this.flutterManager.hotRestart();
            Logger_1.Logger.info("Hot restart completed successfully");
        }
        catch (error) {
            Logger_1.Logger.error("Failed to trigger hot restart", error);
            vscode.window.showErrorMessage("Failed to trigger hot restart.");
        }
    }
    /**
     * Open extension settings
     */
    openSettings() {
        vscode.commands.executeCommand("workbench.action.openSettings", "syncview");
    }
    /**
     * Show logs in output panel
     */
    showLogs() {
        Logger_1.Logger.show();
    }
    /**
     * Clear logs
     */
    clearLogs() {
        Logger_1.Logger.clear();
    }
    /**
     * Check Flutter installation
     */
    async checkFlutterInstallation() {
        try {
            Logger_1.Logger.info("Checking Flutter installation...");
            // Create a terminal to run flutter doctor
            const terminal = vscode.window.createTerminal("Flutter Doctor");
            terminal.sendText("flutter doctor -v");
            terminal.show();
            vscode.window
                .showInformationMessage("Flutter Doctor is running in the terminal. Check the output for any issues.", "Open Settings")
                .then((selection) => {
                if (selection === "Open Settings") {
                    this.openSettings();
                }
            });
        }
        catch (error) {
            Logger_1.Logger.error("Failed to check Flutter installation", error);
            vscode.window.showErrorMessage("Failed to check Flutter installation.");
        }
    }
    /**
     * Show welcome message (legacy command)
     */
    showWelcome() {
        vscode.window
            .showInformationMessage("Welcome to SyncView! Use the status bar controls or command palette to start your Flutter preview.", "Start Preview")
            .then((selection) => {
            if (selection === "Start Preview") {
                this.startPreview();
            }
        });
    }
    /**
     * Dispose all command registrations
     */
    dispose() {
        this.disposables.forEach((disposable) => disposable.dispose());
        this.disposables.length = 0;
    }
}
exports.CommandManager = CommandManager;
//# sourceMappingURL=CommandManager.js.map