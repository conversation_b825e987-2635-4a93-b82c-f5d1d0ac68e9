"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const FlutterManager_1 = require("../../flutter/FlutterManager");
const Logger_1 = require("../../utils/Logger");
suite("FlutterManager Tests", () => {
    let flutterManager;
    suiteSetup(async () => {
        // Initialize logger for tests
        const context = {
            subscriptions: [],
        };
        Logger_1.Logger.initialize(context);
    });
    setup(() => {
        flutterManager = new FlutterManager_1.FlutterManager();
    });
    teardown(() => {
        if (flutterManager) {
            flutterManager.dispose();
        }
    });
    test("Should create FlutterManager instance", () => {
        assert.ok(flutterManager);
        assert.strictEqual(flutterManager.isRunning(), false);
    });
    test("Should handle process state correctly", () => {
        assert.strictEqual(flutterManager.isRunning(), false);
        assert.strictEqual(flutterManager.getServerUrl(), undefined);
    });
    test("Should emit events correctly", (done) => {
        let eventReceived = false;
        flutterManager.on("error", (error) => {
            eventReceived = true;
            assert.ok(error);
            done();
        });
        // Simulate an error event
        flutterManager.emit("error", new Error("Test error"));
        // Fallback timeout
        setTimeout(() => {
            if (!eventReceived) {
                done(new Error("Event not received"));
            }
        }, 1000);
    });
    test("Should handle hot reload when not running", async () => {
        try {
            await flutterManager.hotReload();
            assert.fail("Should have thrown an error");
        }
        catch (error) {
            assert.ok(error);
            assert.ok(error.message.includes("not running"));
        }
    });
    test("Should handle hot restart when not running", async () => {
        try {
            await flutterManager.hotRestart();
            assert.fail("Should have thrown an error");
        }
        catch (error) {
            assert.ok(error);
            assert.ok(error.message.includes("not running"));
        }
    });
    test("Should dispose cleanly", () => {
        assert.doesNotThrow(() => {
            flutterManager.dispose();
        });
    });
});
//# sourceMappingURL=FlutterManager.test.js.map