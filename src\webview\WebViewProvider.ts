import * as vscode from "vscode";
import { Logger } from "../utils/Logger";

/**
 * Custom WebView provider for Flutter preview with enhanced features
 */
export class WebViewProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = "syncview.flutterPreview";

  private _view?: vscode.WebviewView;
  private _currentUrl?: string;
  private _isLoading = false;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly _context: vscode.ExtensionContext
  ) {}

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    _context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri],
    };

    webviewView.webview.html = this._getLoadingHtml();

    // Handle messages from webview
    webviewView.webview.onDidReceiveMessage(
      (message) => this._handleMessage(message),
      undefined,
      this._context.subscriptions
    );

    Logger.info("WebView provider resolved");
  }

  /**
   * Update the preview URL
   */
  public updatePreview(url: string): void {
    this._currentUrl = url;
    if (this._view) {
      this._view.webview.html = this._getPreviewHtml(url);
      Logger.info(`WebView updated with URL: ${url}`);
    }
  }

  /**
   * Show loading state
   */
  public showLoading(): void {
    this._isLoading = true;
    if (this._view) {
      this._view.webview.html = this._getLoadingHtml();
    }
  }

  /**
   * Show error state
   */
  public showError(error: string): void {
    this._isLoading = false;
    if (this._view) {
      this._view.webview.html = this._getErrorHtml(error);
    }
  }

  /**
   * Refresh the current preview
   */
  public refresh(): void {
    if (this._currentUrl && this._view) {
      this._view.webview.html = this._getPreviewHtml(this._currentUrl);
    }
  }

  /**
   * Handle messages from webview
   */
  private _handleMessage(message: any): void {
    switch (message.type) {
      case "ready":
        Logger.info("WebView ready");
        break;
      case "error":
        Logger.error("WebView error", message.error);
        vscode.window.showErrorMessage(`Preview error: ${message.error}`);
        break;
      case "reload":
        this.refresh();
        break;
      case "openDevTools":
        vscode.commands.executeCommand(
          "workbench.action.webview.openDeveloperTools"
        );
        break;
      default:
        Logger.debug("Unknown webview message", message);
    }
  }

  /**
   * Get loading HTML content
   */
  private _getLoadingHtml(): string {
    const nonce = this._getNonce();

    return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <title>Flutter Preview</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    font-family: var(--vscode-font-family);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    box-sizing: border-box;
                }
                .loading-container {
                    text-align: center;
                    max-width: 300px;
                }
                .spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid var(--vscode-progressBar-background);
                    border-top: 4px solid var(--vscode-progressBar-foreground);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 20px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                h2 {
                    margin: 0 0 10px 0;
                    color: var(--vscode-foreground);
                }
                p {
                    margin: 5px 0;
                    color: var(--vscode-descriptionForeground);
                    font-size: 14px;
                }
                .status {
                    margin-top: 20px;
                    padding: 10px;
                    background: var(--vscode-textBlockQuote-background);
                    border-left: 4px solid var(--vscode-textBlockQuote-border);
                    border-radius: 4px;
                }
            </style>
        </head>
        <body>
            <div class="loading-container">
                <div class="spinner"></div>
                <h2>Starting Flutter Preview</h2>
                <p>Initializing Flutter development server...</p>
                <div class="status">
                    <p>This may take a moment on first run</p>
                </div>
            </div>
            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();
                vscode.postMessage({ type: 'ready' });
            </script>
        </body>
        </html>`;
  }

  /**
   * Get preview HTML content
   */
  private _getPreviewHtml(url: string): string {
    const nonce = this._getNonce();

    return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; frame-src ${url} http: https:; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <title>Flutter Preview</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                    background: var(--vscode-editor-background);
                }
                iframe {
                    width: 100%;
                    height: 100vh;
                    border: none;
                    background: white;
                }
                .error-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-errorForeground);
                    display: none;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    padding: 20px;
                    text-align: center;
                }
                .toolbar {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    z-index: 1000;
                    display: flex;
                    gap: 5px;
                }
                .toolbar button {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                }
                .toolbar button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
            </style>
        </head>
        <body>
            <div class="toolbar">
                <button onclick="refreshPreview()">↻ Refresh</button>
                <button onclick="openDevTools()">🔧 DevTools</button>
            </div>
            <iframe
                id="preview-frame"
                src="${url}"
                onload="handleLoad()"
                onerror="handleError()">
            </iframe>
            <div class="error-overlay" id="error-overlay">
                <h3>Failed to load Flutter preview</h3>
                <p>The Flutter development server may not be ready yet.</p>
                <button onclick="refreshPreview()">Try Again</button>
            </div>

            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();

                function handleLoad() {
                    document.getElementById('error-overlay').style.display = 'none';
                    vscode.postMessage({ type: 'loaded' });
                }

                function handleError() {
                    document.getElementById('error-overlay').style.display = 'flex';
                    vscode.postMessage({
                        type: 'error',
                        error: 'Failed to load Flutter preview'
                    });
                }

                function refreshPreview() {
                    vscode.postMessage({ type: 'reload' });
                }

                function openDevTools() {
                    vscode.postMessage({ type: 'openDevTools' });
                }

                // Handle iframe errors
                window.addEventListener('message', function(event) {
                    if (event.data.type === 'flutter-error') {
                        handleError();
                    }
                });

                // Auto-refresh on connection errors
                let retryCount = 0;
                const maxRetries = 3;

                function autoRetry() {
                    if (retryCount < maxRetries) {
                        retryCount++;
                        setTimeout(() => {
                            document.getElementById('preview-frame').src = "${url}";
                        }, 2000 * retryCount);
                    }
                }

                document.getElementById('preview-frame').addEventListener('error', autoRetry);
            </script>
        </body>
        </html>`;
  }

  /**
   * Get error HTML content
   */
  private _getErrorHtml(error: string): string {
    const nonce = this._getNonce();

    return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <title>Flutter Preview Error</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-errorForeground);
                    font-family: var(--vscode-font-family);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    text-align: center;
                }
                .error-icon {
                    font-size: 48px;
                    margin-bottom: 20px;
                }
                h2 {
                    margin: 0 0 10px 0;
                }
                .error-message {
                    background: var(--vscode-inputValidation-errorBackground);
                    border: 1px solid var(--vscode-inputValidation-errorBorder);
                    padding: 15px;
                    border-radius: 4px;
                    margin: 20px 0;
                    max-width: 400px;
                }
                button {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin: 5px;
                }
                button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
            </style>
        </head>
        <body>
            <div class="error-icon">⚠️</div>
            <h2>Flutter Preview Error</h2>
            <div class="error-message">
                <p>${error}</p>
            </div>
            <button onclick="retry()">Retry</button>
            <button onclick="showLogs()">Show Logs</button>

            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();

                function retry() {
                    vscode.postMessage({ type: 'reload' });
                }

                function showLogs() {
                    vscode.postMessage({ type: 'showLogs' });
                }
            </script>
        </body>
        </html>`;
  }

  /**
   * Generate a nonce for CSP
   */
  private _getNonce(): string {
    let text = "";
    const possible =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    for (let i = 0; i < 32; i++) {
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
  }
}
