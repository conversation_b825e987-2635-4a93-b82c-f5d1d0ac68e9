{"version": 3, "file": "FlutterProjectDetector.js", "sourceRoot": "", "sources": ["../../src/flutter/FlutterProjectDetector.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAC7B,uCAAyB;AACzB,4CAAyC;AAEzC;;GAEG;AACH,MAAa,sBAAsB;IAE/B;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC3B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAE3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrD,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC1C,OAAO,SAAS,CAAC;QACrB,CAAC;QAED,kDAAkD;QAClD,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC7E,IAAI,WAAW,EAAE,CAAC;gBACd,OAAO,WAAW,CAAC;YACvB,CAAC;QACL,CAAC;QAED,2DAA2D;QAC3D,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oCAAoC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACvF,IAAI,WAAW,EAAE,CAAC;gBACd,OAAO,WAAW,CAAC;YACvB,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B,CAAC,WAAmB;QACvD,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAE3D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC9B,OAAO,SAAS,CAAC;YACrB,CAAC;YAED,MAAM,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE5D,kCAAkC;YAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;gBACxC,eAAM,CAAC,IAAI,CAAC,gCAAgC,WAAW,EAAE,CAAC,CAAC;gBAC3D,OAAO,WAAW,CAAC;YACvB,CAAC;YAED,OAAO,SAAS,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,sCAAsC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oCAAoC,CAAC,QAAgB,EAAE,WAAmB,CAAC;QACrF,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAElE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC1B,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;oBAChD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC;oBAEnE,IAAI,WAAW,EAAE,CAAC;wBACd,OAAO,WAAW,CAAC;oBACvB,CAAC;oBAED,oCAAoC;oBACpC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;wBACf,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,oCAAoC,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;wBAC/F,IAAI,eAAe,EAAE,CAAC;4BAClB,OAAO,eAAe,CAAC;wBAC3B,CAAC;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,qCAAqC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QACzE,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,cAAsB;QAC3C,+BAA+B;QAC/B,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEtE,mCAAmC;QACnC,MAAM,aAAa,GAAG,yBAAyB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAErE,OAAO,oBAAoB,IAAI,aAAa,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAe;QACvC,MAAM,QAAQ,GAAG;YACb,cAAc;YACd,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,YAAY;YACZ,KAAK;YACL,SAAS;YACT,KAAK;YACL,SAAS;YACT,OAAO;YACP,OAAO;SACV,CAAC;QAEF,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CAAC,WAAmB;QACnD,IAAI,CAAC;YACD,MAAM,aAAa,GAAG;gBAClB,cAAc;gBACd,eAAe;aAClB,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;gBAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;gBAC9C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC3B,eAAM,CAAC,IAAI,CAAC,kCAAkC,IAAI,EAAE,CAAC,CAAC;oBACtD,OAAO,KAAK,CAAC;gBACjB,CAAC;YACL,CAAC;YAED,kCAAkC;YAClC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzB,eAAM,CAAC,IAAI,CAAC,sFAAsF,CAAC,CAAC;gBACpG,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,OAAO,IAAI,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,uCAAuC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YAC1E,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,WAAmB;QAC3C,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YAC3D,MAAM,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAE5D,qDAAqD;YACrD,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAC1D,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YAChE,MAAM,gBAAgB,GAAG,cAAc,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAExE,OAAO;gBACH,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;gBACjD,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO;gBACxD,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;gBAC/D,IAAI,EAAE,WAAW;aACpB,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,WAAmB;QAC3C,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAElD,OAAO,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,kCAAkC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB;QAC9B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAE3D,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACpB,OAAO,QAAQ,CAAC;QACpB,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACnE,CAAC;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,QAAkB,EAAE,WAAmB,CAAC;QAC3F,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,CAAC;YACpE,IAAI,WAAW,EAAE,CAAC;gBACd,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3B,OAAO,CAAC,wEAAwE;YACpF,CAAC;YAED,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC;gBAChB,OAAO;YACX,CAAC;YAED,MAAM,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAElE,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC1B,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;oBAChD,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;gBACvE,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,eAAM,CAAC,KAAK,CAAC,wCAAwC,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;CACJ;AAlPD,wDAkPC"}