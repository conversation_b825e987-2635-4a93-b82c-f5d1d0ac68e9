"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotReloadHandler = void 0;
const events_1 = require("events");
const vscode = __importStar(require("vscode"));
const Logger_1 = require("../utils/Logger");
/**
 * Handles Flutter hot reload and hot restart functionality
 */
class HotReloadHandler extends events_1.EventEmitter {
    process;
    fileWatcher;
    hotReloadTimeout;
    isHotReloadEnabled = true;
    lastReloadTime = 0;
    reloadDebounceMs = 1000; // Debounce hot reload requests
    /**
     * Attach to a Flutter process
     */
    attachToProcess(process) {
        this.process = process;
        this.setupFileWatcher();
    }
    /**
     * Setup file system watcher for automatic hot reload
     */
    setupFileWatcher() {
        // Watch for Dart file changes
        this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*.dart', false, // Don't ignore creates
        false, // Don't ignore changes
        true // Ignore deletes (they require hot restart)
        );
        this.fileWatcher.onDidCreate(uri => {
            Logger_1.Logger.debug(`Dart file created: ${uri.fsPath}`);
            this.scheduleHotReload();
        });
        this.fileWatcher.onDidChange(uri => {
            Logger_1.Logger.debug(`Dart file changed: ${uri.fsPath}`);
            this.scheduleHotReload();
        });
        Logger_1.Logger.info('File watcher setup for hot reload');
    }
    /**
     * Schedule a hot reload with debouncing
     */
    scheduleHotReload() {
        if (!this.isHotReloadEnabled) {
            return;
        }
        // Clear existing timeout
        if (this.hotReloadTimeout) {
            clearTimeout(this.hotReloadTimeout);
        }
        // Schedule new hot reload
        this.hotReloadTimeout = setTimeout(() => {
            this.triggerHotReload().catch(error => {
                Logger_1.Logger.error('Scheduled hot reload failed', error);
            });
        }, this.reloadDebounceMs);
    }
    /**
     * Trigger hot reload manually
     */
    async triggerHotReload() {
        if (!this.process || !this.process.stdin || this.process.stdin.destroyed) {
            throw new Error('Flutter process is not available for hot reload');
        }
        const now = Date.now();
        if (now - this.lastReloadTime < this.reloadDebounceMs) {
            Logger_1.Logger.debug('Hot reload skipped due to debouncing');
            return;
        }
        try {
            Logger_1.Logger.info('Triggering hot reload...');
            this.lastReloadTime = now;
            // Send 'r' command to Flutter CLI for hot reload
            this.process.stdin.write('r\n');
            // Wait for hot reload completion or timeout
            await this.waitForHotReloadCompletion();
            this.emit('hotReloadComplete');
            Logger_1.Logger.info('Hot reload completed successfully');
        }
        catch (error) {
            Logger_1.Logger.error('Hot reload failed', error);
            throw error;
        }
    }
    /**
     * Trigger hot restart manually
     */
    async triggerHotRestart() {
        if (!this.process || !this.process.stdin || this.process.stdin.destroyed) {
            throw new Error('Flutter process is not available for hot restart');
        }
        try {
            Logger_1.Logger.info('Triggering hot restart...');
            // Send 'R' command to Flutter CLI for hot restart
            this.process.stdin.write('R\n');
            // Wait for hot restart completion or timeout
            await this.waitForHotRestartCompletion();
            this.emit('hotRestartComplete');
            Logger_1.Logger.info('Hot restart completed successfully');
        }
        catch (error) {
            Logger_1.Logger.error('Hot restart failed', error);
            throw error;
        }
    }
    /**
     * Wait for hot reload completion
     */
    waitForHotReloadCompletion(timeoutMs = 10000) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Hot reload timeout'));
            }, timeoutMs);
            const onComplete = () => {
                clearTimeout(timeout);
                this.removeListener('hotReloadComplete', onComplete);
                this.removeListener('error', onError);
                resolve();
            };
            const onError = (error) => {
                clearTimeout(timeout);
                this.removeListener('hotReloadComplete', onComplete);
                this.removeListener('error', onError);
                reject(error);
            };
            this.once('hotReloadComplete', onComplete);
            this.once('error', onError);
        });
    }
    /**
     * Wait for hot restart completion
     */
    waitForHotRestartCompletion(timeoutMs = 15000) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Hot restart timeout'));
            }, timeoutMs);
            const onComplete = () => {
                clearTimeout(timeout);
                this.removeListener('hotRestartComplete', onComplete);
                this.removeListener('error', onError);
                resolve();
            };
            const onError = (error) => {
                clearTimeout(timeout);
                this.removeListener('hotRestartComplete', onComplete);
                this.removeListener('error', onError);
                reject(error);
            };
            this.once('hotRestartComplete', onComplete);
            this.once('error', onError);
        });
    }
    /**
     * Enable or disable hot reload
     */
    setHotReloadEnabled(enabled) {
        this.isHotReloadEnabled = enabled;
        Logger_1.Logger.info(`Hot reload ${enabled ? 'enabled' : 'disabled'}`);
    }
    /**
     * Check if hot reload is enabled
     */
    isEnabled() {
        return this.isHotReloadEnabled;
    }
    /**
     * Get hot reload statistics
     */
    getStats() {
        return {
            enabled: this.isHotReloadEnabled,
            lastReloadTime: this.lastReloadTime,
            debounceMs: this.reloadDebounceMs
        };
    }
    /**
     * Clear any pending hot reload
     */
    clearPendingReload() {
        if (this.hotReloadTimeout) {
            clearTimeout(this.hotReloadTimeout);
            this.hotReloadTimeout = undefined;
        }
    }
    /**
     * Dispose the handler
     */
    dispose() {
        this.clearPendingReload();
        if (this.fileWatcher) {
            this.fileWatcher.dispose();
            this.fileWatcher = undefined;
        }
        this.removeAllListeners();
        this.process = undefined;
    }
}
exports.HotReloadHandler = HotReloadHandler;
//# sourceMappingURL=HotReloadHandler.js.map