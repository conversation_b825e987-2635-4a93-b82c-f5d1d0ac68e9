"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const ExtensionManager_1 = require("../../core/ExtensionManager");
const Logger_1 = require("../../utils/Logger");
suite("ExtensionManager Integration Tests", () => {
    let extensionManager;
    let mockContext;
    let isFirstActivation = true;
    suiteSetup(async () => {
        // Create mock context
        mockContext = {
            subscriptions: [],
            workspaceState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                keys: () => [],
            },
            globalState: {
                get: () => undefined,
                update: () => Promise.resolve(),
                setKeysForSync: () => { },
                keys: () => [],
            },
            extensionPath: __dirname,
            extensionUri: vscode.Uri.file(__dirname),
            environmentVariableCollection: {},
            extensionMode: vscode.ExtensionMode.Test,
            storageUri: vscode.Uri.file(__dirname),
            globalStorageUri: vscode.Uri.file(__dirname),
            logUri: vscode.Uri.file(__dirname),
            secrets: {},
            asAbsolutePath: (relativePath) => relativePath,
            storagePath: __dirname,
            globalStoragePath: __dirname,
            logPath: __dirname,
            extension: {},
            languageModelAccessInformation: {},
        };
        Logger_1.Logger.initialize(mockContext);
    });
    setup(() => {
        extensionManager = new ExtensionManager_1.ExtensionManager(mockContext);
    });
    teardown(() => {
        if (extensionManager) {
            extensionManager.dispose();
        }
    });
    test("Should create ExtensionManager instance", () => {
        assert.ok(extensionManager);
    });
    test("Should activate without errors", () => {
        if (isFirstActivation) {
            assert.doesNotThrow(() => {
                extensionManager.activate();
            });
            isFirstActivation = false;
        }
        else {
            // Skip activation for subsequent tests to avoid WebView provider conflicts
            assert.ok(true, "Skipping activation to avoid WebView provider conflicts");
        }
    });
    test("Should dispose cleanly", () => {
        // Only test disposal, don't activate again
        assert.doesNotThrow(() => {
            extensionManager.dispose();
        });
    });
    test("Should handle multiple activations gracefully", () => {
        // Test that multiple activations don't throw errors (should be handled by isActivated flag)
        assert.doesNotThrow(() => {
            extensionManager.activate(); // This should be ignored due to isActivated flag
        });
    });
});
//# sourceMappingURL=ExtensionManager.test.js.map