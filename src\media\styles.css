/* SyncView Extension Styles */

/* Base styles for WebView content */
:root {
    --syncview-primary: #007acc;
    --syncview-secondary: #1e1e1e;
    --syncview-success: #4caf50;
    --syncview-warning: #ff9800;
    --syncview-error: #f44336;
    --syncview-background: var(--vscode-editor-background);
    --syncview-foreground: var(--vscode-editor-foreground);
    --syncview-border: var(--vscode-panel-border);
}

/* Loading spinner styles */
.syncview-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--vscode-progressBar-background);
    border-top: 4px solid var(--vscode-progressBar-foreground);
    border-radius: 50%;
    animation: syncview-spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes syncview-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Loading container */
.syncview-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: var(--syncview-background);
    color: var(--syncview-foreground);
    font-family: var(--vscode-font-family);
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
}

.syncview-loading h2 {
    margin: 20px 0 10px 0;
    color: var(--vscode-foreground);
    font-weight: 600;
}

.syncview-loading p {
    margin: 5px 0;
    color: var(--vscode-descriptionForeground);
    font-size: 14px;
    line-height: 1.4;
}

/* Status indicator */
.syncview-status {
    margin-top: 20px;
    padding: 12px 16px;
    background: var(--vscode-textBlockQuote-background);
    border-left: 4px solid var(--vscode-textBlockQuote-border);
    border-radius: 4px;
    max-width: 400px;
}

.syncview-status.success {
    background: var(--vscode-inputValidation-infoBackground);
    border-left-color: var(--syncview-success);
}

.syncview-status.warning {
    background: var(--vscode-inputValidation-warningBackground);
    border-left-color: var(--syncview-warning);
}

.syncview-status.error {
    background: var(--vscode-inputValidation-errorBackground);
    border-left-color: var(--syncview-error);
}

/* Error display */
.syncview-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: var(--syncview-background);
    color: var(--vscode-errorForeground);
    font-family: var(--vscode-font-family);
    text-align: center;
    padding: 20px;
}

.syncview-error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.8;
}

.syncview-error h2 {
    margin: 0 0 10px 0;
    color: var(--vscode-errorForeground);
}

.syncview-error-message {
    background: var(--vscode-inputValidation-errorBackground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
    max-width: 500px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    line-height: 1.4;
}

/* Button styles */
.syncview-button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-family: var(--vscode-font-family);
    margin: 4px;
    transition: background-color 0.2s ease;
}

.syncview-button:hover {
    background: var(--vscode-button-hoverBackground);
}

.syncview-button:active {
    background: var(--vscode-button-background);
    transform: translateY(1px);
}

.syncview-button.secondary {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.syncview-button.secondary:hover {
    background: var(--vscode-button-secondaryHoverBackground);
}

/* Toolbar styles */
.syncview-toolbar {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    gap: 6px;
    background: var(--vscode-editor-background);
    padding: 4px;
    border-radius: 6px;
    border: 1px solid var(--vscode-panel-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.syncview-toolbar .syncview-button {
    padding: 6px 10px;
    font-size: 12px;
    margin: 0;
    min-width: auto;
}

/* Preview frame styles */
.syncview-preview-frame {
    width: 100%;
    height: 100vh;
    border: none;
    background: white;
    display: block;
}

/* Overlay styles */
.syncview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--syncview-background);
    display: none;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 999;
}

.syncview-overlay.visible {
    display: flex;
}

/* Device frame styles */
.syncview-device-frame {
    position: relative;
    margin: 20px auto;
    background: #333;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.syncview-device-frame.ios {
    border-radius: 25px;
    background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
}

.syncview-device-frame.android {
    border-radius: 15px;
    background: linear-gradient(145deg, #424242, #212121);
}

.syncview-device-frame .frame-content {
    border-radius: 10px;
    overflow: hidden;
    background: white;
}

/* Progress bar */
.syncview-progress {
    width: 100%;
    height: 4px;
    background: var(--vscode-progressBar-background);
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.syncview-progress-bar {
    height: 100%;
    background: var(--vscode-progressBar-foreground);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.syncview-progress-bar.indeterminate {
    width: 30%;
    animation: syncview-progress-slide 2s infinite;
}

@keyframes syncview-progress-slide {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(400%); }
}

/* Responsive design */
@media (max-width: 768px) {
    .syncview-loading,
    .syncview-error {
        padding: 15px;
    }
    
    .syncview-error-message,
    .syncview-status {
        max-width: 90%;
        padding: 12px;
    }
    
    .syncview-toolbar {
        top: 5px;
        right: 5px;
        gap: 4px;
    }
    
    .syncview-device-frame {
        margin: 10px;
        padding: 15px;
    }
}

/* Dark theme adjustments */
.vscode-dark {
    --syncview-background: #1e1e1e;
    --syncview-foreground: #cccccc;
}

/* Light theme adjustments */
.vscode-light {
    --syncview-background: #ffffff;
    --syncview-foreground: #333333;
}

/* High contrast theme adjustments */
.vscode-high-contrast {
    --syncview-background: #000000;
    --syncview-foreground: #ffffff;
}

/* Accessibility improvements */
.syncview-button:focus {
    outline: 2px solid var(--vscode-focusBorder);
    outline-offset: 2px;
}

.syncview-error-message:focus {
    outline: 2px solid var(--vscode-focusBorder);
}

/* Animation for smooth transitions */
.syncview-fade-in {
    animation: syncview-fadeIn 0.3s ease-in;
}

@keyframes syncview-fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Utility classes */
.syncview-hidden {
    display: none !important;
}

.syncview-visible {
    display: block !important;
}

.syncview-text-center {
    text-align: center;
}

.syncview-mt-10 {
    margin-top: 10px;
}

.syncview-mb-10 {
    margin-bottom: 10px;
}

.syncview-p-10 {
    padding: 10px;
}
