"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUtils = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const vscode = __importStar(require("vscode"));
const Logger_1 = require("./Logger");
/**
 * File system utilities for the SyncView extension
 */
class FileUtils {
    /**
     * Check if a file exists
     */
    static async fileExists(filePath) {
        try {
            await fs.promises.access(filePath, fs.constants.F_OK);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Check if a directory exists
     */
    static async directoryExists(dirPath) {
        try {
            const stats = await fs.promises.stat(dirPath);
            return stats.isDirectory();
        }
        catch {
            return false;
        }
    }
    /**
     * Read file content as string
     */
    static async readFile(filePath) {
        try {
            return await fs.promises.readFile(filePath, "utf8");
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to read file: ${filePath}`, error);
            throw error;
        }
    }
    /**
     * Write content to file
     */
    static async writeFile(filePath, content) {
        try {
            await fs.promises.writeFile(filePath, content, "utf8");
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to write file: ${filePath}`, error);
            throw error;
        }
    }
    /**
     * Create directory recursively
     */
    static async createDirectory(dirPath) {
        try {
            await fs.promises.mkdir(dirPath, { recursive: true });
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to create directory: ${dirPath}`, error);
            throw error;
        }
    }
    /**
     * Delete file
     */
    static async deleteFile(filePath) {
        try {
            await fs.promises.unlink(filePath);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to delete file: ${filePath}`, error);
            throw error;
        }
    }
    /**
     * Delete directory recursively
     */
    static async deleteDirectory(dirPath) {
        try {
            await fs.promises.rmdir(dirPath, { recursive: true });
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to delete directory: ${dirPath}`, error);
            throw error;
        }
    }
    /**
     * Get file stats
     */
    static async getFileStats(filePath) {
        try {
            return await fs.promises.stat(filePath);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to get file stats: ${filePath}`, error);
            throw error;
        }
    }
    /**
     * List directory contents
     */
    static async listDirectory(dirPath) {
        try {
            return await fs.promises.readdir(dirPath);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to list directory: ${dirPath}`, error);
            throw error;
        }
    }
    /**
     * Find files matching a pattern
     */
    static async findFiles(rootPath, pattern, maxDepth = 5) {
        const results = [];
        try {
            await this.findFilesRecursive(rootPath, pattern, results, 0, maxDepth);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to find files in: ${rootPath}`, error);
        }
        return results;
    }
    /**
     * Recursive helper for finding files
     */
    static async findFilesRecursive(currentPath, pattern, results, currentDepth, maxDepth) {
        if (currentDepth >= maxDepth) {
            return;
        }
        try {
            const entries = await fs.promises.readdir(currentPath, {
                withFileTypes: true,
            });
            for (const entry of entries) {
                const fullPath = path.join(currentPath, entry.name);
                if (entry.isFile() && pattern.test(entry.name)) {
                    results.push(fullPath);
                }
                else if (entry.isDirectory() &&
                    !this.shouldSkipDirectory(entry.name)) {
                    await this.findFilesRecursive(fullPath, pattern, results, currentDepth + 1, maxDepth);
                }
            }
        }
        catch (error) {
            // Silently skip directories we can't read
            Logger_1.Logger.debug(`Skipping directory due to error: ${currentPath}`, error);
        }
    }
    /**
     * Check if directory should be skipped
     */
    static shouldSkipDirectory(dirName) {
        const skipDirs = [
            "node_modules",
            ".git",
            ".vscode",
            ".idea",
            "build",
            ".dart_tool",
            ".packages",
            "coverage",
        ];
        return skipDirs.includes(dirName) || dirName.startsWith(".");
    }
    /**
     * Get workspace relative path
     */
    static getWorkspaceRelativePath(absolutePath) {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders) {
            return undefined;
        }
        for (const folder of workspaceFolders) {
            const folderPath = folder.uri.fsPath;
            if (absolutePath.startsWith(folderPath)) {
                return path.relative(folderPath, absolutePath);
            }
        }
        return undefined;
    }
    /**
     * Resolve path relative to workspace
     */
    static resolveWorkspacePath(relativePath) {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return undefined;
        }
        return path.join(workspaceFolders[0].uri.fsPath, relativePath);
    }
    /**
     * Copy file
     */
    static async copyFile(sourcePath, destPath) {
        try {
            await fs.promises.copyFile(sourcePath, destPath);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to copy file from ${sourcePath} to ${destPath}`, error);
            throw error;
        }
    }
    /**
     * Move/rename file
     */
    static async moveFile(sourcePath, destPath) {
        try {
            await fs.promises.rename(sourcePath, destPath);
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to move file from ${sourcePath} to ${destPath}`, error);
            throw error;
        }
    }
    /**
     * Get file extension
     */
    static getFileExtension(filePath) {
        return path.extname(filePath).toLowerCase();
    }
    /**
     * Get file name without extension
     */
    static getFileNameWithoutExtension(filePath) {
        const fileName = path.basename(filePath);
        const extension = path.extname(fileName);
        return fileName.substring(0, fileName.length - extension.length);
    }
    /**
     * Normalize path separators
     */
    static normalizePath(filePath) {
        return path.normalize(filePath).replace(/\\/g, "/");
    }
    /**
     * Check if path is absolute
     */
    static isAbsolutePath(filePath) {
        return path.isAbsolute(filePath);
    }
    /**
     * Join paths safely
     */
    static joinPaths(...paths) {
        return path.join(...paths);
    }
    /**
     * Get directory name
     */
    static getDirectoryName(filePath) {
        return path.dirname(filePath);
    }
    /**
     * Get base name
     */
    static getBaseName(filePath) {
        return path.basename(filePath);
    }
    /**
     * Watch file for changes
     */
    static watchFile(filePath, callback) {
        return fs.watch(filePath, callback);
    }
    /**
     * Create a temporary file
     */
    static async createTempFile(content, extension = ".tmp") {
        const tempDir = require("os").tmpdir();
        const tempFileName = `syncview_${Date.now()}${extension}`;
        const tempFilePath = path.join(tempDir, tempFileName);
        await this.writeFile(tempFilePath, content);
        return tempFilePath;
    }
    /**
     * Clean up temporary files
     */
    static async cleanupTempFiles(_pattern = "syncview_*") {
        try {
            const tempDir = require("os").tmpdir();
            const files = await this.listDirectory(tempDir);
            for (const file of files) {
                if (file.includes("syncview_")) {
                    const filePath = path.join(tempDir, file);
                    try {
                        await this.deleteFile(filePath);
                        Logger_1.Logger.debug(`Cleaned up temp file: ${filePath}`);
                    }
                    catch (error) {
                        // Ignore errors when cleaning up temp files
                        Logger_1.Logger.debug(`Failed to clean up temp file: ${filePath}`, error);
                    }
                }
            }
        }
        catch (error) {
            Logger_1.Logger.error("Failed to cleanup temp files", error);
        }
    }
}
exports.FileUtils = FileUtils;
//# sourceMappingURL=FileUtils.js.map