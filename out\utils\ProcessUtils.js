"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessMonitor = exports.ProcessUtils = void 0;
const child_process_1 = require("child_process");
const Logger_1 = require("./Logger");
/**
 * Process utilities for spawning and managing child processes
 */
class ProcessUtils {
    /**
     * Execute a command and return the result
     */
    static async execute(command, args = [], options = {}) {
        const startTime = Date.now();
        return new Promise((resolve, reject) => {
            const child = (0, child_process_1.spawn)(command, args, {
                stdio: ["pipe", "pipe", "pipe"],
                ...options,
            });
            let stdout = "";
            let stderr = "";
            let timeoutId;
            // Setup timeout
            if (options.timeout) {
                timeoutId = setTimeout(() => {
                    child.kill("SIGKILL");
                    reject(new Error(`Process timeout after ${options.timeout}ms`));
                }, options.timeout);
            }
            // Collect stdout
            if (child.stdout) {
                child.stdout.on("data", (data) => {
                    stdout += data.toString(options.encoding || "utf8");
                });
            }
            // Collect stderr
            if (child.stderr) {
                child.stderr.on("data", (data) => {
                    stderr += data.toString(options.encoding || "utf8");
                });
            }
            // Handle process completion
            child.on("close", (exitCode) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                const duration = Date.now() - startTime;
                const result = {
                    exitCode: exitCode || 0,
                    stdout,
                    stderr,
                    duration,
                };
                Logger_1.Logger.debug(`Process completed: ${command} ${args.join(" ")} (${duration}ms, exit code: ${exitCode})`);
                resolve(result);
            });
            // Handle process errors
            child.on("error", (error) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                Logger_1.Logger.error(`Process error: ${command} ${args.join(" ")}`, error);
                reject(error);
            });
        });
    }
    /**
     * Spawn a long-running process
     */
    static spawn(command, args = [], options = {}) {
        Logger_1.Logger.debug(`Spawning process: ${command} ${args.join(" ")}`);
        const child = (0, child_process_1.spawn)(command, args, {
            stdio: ["pipe", "pipe", "pipe"],
            ...options,
        });
        // Log process events
        child.on("spawn", () => {
            Logger_1.Logger.debug(`Process spawned: ${command} (PID: ${child.pid})`);
        });
        child.on("exit", (code, signal) => {
            Logger_1.Logger.debug(`Process exited: ${command} (code: ${code}, signal: ${signal})`);
        });
        child.on("error", (error) => {
            Logger_1.Logger.error(`Process error: ${command}`, error);
        });
        return child;
    }
    /**
     * Check if a command is available in the system
     */
    static async isCommandAvailable(command) {
        try {
            const isWindows = process.platform === "win32";
            const checkCommand = isWindows ? "where" : "which";
            const result = await this.execute(checkCommand, [command], {
                timeout: 5000,
            });
            return result.exitCode === 0;
        }
        catch (error) {
            Logger_1.Logger.debug(`Command not available: ${command}`, error);
            return false;
        }
    }
    /**
     * Get command version
     */
    static async getCommandVersion(command, versionArg = "--version") {
        try {
            const result = await this.execute(command, [versionArg], {
                timeout: 10000,
            });
            if (result.exitCode === 0) {
                return result.stdout.trim();
            }
            return undefined;
        }
        catch (error) {
            Logger_1.Logger.debug(`Failed to get version for command: ${command}`, error);
            return undefined;
        }
    }
    /**
     * Kill a process gracefully
     */
    static async killProcess(process, timeoutMs = 5000) {
        if (!process || process.killed) {
            return;
        }
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                // Force kill if graceful shutdown failed
                if (!process.killed) {
                    Logger_1.Logger.warn(`Force killing process (PID: ${process.pid})`);
                    process.kill("SIGKILL");
                }
                resolve();
            }, timeoutMs);
            process.on("exit", () => {
                clearTimeout(timeout);
                resolve();
            });
            // Try graceful shutdown first
            Logger_1.Logger.debug(`Gracefully terminating process (PID: ${process.pid})`);
            process.kill("SIGTERM");
        });
    }
    /**
     * Send input to a process
     */
    static sendInput(process, input) {
        if (!process.stdin || process.stdin.destroyed) {
            Logger_1.Logger.warn("Cannot send input: process stdin not available");
            return false;
        }
        try {
            process.stdin.write(input);
            return true;
        }
        catch (error) {
            Logger_1.Logger.error("Failed to send input to process", error);
            return false;
        }
    }
    /**
     * Wait for process to exit
     */
    static async waitForExit(process, timeoutMs) {
        return new Promise((resolve, reject) => {
            let timeoutId;
            if (timeoutMs) {
                timeoutId = setTimeout(() => {
                    reject(new Error(`Process exit timeout after ${timeoutMs}ms`));
                }, timeoutMs);
            }
            process.on("exit", (code) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                resolve(code || 0);
            });
            process.on("error", (error) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                reject(error);
            });
        });
    }
    /**
     * Create a process monitor
     */
    static createProcessMonitor(process) {
        return new ProcessMonitor(process);
    }
    /**
     * Find processes by name (platform-specific)
     */
    static async findProcessesByName(name) {
        try {
            const isWindows = process.platform === "win32";
            let command;
            let args;
            if (isWindows) {
                command = "tasklist";
                args = ["/FI", `IMAGENAME eq ${name}*`, "/FO", "CSV", "/NH"];
            }
            else {
                command = "pgrep";
                args = ["-f", name];
            }
            const result = await this.execute(command, args, { timeout: 5000 });
            if (result.exitCode !== 0) {
                return [];
            }
            if (isWindows) {
                // Parse Windows tasklist output
                const lines = result.stdout.split("\n").filter((line) => line.trim());
                return lines
                    .map((line) => {
                    const parts = line.split(",");
                    return parseInt(parts[1]?.replace(/"/g, "") || "0", 10);
                })
                    .filter((pid) => pid > 0);
            }
            else {
                // Parse Unix pgrep output
                return result.stdout
                    .split("\n")
                    .filter((line) => line.trim())
                    .map((line) => parseInt(line.trim(), 10))
                    .filter((pid) => !isNaN(pid));
            }
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to find processes by name: ${name}`, error);
            return [];
        }
    }
    /**
     * Kill processes by name
     */
    static async killProcessesByName(name) {
        try {
            const pids = await this.findProcessesByName(name);
            let killedCount = 0;
            for (const pid of pids) {
                try {
                    process.kill(pid, "SIGTERM");
                    killedCount++;
                    Logger_1.Logger.debug(`Killed process: ${name} (PID: ${pid})`);
                }
                catch (error) {
                    Logger_1.Logger.debug(`Failed to kill process: ${name} (PID: ${pid})`, error);
                }
            }
            return killedCount;
        }
        catch (error) {
            Logger_1.Logger.error(`Failed to kill processes by name: ${name}`, error);
            return 0;
        }
    }
}
exports.ProcessUtils = ProcessUtils;
/**
 * Process monitor for tracking process health and statistics
 */
class ProcessMonitor {
    process;
    startTime;
    isMonitoring = false;
    stats = {
        restarts: 0,
        uptime: 0,
        lastRestart: 0,
    };
    constructor(process) {
        this.process = process;
        this.startTime = Date.now();
        this.startMonitoring();
    }
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }
        this.isMonitoring = true;
        this.process.on("exit", (code, signal) => {
            this.stats.uptime = Date.now() - this.startTime;
            Logger_1.Logger.debug(`Process monitor: process exited (code: ${code}, signal: ${signal}, uptime: ${this.stats.uptime}ms)`);
        });
        this.process.on("error", (error) => {
            Logger_1.Logger.error("Process monitor: process error", error);
        });
    }
    getStats() {
        return {
            ...this.stats,
            currentUptime: this.isRunning()
                ? Date.now() - this.startTime
                : this.stats.uptime,
            isRunning: this.isRunning(),
            pid: this.process.pid,
        };
    }
    isRunning() {
        return !this.process.killed && this.process.exitCode === null;
    }
    restart() {
        this.stats.restarts++;
        this.stats.lastRestart = Date.now();
        this.startTime = Date.now();
    }
    stop() {
        this.isMonitoring = false;
        this.stats.uptime = Date.now() - this.startTime;
    }
}
exports.ProcessMonitor = ProcessMonitor;
//# sourceMappingURL=ProcessUtils.js.map