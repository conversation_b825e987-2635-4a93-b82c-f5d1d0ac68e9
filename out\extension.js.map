{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,4BAiBC;AAGD,gCAOC;AAnCD,6DAA6D;AAC7D,8EAA8E;AAC9E,+CAAiC;AACjC,8DAA2D;AAC3D,2CAAwC;AAExC,yDAAyD;AACzD,0EAA0E;AAC1E,SAAgB,QAAQ,CAAC,OAAgC;IACvD,IAAI,CAAC;QACH,oBAAoB;QACpB,eAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC3B,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAEnD,wCAAwC;QACxC,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,OAAO,CAAC,CAAC;QACvD,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAE5B,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;IAC5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC7D,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC5B,4EAA4E,CAC7E,CAAC;IACJ,CAAC;AACH,CAAC;AAED,2DAA2D;AAC3D,SAAgB,UAAU;IACxB,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACrD,8DAA8D;IAChE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;AACH,CAAC"}