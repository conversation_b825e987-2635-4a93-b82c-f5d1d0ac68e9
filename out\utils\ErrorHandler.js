"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorHandler = void 0;
const vscode = __importStar(require("vscode"));
const Logger_1 = require("./Logger");
/**
 * Centralized error handling utility
 */
class ErrorHandler {
    static ERROR_CODES = {
        FLUTTER_NOT_FOUND: "FLUTTER_NOT_FOUND",
        PROJECT_NOT_FOUND: "PROJECT_NOT_FOUND",
        PROCESS_FAILED: "PROCESS_FAILED",
        PROXY_ERROR: "PROXY_ERROR",
        WEBVIEW_ERROR: "WEBVIEW_ERROR",
        CONFIGURATION_ERROR: "CONFIGURATION_ERROR",
    };
    /**
     * Handle and display an error with context
     */
    static async handleError(error, context, actions) {
        const errorMessage = typeof error === "string" ? error : error.message;
        const fullMessage = `${context.component} - ${context.operation}: ${errorMessage}`;
        // Log the error
        Logger_1.Logger.error(fullMessage, typeof error === "object" ? error : undefined);
        // Determine error type and create appropriate user message
        const userMessage = this.createUserFriendlyMessage(errorMessage, context);
        // Show error to user with actions
        if (actions && actions.length > 0) {
            const actionTitles = actions.map((action) => action.title);
            const selection = await vscode.window.showErrorMessage(userMessage, ...actionTitles);
            if (selection) {
                const selectedAction = actions.find((action) => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    }
                    catch (actionError) {
                        Logger_1.Logger.error("Error executing error action", actionError);
                    }
                }
            }
        }
        else {
            vscode.window.showErrorMessage(userMessage);
        }
    }
    /**
     * Handle Flutter-specific errors
     */
    static async handleFlutterError(error, operation) {
        const context = {
            component: "Flutter",
            operation,
            details: { error },
        };
        const actions = [];
        // Add specific actions based on error type
        const errorMessage = typeof error === "string" ? error : error.message;
        if (errorMessage.includes("Flutter SDK not found") ||
            errorMessage.includes("spawn EINVAL")) {
            actions.push({
                title: "Open Settings",
                action: async () => {
                    await vscode.commands.executeCommand("workbench.action.openSettings", "syncview.flutter.sdkPath");
                },
            });
            actions.push({
                title: "Flutter Doctor",
                action: async () => {
                    const terminal = vscode.window.createTerminal("Flutter Doctor");
                    terminal.sendText("flutter doctor");
                    terminal.show();
                },
            });
        }
        if (errorMessage.includes("No connected devices")) {
            actions.push({
                title: "Check Flutter Setup",
                action: async () => {
                    await vscode.env.openExternal(vscode.Uri.parse("https://flutter.dev/docs/get-started/install"));
                },
            });
        }
        if (errorMessage.includes("pubspec.yaml")) {
            actions.push({
                title: "Open pubspec.yaml",
                action: async () => {
                    const workspaceFolders = vscode.workspace.workspaceFolders;
                    if (workspaceFolders && workspaceFolders.length > 0) {
                        const pubspecUri = vscode.Uri.joinPath(workspaceFolders[0].uri, "pubspec.yaml");
                        await vscode.window.showTextDocument(pubspecUri);
                    }
                },
            });
        }
        actions.push({
            title: "Show Logs",
            action: async () => {
                Logger_1.Logger.show();
            },
        });
        await this.handleError(error, context, actions);
    }
    /**
     * Handle proxy server errors
     */
    static async handleProxyError(error, operation) {
        const context = {
            component: "Proxy Server",
            operation,
            details: { error },
        };
        const actions = [
            {
                title: "Restart Proxy",
                action: async () => {
                    await vscode.commands.executeCommand("syncview.restartPreview");
                },
            },
            {
                title: "Show Logs",
                action: async () => {
                    Logger_1.Logger.show();
                },
            },
        ];
        await this.handleError(error, context, actions);
    }
    /**
     * Handle WebView errors
     */
    static async handleWebViewError(error, operation) {
        const context = {
            component: "WebView",
            operation,
            details: { error },
        };
        const actions = [
            {
                title: "Refresh Preview",
                action: async () => {
                    await vscode.commands.executeCommand("syncview.refreshPreview");
                },
            },
            {
                title: "Restart Preview",
                action: async () => {
                    await vscode.commands.executeCommand("syncview.restartPreview");
                },
            },
            {
                title: "Show Logs",
                action: async () => {
                    Logger_1.Logger.show();
                },
            },
        ];
        await this.handleError(error, context, actions);
    }
    /**
     * Handle configuration errors
     */
    static async handleConfigurationError(error, setting) {
        const context = {
            component: "Configuration",
            operation: setting ? `Setting: ${setting}` : "General",
            details: { error, setting },
        };
        const actions = [
            {
                title: "Open Settings",
                action: async () => {
                    await vscode.commands.executeCommand("workbench.action.openSettings", "syncview");
                },
            },
            {
                title: "Reset to Defaults",
                action: async () => {
                    const confirm = await vscode.window.showWarningMessage("Reset all SyncView settings to defaults?", "Yes", "No");
                    if (confirm === "Yes") {
                        // Reset configuration logic would go here
                        vscode.window.showInformationMessage("Settings reset to defaults");
                    }
                },
            },
        ];
        await this.handleError(error, context, actions);
    }
    /**
     * Create user-friendly error messages
     */
    static createUserFriendlyMessage(errorMessage, context) {
        // Flutter-specific messages
        if (context.component === "Flutter") {
            if (errorMessage.includes("Flutter SDK not found")) {
                return "Flutter SDK not found. Please configure the Flutter SDK path in settings.";
            }
            if (errorMessage.includes("No connected devices")) {
                return "No Flutter devices available. Web preview requires Flutter web support.";
            }
            if (errorMessage.includes("pubspec.yaml")) {
                return "Flutter project configuration error. Please check your pubspec.yaml file.";
            }
            if (errorMessage.includes("No Flutter project found")) {
                return "No Flutter project found in the current workspace. Please open a Flutter project.";
            }
        }
        // Proxy-specific messages
        if (context.component === "Proxy Server") {
            if (errorMessage.includes("EADDRINUSE")) {
                return "Proxy server port is already in use. Try restarting the preview or changing the port in settings.";
            }
            if (errorMessage.includes("ECONNREFUSED")) {
                return "Cannot connect to Flutter development server. Make sure Flutter is running.";
            }
        }
        // WebView-specific messages
        if (context.component === "WebView") {
            if (errorMessage.includes("Failed to load")) {
                return "Failed to load Flutter preview. The development server may not be ready yet.";
            }
        }
        // Generic message
        return `${context.component} error during ${context.operation}: ${errorMessage}`;
    }
    /**
     * Show a warning message with optional actions
     */
    static async showWarning(message, actions) {
        Logger_1.Logger.warn(message);
        if (actions && actions.length > 0) {
            const actionTitles = actions.map((action) => action.title);
            const selection = await vscode.window.showWarningMessage(message, ...actionTitles);
            if (selection) {
                const selectedAction = actions.find((action) => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    }
                    catch (actionError) {
                        Logger_1.Logger.error("Error executing warning action", actionError);
                    }
                }
            }
        }
        else {
            vscode.window.showWarningMessage(message);
        }
    }
    /**
     * Show an information message with optional actions
     */
    static async showInfo(message, actions) {
        Logger_1.Logger.info(message);
        if (actions && actions.length > 0) {
            const actionTitles = actions.map((action) => action.title);
            const selection = await vscode.window.showInformationMessage(message, ...actionTitles);
            if (selection) {
                const selectedAction = actions.find((action) => action.title === selection);
                if (selectedAction) {
                    try {
                        await selectedAction.action();
                    }
                    catch (actionError) {
                        Logger_1.Logger.error("Error executing info action", actionError);
                    }
                }
            }
        }
        else {
            vscode.window.showInformationMessage(message);
        }
    }
    /**
     * Get error codes for programmatic error handling
     */
    static getErrorCodes() {
        return this.ERROR_CODES;
    }
}
exports.ErrorHandler = ErrorHandler;
//# sourceMappingURL=ErrorHandler.js.map