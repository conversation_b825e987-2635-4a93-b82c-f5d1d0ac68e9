import * as assert from 'assert';
import * as vscode from 'vscode';
import { FlutterManager } from '../../flutter/FlutterManager';
import { Logger } from '../../utils/Logger';

suite('FlutterManager Tests', () => {
    let flutterManager: FlutterManager;

    suiteSetup(async () => {
        // Initialize logger for tests
        const context = {
            subscriptions: []
        } as any;
        Logger.initialize(context);
    });

    setup(() => {
        flutterManager = new FlutterManager();
    });

    teardown(() => {
        if (flutterManager) {
            flutterManager.dispose();
        }
    });

    test('Should create FlutterManager instance', () => {
        assert.ok(flutterManager);
        assert.strictEqual(flutterManager.isRunning(), false);
    });

    test('Should handle process state correctly', () => {
        assert.strictEqual(flutterManager.isRunning(), false);
        assert.strictEqual(flutterManager.getServerUrl(), undefined);
    });

    test('Should emit events correctly', (done) => {
        let eventReceived = false;

        flutterManager.on('error', (error) => {
            eventReceived = true;
            assert.ok(error);
            done();
        });

        // Simulate an error event
        flutterManager.emit('error', new Error('Test error'));

        // Fallback timeout
        setTimeout(() => {
            if (!eventReceived) {
                done(new Error('Event not received'));
            }
        }, 1000);
    });

    test('Should handle hot reload when not running', async () => {
        try {
            await flutterManager.hotReload();
            assert.fail('Should have thrown an error');
        } catch (error) {
            assert.ok(error);
            assert.ok(error.message.includes('not running'));
        }
    });

    test('Should handle hot restart when not running', async () => {
        try {
            await flutterManager.hotRestart();
            assert.fail('Should have thrown an error');
        } catch (error) {
            assert.ok(error);
            assert.ok(error.message.includes('not running'));
        }
    });

    test('Should dispose cleanly', () => {
        assert.doesNotThrow(() => {
            flutterManager.dispose();
        });
    });
});
