import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import { ProcessUtils } from '../utils/ProcessUtils';
import { Logger } from '../utils/Logger';

export interface FlutterCliOptions {
    workingDirectory: string;
    flutterSdkPath?: string;
    additionalArgs?: string[];
    timeout?: number;
}

export interface FlutterDeviceInfo {
    id: string;
    name: string;
    platform: string;
    category: string;
    platformType: string;
}

/**
 * Wrapper for Flutter CLI commands with proper error handling and output parsing
 */
export class FlutterCliWrapper extends EventEmitter {
    private flutterCommand: string;

    constructor(private options: FlutterCliOptions) {
        super();
        this.flutterCommand = this.resolveFlutterCommand();
    }

    /**
     * Check if Flutter is available
     */
    public async isFlutterAvailable(): Promise<boolean> {
        try {
            const result = await ProcessUtils.execute(this.flutterCommand, ['--version'], {
                timeout: 10000,
                cwd: this.options.workingDirectory
            });
            return result.exitCode === 0;
        } catch (error) {
            Logger.error('Flutter availability check failed', error);
            return false;
        }
    }

    /**
     * Get Flutter version information
     */
    public async getFlutterVersion(): Promise<string | undefined> {
        try {
            const result = await ProcessUtils.execute(this.flutterCommand, ['--version'], {
                timeout: 10000,
                cwd: this.options.workingDirectory
            });
            
            if (result.exitCode === 0) {
                const versionLine = result.stdout.split('\n')[0];
                return versionLine.trim();
            }
            
            return undefined;
        } catch (error) {
            Logger.error('Failed to get Flutter version', error);
            return undefined;
        }
    }

    /**
     * Get available Flutter devices
     */
    public async getDevices(): Promise<FlutterDeviceInfo[]> {
        try {
            const result = await ProcessUtils.execute(this.flutterCommand, ['devices', '--machine'], {
                timeout: 15000,
                cwd: this.options.workingDirectory
            });
            
            if (result.exitCode === 0) {
                try {
                    const devices = JSON.parse(result.stdout);
                    return devices.map((device: any) => ({
                        id: device.id,
                        name: device.name,
                        platform: device.platform,
                        category: device.category,
                        platformType: device.platformType
                    }));
                } catch (parseError) {
                    Logger.error('Failed to parse devices JSON', parseError);
                    return [];
                }
            }
            
            return [];
        } catch (error) {
            Logger.error('Failed to get Flutter devices', error);
            return [];
        }
    }

    /**
     * Check if web support is available
     */
    public async isWebSupported(): Promise<boolean> {
        const devices = await this.getDevices();
        return devices.some(device => device.platform === 'web');
    }

    /**
     * Run flutter pub get
     */
    public async pubGet(): Promise<boolean> {
        try {
            Logger.info('Running flutter pub get...');
            const result = await ProcessUtils.execute(this.flutterCommand, ['pub', 'get'], {
                timeout: 60000,
                cwd: this.options.workingDirectory
            });
            
            if (result.exitCode === 0) {
                Logger.info('flutter pub get completed successfully');
                return true;
            } else {
                Logger.error('flutter pub get failed', result.stderr);
                return false;
            }
        } catch (error) {
            Logger.error('Failed to run flutter pub get', error);
            return false;
        }
    }

    /**
     * Clean Flutter project
     */
    public async clean(): Promise<boolean> {
        try {
            Logger.info('Running flutter clean...');
            const result = await ProcessUtils.execute(this.flutterCommand, ['clean'], {
                timeout: 30000,
                cwd: this.options.workingDirectory
            });
            
            if (result.exitCode === 0) {
                Logger.info('flutter clean completed successfully');
                return true;
            } else {
                Logger.error('flutter clean failed', result.stderr);
                return false;
            }
        } catch (error) {
            Logger.error('Failed to run flutter clean', error);
            return false;
        }
    }

    /**
     * Build Flutter web
     */
    public async buildWeb(): Promise<boolean> {
        try {
            Logger.info('Building Flutter web...');
            const args = ['build', 'web'];
            
            if (this.options.additionalArgs) {
                args.push(...this.options.additionalArgs);
            }
            
            const result = await ProcessUtils.execute(this.flutterCommand, args, {
                timeout: 300000, // 5 minutes
                cwd: this.options.workingDirectory
            });
            
            if (result.exitCode === 0) {
                Logger.info('Flutter web build completed successfully');
                return true;
            } else {
                Logger.error('Flutter web build failed', result.stderr);
                return false;
            }
        } catch (error) {
            Logger.error('Failed to build Flutter web', error);
            return false;
        }
    }

    /**
     * Start Flutter web server
     */
    public startWebServer(port?: number): ChildProcess {
        const args = ['run', '-d', 'web-server'];
        
        if (port) {
            args.push('--web-port', port.toString());
        } else {
            args.push('--web-port', '0'); // Random port
        }
        
        args.push('--web-hostname', 'localhost');
        
        // Add additional args
        if (this.options.additionalArgs) {
            args.push(...this.options.additionalArgs);
        }
        
        Logger.info(`Starting Flutter web server: ${this.flutterCommand} ${args.join(' ')}`);
        
        const process = ProcessUtils.spawn(this.flutterCommand, args, {
            cwd: this.options.workingDirectory
        });
        
        return process;
    }

    /**
     * Create a new Flutter project
     */
    public async createProject(projectName: string, projectPath: string): Promise<boolean> {
        try {
            Logger.info(`Creating Flutter project: ${projectName}`);
            const result = await ProcessUtils.execute(this.flutterCommand, [
                'create',
                '--platforms', 'web',
                projectName
            ], {
                timeout: 120000, // 2 minutes
                cwd: projectPath
            });
            
            if (result.exitCode === 0) {
                Logger.info('Flutter project created successfully');
                return true;
            } else {
                Logger.error('Flutter project creation failed', result.stderr);
                return false;
            }
        } catch (error) {
            Logger.error('Failed to create Flutter project', error);
            return false;
        }
    }

    /**
     * Enable web support for existing project
     */
    public async enableWeb(): Promise<boolean> {
        try {
            Logger.info('Enabling Flutter web support...');
            const result = await ProcessUtils.execute(this.flutterCommand, [
                'create', '.', '--platforms', 'web'
            ], {
                timeout: 60000,
                cwd: this.options.workingDirectory
            });
            
            if (result.exitCode === 0) {
                Logger.info('Flutter web support enabled successfully');
                return true;
            } else {
                Logger.error('Failed to enable Flutter web support', result.stderr);
                return false;
            }
        } catch (error) {
            Logger.error('Failed to enable Flutter web support', error);
            return false;
        }
    }

    /**
     * Run Flutter doctor
     */
    public async doctor(): Promise<{ success: boolean; output: string }> {
        try {
            Logger.info('Running flutter doctor...');
            const result = await ProcessUtils.execute(this.flutterCommand, ['doctor', '-v'], {
                timeout: 30000,
                cwd: this.options.workingDirectory
            });
            
            return {
                success: result.exitCode === 0,
                output: result.stdout + result.stderr
            };
        } catch (error) {
            Logger.error('Failed to run flutter doctor', error);
            return {
                success: false,
                output: `Error running flutter doctor: ${error}`
            };
        }
    }

    /**
     * Analyze Flutter project
     */
    public async analyze(): Promise<{ success: boolean; issues: string[] }> {
        try {
            Logger.info('Analyzing Flutter project...');
            const result = await ProcessUtils.execute(this.flutterCommand, ['analyze'], {
                timeout: 60000,
                cwd: this.options.workingDirectory
            });
            
            const issues = result.stdout
                .split('\n')
                .filter(line => line.includes('•') || line.includes('error') || line.includes('warning'))
                .map(line => line.trim())
                .filter(line => line.length > 0);
            
            return {
                success: result.exitCode === 0,
                issues
            };
        } catch (error) {
            Logger.error('Failed to analyze Flutter project', error);
            return {
                success: false,
                issues: [`Analysis failed: ${error}`]
            };
        }
    }

    /**
     * Resolve Flutter command path
     */
    private resolveFlutterCommand(): string {
        if (this.options.flutterSdkPath) {
            const isWindows = process.platform === 'win32';
            const flutterBin = isWindows ? 'flutter.bat' : 'flutter';
            return `${this.options.flutterSdkPath}/bin/${flutterBin}`;
        }
        
        // Use flutter from PATH
        return process.platform === 'win32' ? 'flutter.bat' : 'flutter';
    }

    /**
     * Validate Flutter installation
     */
    public async validateInstallation(): Promise<{ valid: boolean; issues: string[] }> {
        const issues: string[] = [];
        
        try {
            // Check if Flutter command is available
            const isAvailable = await this.isFlutterAvailable();
            if (!isAvailable) {
                issues.push('Flutter command not found. Please install Flutter SDK.');
                return { valid: false, issues };
            }
            
            // Check Flutter doctor
            const doctorResult = await this.doctor();
            if (!doctorResult.success) {
                issues.push('Flutter doctor reported issues. Run "flutter doctor" for details.');
            }
            
            // Check web support
            const webSupported = await this.isWebSupported();
            if (!webSupported) {
                issues.push('Flutter web support not available. Run "flutter config --enable-web".');
            }
            
            return {
                valid: issues.length === 0,
                issues
            };
            
        } catch (error) {
            issues.push(`Flutter validation failed: ${error}`);
            return { valid: false, issues };
        }
    }
}
