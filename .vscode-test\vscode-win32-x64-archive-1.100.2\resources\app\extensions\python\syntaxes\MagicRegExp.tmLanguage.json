{"information_for_contributors": ["This file has been converted from https://github.com/MagicStack/MagicPython/blob/master/grammars/MagicRegExp.tmLanguage", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/MagicStack/MagicPython/commit/c9b3409deb69acec31bbf7913830e93a046b30cc", "name": "MagicRegExp", "scopeName": "source.regexp.python", "patterns": [{"include": "#regexp-expression"}], "repository": {"regexp-base-expression": {"patterns": [{"include": "#regexp-quantifier"}, {"include": "#regexp-base-common"}]}, "fregexp-base-expression": {"patterns": [{"include": "#fregexp-quantifier"}, {"include": "#fstring-formatting-braces"}, {"match": "\\{.*?\\}"}, {"include": "#regexp-base-common"}]}, "fstring-formatting-braces": {"patterns": [{"comment": "empty braces are illegal", "match": "({)(\\s*?)(})", "captures": {"1": {"name": "constant.character.format.placeholder.other.python"}, "2": {"name": "invalid.illegal.brace.python"}, "3": {"name": "constant.character.format.placeholder.other.python"}}}, {"name": "constant.character.escape.python", "match": "({{|}})"}]}, "regexp-base-common": {"patterns": [{"name": "support.other.match.any.regexp", "match": "\\."}, {"name": "support.other.match.begin.regexp", "match": "\\^"}, {"name": "support.other.match.end.regexp", "match": "\\$"}, {"name": "keyword.operator.quantifier.regexp", "match": "[+*?]\\??"}, {"name": "keyword.operator.disjunction.regexp", "match": "\\|"}, {"include": "#regexp-escape-sequence"}]}, "regexp-quantifier": {"name": "keyword.operator.quantifier.regexp", "match": "(?x)\n  \\{(\n    \\d+ | \\d+,(\\d+)? | ,\\d+\n  )\\}\n"}, "fregexp-quantifier": {"name": "keyword.operator.quantifier.regexp", "match": "(?x)\n  \\{\\{(\n    \\d+ | \\d+,(\\d+)? | ,\\d+\n  )\\}\\}\n"}, "regexp-backreference-number": {"name": "meta.backreference.regexp", "match": "(\\\\[1-9]\\d?)", "captures": {"1": {"name": "entity.name.tag.backreference.regexp"}}}, "regexp-backreference": {"name": "meta.backreference.named.regexp", "match": "(?x)\n  (\\()  (\\?P= \\w+(?:\\s+[[:alnum:]]+)?)  (\\))\n", "captures": {"1": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.begin.regexp"}, "2": {"name": "entity.name.tag.named.backreference.regexp"}, "3": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.end.regexp"}}}, "regexp-flags": {"name": "storage.modifier.flag.regexp", "match": "\\(\\?[aiLmsux]+\\)"}, "regexp-escape-special": {"name": "support.other.escape.special.regexp", "match": "\\\\([AbBdDsSwWZ])"}, "regexp-escape-character": {"name": "constant.character.escape.regexp", "match": "(?x)\n  \\\\ (\n        x[0-9A-Fa-f]{2}\n        | 0[0-7]{1,2}\n        | [0-7]{3}\n     )\n"}, "regexp-escape-unicode": {"name": "constant.character.unicode.regexp", "match": "(?x)\n  \\\\ (\n        u[0-9A-Fa-f]{4}\n        | U[0-9A-Fa-f]{8}\n     )\n"}, "regexp-escape-catchall": {"name": "constant.character.escape.regexp", "match": "\\\\(.|\\n)"}, "regexp-escape-sequence": {"patterns": [{"include": "#regexp-escape-special"}, {"include": "#regexp-escape-character"}, {"include": "#regexp-escape-unicode"}, {"include": "#regexp-backreference-number"}, {"include": "#regexp-escape-catchall"}]}, "regexp-charecter-set-escapes": {"patterns": [{"name": "constant.character.escape.regexp", "match": "\\\\[abfnrtv\\\\]"}, {"include": "#regexp-escape-special"}, {"name": "constant.character.escape.regexp", "match": "\\\\([0-7]{1,3})"}, {"include": "#regexp-escape-character"}, {"include": "#regexp-escape-unicode"}, {"include": "#regexp-escape-catchall"}]}, "codetags": {"match": "(?:\\b(NOTE|XXX|HACK|FIXME|BUG|TODO)\\b)", "captures": {"1": {"name": "keyword.codetag.notation.python"}}}, "regexp-expression": {"patterns": [{"include": "#regexp-base-expression"}, {"include": "#regexp-character-set"}, {"include": "#regexp-comments"}, {"include": "#regexp-flags"}, {"include": "#regexp-named-group"}, {"include": "#regexp-backreference"}, {"include": "#regexp-lookahead"}, {"include": "#regexp-lookahead-negative"}, {"include": "#regexp-lookbehind"}, {"include": "#regexp-lookbehind-negative"}, {"include": "#regexp-conditional"}, {"include": "#regexp-parentheses-non-capturing"}, {"include": "#regexp-parentheses"}]}, "regexp-character-set": {"patterns": [{"match": "(?x)\n  \\[ \\^? \\] (?! .*?\\])\n"}, {"name": "meta.character.set.regexp", "begin": "(\\[)(\\^)?(\\])?", "end": "(\\])", "beginCaptures": {"1": {"name": "punctuation.character.set.begin.regexp constant.other.set.regexp"}, "2": {"name": "keyword.operator.negation.regexp"}, "3": {"name": "constant.character.set.regexp"}}, "endCaptures": {"1": {"name": "punctuation.character.set.end.regexp constant.other.set.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-charecter-set-escapes"}, {"name": "constant.character.set.regexp", "match": "[^\\n]"}]}]}, "regexp-named-group": {"name": "meta.named.regexp", "begin": "(?x)\n  (\\()  (\\?P <\\w+(?:\\s+[[:alnum:]]+)?>)\n", "end": "(\\))", "beginCaptures": {"1": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp"}, "2": {"name": "entity.name.tag.named.group.regexp"}}, "endCaptures": {"1": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-comments": {"name": "comment.regexp", "begin": "\\(\\?#", "end": "(\\))", "beginCaptures": {"0": {"name": "punctuation.comment.begin.regexp"}}, "endCaptures": {"1": {"name": "punctuation.comment.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#codetags"}]}, "regexp-lookahead": {"begin": "(\\()\\?=", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookahead.regexp"}, "1": {"name": "punctuation.parenthesis.lookahead.begin.regexp"}}, "endCaptures": {"1": {"name": "keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-lookahead-negative": {"begin": "(\\()\\?!", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookahead.negative.regexp"}, "1": {"name": "punctuation.parenthesis.lookahead.begin.regexp"}}, "endCaptures": {"1": {"name": "keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-lookbehind": {"begin": "(\\()\\?<=", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookbehind.regexp"}, "1": {"name": "punctuation.parenthesis.lookbehind.begin.regexp"}}, "endCaptures": {"1": {"name": "keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-lookbehind-negative": {"begin": "(\\()\\?<!", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.lookbehind.negative.regexp"}, "1": {"name": "punctuation.parenthesis.lookbehind.begin.regexp"}}, "endCaptures": {"1": {"name": "keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-conditional": {"begin": "(\\()\\?\\((\\w+(?:\\s+[[:alnum:]]+)?|\\d+)\\)", "end": "(\\))", "beginCaptures": {"0": {"name": "keyword.operator.conditional.regexp"}, "1": {"name": "punctuation.parenthesis.conditional.begin.regexp"}}, "endCaptures": {"1": {"name": "keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-parentheses-non-capturing": {"begin": "\\(\\?:", "end": "(\\))", "beginCaptures": {"0": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp"}}, "endCaptures": {"1": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}, "regexp-parentheses": {"begin": "\\(", "end": "(\\))", "beginCaptures": {"0": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp"}}, "endCaptures": {"1": {"name": "support.other.parenthesis.regexp punctuation.parenthesis.end.regexp"}, "2": {"name": "invalid.illegal.newline.python"}}, "patterns": [{"include": "#regexp-expression"}]}}}