"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Centralized logging utility for the SyncView extension
 */
class Logger {
    static outputChannel;
    static isInitialized = false;
    /**
     * Initialize the logger with VS Code output channel
     */
    static initialize(context) {
        if (this.isInitialized) {
            return;
        }
        this.outputChannel = vscode.window.createOutputChannel('SyncView');
        context.subscriptions.push(this.outputChannel);
        this.isInitialized = true;
    }
    /**
     * Log an info message
     */
    static info(message, ...args) {
        this.log('INFO', message, ...args);
    }
    /**
     * Log a warning message
     */
    static warn(message, ...args) {
        this.log('WARN', message, ...args);
    }
    /**
     * Log an error message
     */
    static error(message, error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const stack = error instanceof Error ? error.stack : undefined;
        this.log('ERROR', message, errorMessage);
        if (stack) {
            this.outputChannel.appendLine(`Stack trace: ${stack}`);
        }
    }
    /**
     * Log a debug message (only in development)
     */
    static debug(message, ...args) {
        if (process.env.NODE_ENV === 'development') {
            this.log('DEBUG', message, ...args);
        }
    }
    /**
     * Show the output channel
     */
    static show() {
        if (this.outputChannel) {
            this.outputChannel.show();
        }
    }
    /**
     * Clear the output channel
     */
    static clear() {
        if (this.outputChannel) {
            this.outputChannel.clear();
        }
    }
    /**
     * Internal logging method
     */
    static log(level, message, ...args) {
        if (!this.isInitialized || !this.outputChannel) {
            console.log(`[${level}] ${message}`, ...args);
            return;
        }
        const timestamp = new Date().toISOString();
        const formattedArgs = args.length > 0 ? ` ${args.join(' ')}` : '';
        const logMessage = `[${timestamp}] [${level}] ${message}${formattedArgs}`;
        this.outputChannel.appendLine(logMessage);
        // Also log to console for development
        if (process.env.NODE_ENV === 'development') {
            console.log(logMessage);
        }
    }
}
exports.Logger = Logger;
//# sourceMappingURL=Logger.js.map