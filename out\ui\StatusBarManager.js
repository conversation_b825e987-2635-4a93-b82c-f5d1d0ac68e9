"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusBarManager = void 0;
const vscode = __importStar(require("vscode"));
const Logger_1 = require("../utils/Logger");
/**
 * Manages the status bar items for SyncView
 */
class StatusBarManager {
    statusBarItem;
    currentStatus = 'stopped';
    constructor() {
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    }
    /**
     * Initialize the status bar
     */
    initialize() {
        this.updateStatusBar();
        this.statusBarItem.show();
        Logger_1.Logger.info('Status bar initialized');
    }
    /**
     * Set the current status
     */
    setStatus(status) {
        this.currentStatus = status;
        this.updateStatusBar();
        Logger_1.Logger.debug(`Status bar updated: ${status}`);
    }
    /**
     * Get the current status
     */
    getStatus() {
        return this.currentStatus;
    }
    /**
     * Update the status bar display
     */
    updateStatusBar() {
        switch (this.currentStatus) {
            case 'stopped':
                this.statusBarItem.text = '$(play) SyncView';
                this.statusBarItem.tooltip = 'Start Flutter Preview';
                this.statusBarItem.command = 'syncview.startPreview';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'starting':
                this.statusBarItem.text = '$(loading~spin) SyncView Starting...';
                this.statusBarItem.tooltip = 'Flutter Preview is starting';
                this.statusBarItem.command = undefined;
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'running':
                this.statusBarItem.text = '$(stop) SyncView Running';
                this.statusBarItem.tooltip = 'Stop Flutter Preview';
                this.statusBarItem.command = 'syncview.stopPreview';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'stopping':
                this.statusBarItem.text = '$(loading~spin) SyncView Stopping...';
                this.statusBarItem.tooltip = 'Flutter Preview is stopping';
                this.statusBarItem.command = undefined;
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'error':
                this.statusBarItem.text = '$(error) SyncView Error';
                this.statusBarItem.tooltip = 'Flutter Preview encountered an error. Click to restart.';
                this.statusBarItem.command = 'syncview.restartPreview';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                break;
            default:
                this.statusBarItem.text = '$(question) SyncView';
                this.statusBarItem.tooltip = 'Unknown status';
                this.statusBarItem.command = undefined;
                this.statusBarItem.backgroundColor = undefined;
        }
    }
    /**
     * Show a temporary message in the status bar
     */
    showTemporaryMessage(message, durationMs = 3000) {
        const originalText = this.statusBarItem.text;
        const originalTooltip = this.statusBarItem.tooltip;
        const originalCommand = this.statusBarItem.command;
        this.statusBarItem.text = message;
        this.statusBarItem.tooltip = message;
        this.statusBarItem.command = undefined;
        setTimeout(() => {
            this.statusBarItem.text = originalText;
            this.statusBarItem.tooltip = originalTooltip;
            this.statusBarItem.command = originalCommand;
        }, durationMs);
    }
    /**
     * Hide the status bar item
     */
    hide() {
        this.statusBarItem.hide();
    }
    /**
     * Show the status bar item
     */
    show() {
        this.statusBarItem.show();
    }
    /**
     * Dispose the status bar item
     */
    dispose() {
        this.statusBarItem.dispose();
    }
}
exports.StatusBarManager = StatusBarManager;
//# sourceMappingURL=StatusBarManager.js.map