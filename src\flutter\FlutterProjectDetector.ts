import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { Logger } from '../utils/Logger';

/**
 * Detects and validates Flutter projects in the workspace
 */
export class FlutterProjectDetector {
    
    /**
     * Find the Flutter project in the current workspace
     */
    public async findFlutterProject(): Promise<string | undefined> {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        
        if (!workspaceFolders || workspaceFolders.length === 0) {
            Logger.warn('No workspace folders found');
            return undefined;
        }

        // Check each workspace folder for Flutter project
        for (const folder of workspaceFolders) {
            const projectPath = await this.detectFlutterProjectInPath(folder.uri.fsPath);
            if (projectPath) {
                return projectPath;
            }
        }

        // If no direct Flutter project found, check subdirectories
        for (const folder of workspaceFolders) {
            const projectPath = await this.searchFlutterProjectInSubdirectories(folder.uri.fsPath);
            if (projectPath) {
                return projectPath;
            }
        }

        return undefined;
    }

    /**
     * Check if a specific path contains a Flutter project
     */
    public async detectFlutterProjectInPath(projectPath: string): Promise<string | undefined> {
        try {
            const pubspecPath = path.join(projectPath, 'pubspec.yaml');
            
            if (!fs.existsSync(pubspecPath)) {
                return undefined;
            }

            const pubspecContent = fs.readFileSync(pubspecPath, 'utf8');
            
            // Check if it's a Flutter project
            if (this.isFlutterProject(pubspecContent)) {
                Logger.info(`Flutter project detected at: ${projectPath}`);
                return projectPath;
            }

            return undefined;
        } catch (error) {
            Logger.error(`Error detecting Flutter project in ${projectPath}`, error);
            return undefined;
        }
    }

    /**
     * Search for Flutter projects in subdirectories
     */
    private async searchFlutterProjectInSubdirectories(rootPath: string, maxDepth: number = 2): Promise<string | undefined> {
        try {
            const entries = fs.readdirSync(rootPath, { withFileTypes: true });
            
            for (const entry of entries) {
                if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    const subPath = path.join(rootPath, entry.name);
                    const projectPath = await this.detectFlutterProjectInPath(subPath);
                    
                    if (projectPath) {
                        return projectPath;
                    }
                    
                    // Recursively search subdirectories
                    if (maxDepth > 1) {
                        const deepProjectPath = await this.searchFlutterProjectInSubdirectories(subPath, maxDepth - 1);
                        if (deepProjectPath) {
                            return deepProjectPath;
                        }
                    }
                }
            }
        } catch (error) {
            Logger.error(`Error searching subdirectories in ${rootPath}`, error);
        }

        return undefined;
    }

    /**
     * Check if pubspec.yaml content indicates a Flutter project
     */
    private isFlutterProject(pubspecContent: string): boolean {
        // Check for Flutter dependency
        const hasFlutterDependency = /^\s*flutter:\s*$/m.test(pubspecContent);
        
        // Check for Flutter SDK dependency
        const hasFlutterSdk = /^\s*sdk:\s*flutter\s*$/m.test(pubspecContent);
        
        return hasFlutterDependency || hasFlutterSdk;
    }

    /**
     * Check if directory should be skipped during search
     */
    private shouldSkipDirectory(dirName: string): boolean {
        const skipDirs = [
            'node_modules',
            '.git',
            '.vscode',
            '.idea',
            'build',
            '.dart_tool',
            'ios',
            'android',
            'web',
            'windows',
            'macos',
            'linux'
        ];

        return skipDirs.includes(dirName) || dirName.startsWith('.');
    }

    /**
     * Validate Flutter project structure
     */
    public async validateFlutterProject(projectPath: string): Promise<boolean> {
        try {
            const requiredFiles = [
                'pubspec.yaml',
                'lib/main.dart'
            ];

            for (const file of requiredFiles) {
                const filePath = path.join(projectPath, file);
                if (!fs.existsSync(filePath)) {
                    Logger.warn(`Required Flutter file missing: ${file}`);
                    return false;
                }
            }

            // Check if web support is enabled
            const webDir = path.join(projectPath, 'web');
            if (!fs.existsSync(webDir)) {
                Logger.warn('Web support not found. Run "flutter create . --platforms web" to enable web support.');
                return false;
            }

            return true;
        } catch (error) {
            Logger.error(`Error validating Flutter project at ${projectPath}`, error);
            return false;
        }
    }

    /**
     * Get Flutter project information
     */
    public async getProjectInfo(projectPath: string): Promise<any> {
        try {
            const pubspecPath = path.join(projectPath, 'pubspec.yaml');
            const pubspecContent = fs.readFileSync(pubspecPath, 'utf8');
            
            // Parse basic project info (simplified YAML parsing)
            const nameMatch = pubspecContent.match(/^name:\s*(.+)$/m);
            const versionMatch = pubspecContent.match(/^version:\s*(.+)$/m);
            const descriptionMatch = pubspecContent.match(/^description:\s*(.+)$/m);

            return {
                name: nameMatch ? nameMatch[1].trim() : 'Unknown',
                version: versionMatch ? versionMatch[1].trim() : '1.0.0',
                description: descriptionMatch ? descriptionMatch[1].trim() : '',
                path: projectPath
            };
        } catch (error) {
            Logger.error(`Error getting project info for ${projectPath}`, error);
            return null;
        }
    }

    /**
     * Check if Flutter web is supported
     */
    public async isWebSupported(projectPath: string): Promise<boolean> {
        try {
            const webDir = path.join(projectPath, 'web');
            const indexHtml = path.join(webDir, 'index.html');
            
            return fs.existsSync(webDir) && fs.existsSync(indexHtml);
        } catch (error) {
            Logger.error(`Error checking web support for ${projectPath}`, error);
            return false;
        }
    }

    /**
     * Get all Flutter projects in workspace
     */
    public async getAllFlutterProjects(): Promise<string[]> {
        const projects: string[] = [];
        const workspaceFolders = vscode.workspace.workspaceFolders;
        
        if (!workspaceFolders) {
            return projects;
        }

        for (const folder of workspaceFolders) {
            await this.collectFlutterProjects(folder.uri.fsPath, projects);
        }

        return projects;
    }

    /**
     * Recursively collect Flutter projects
     */
    private async collectFlutterProjects(rootPath: string, projects: string[], maxDepth: number = 3): Promise<void> {
        try {
            const projectPath = await this.detectFlutterProjectInPath(rootPath);
            if (projectPath) {
                projects.push(projectPath);
                return; // Don't search subdirectories if current directory is a Flutter project
            }

            if (maxDepth <= 0) {
                return;
            }

            const entries = fs.readdirSync(rootPath, { withFileTypes: true });
            
            for (const entry of entries) {
                if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    const subPath = path.join(rootPath, entry.name);
                    await this.collectFlutterProjects(subPath, projects, maxDepth - 1);
                }
            }
        } catch (error) {
            Logger.error(`Error collecting Flutter projects in ${rootPath}`, error);
        }
    }
}
