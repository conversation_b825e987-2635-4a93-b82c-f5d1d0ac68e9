"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebViewProvider = void 0;
const vscode = __importStar(require("vscode"));
const Logger_1 = require("../utils/Logger");
/**
 * Custom WebView provider for Flutter preview with enhanced features
 */
class WebViewProvider {
    _extensionUri;
    _context;
    static viewType = "syncview.flutterPreview";
    _view;
    _currentUrl;
    _isLoading = false;
    constructor(_extensionUri, _context) {
        this._extensionUri = _extensionUri;
        this._context = _context;
    }
    resolveWebviewView(webviewView, _context, _token) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri],
        };
        webviewView.webview.html = this._getLoadingHtml();
        // Handle messages from webview
        webviewView.webview.onDidReceiveMessage((message) => this._handleMessage(message), undefined, this._context.subscriptions);
        Logger_1.Logger.info("WebView provider resolved");
    }
    /**
     * Update the preview URL
     */
    updatePreview(url) {
        this._currentUrl = url;
        if (this._view) {
            this._view.webview.html = this._getPreviewHtml(url);
            Logger_1.Logger.info(`WebView updated with URL: ${url}`);
        }
    }
    /**
     * Show loading state
     */
    showLoading() {
        this._isLoading = true;
        if (this._view) {
            this._view.webview.html = this._getLoadingHtml();
        }
    }
    /**
     * Show error state
     */
    showError(error) {
        this._isLoading = false;
        if (this._view) {
            this._view.webview.html = this._getErrorHtml(error);
        }
    }
    /**
     * Refresh the current preview
     */
    refresh() {
        if (this._currentUrl && this._view) {
            this._view.webview.html = this._getPreviewHtml(this._currentUrl);
        }
    }
    /**
     * Handle messages from webview
     */
    _handleMessage(message) {
        switch (message.type) {
            case "ready":
                Logger_1.Logger.info("WebView ready");
                break;
            case "error":
                Logger_1.Logger.error("WebView error", message.error);
                vscode.window.showErrorMessage(`Preview error: ${message.error}`);
                break;
            case "reload":
                this.refresh();
                break;
            case "openDevTools":
                vscode.commands.executeCommand("workbench.action.webview.openDeveloperTools");
                break;
            default:
                Logger_1.Logger.debug("Unknown webview message", message);
        }
    }
    /**
     * Get loading HTML content
     */
    _getLoadingHtml() {
        const nonce = this._getNonce();
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <title>Flutter Preview</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-editor-foreground);
                    font-family: var(--vscode-font-family);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    box-sizing: border-box;
                }
                .loading-container {
                    text-align: center;
                    max-width: 300px;
                }
                .spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid var(--vscode-progressBar-background);
                    border-top: 4px solid var(--vscode-progressBar-foreground);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 20px;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                h2 {
                    margin: 0 0 10px 0;
                    color: var(--vscode-foreground);
                }
                p {
                    margin: 5px 0;
                    color: var(--vscode-descriptionForeground);
                    font-size: 14px;
                }
                .status {
                    margin-top: 20px;
                    padding: 10px;
                    background: var(--vscode-textBlockQuote-background);
                    border-left: 4px solid var(--vscode-textBlockQuote-border);
                    border-radius: 4px;
                }
            </style>
        </head>
        <body>
            <div class="loading-container">
                <div class="spinner"></div>
                <h2>Starting Flutter Preview</h2>
                <p>Initializing Flutter development server...</p>
                <div class="status">
                    <p>This may take a moment on first run</p>
                </div>
            </div>
            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();
                vscode.postMessage({ type: 'ready' });
            </script>
        </body>
        </html>`;
    }
    /**
     * Get preview HTML content
     */
    _getPreviewHtml(url) {
        const nonce = this._getNonce();
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; frame-src ${url} http: https:; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <title>Flutter Preview</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                    background: var(--vscode-editor-background);
                }
                iframe {
                    width: 100%;
                    height: 100vh;
                    border: none;
                    background: white;
                }
                .error-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-errorForeground);
                    display: none;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                    padding: 20px;
                    text-align: center;
                }
                .toolbar {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    z-index: 1000;
                    display: flex;
                    gap: 5px;
                }
                .toolbar button {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    cursor: pointer;
                    font-size: 12px;
                }
                .toolbar button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
            </style>
        </head>
        <body>
            <div class="toolbar">
                <button onclick="refreshPreview()">↻ Refresh</button>
                <button onclick="openDevTools()">🔧 DevTools</button>
            </div>
            <iframe
                id="preview-frame"
                src="${url}"
                onload="handleLoad()"
                onerror="handleError()">
            </iframe>
            <div class="error-overlay" id="error-overlay">
                <h3>Failed to load Flutter preview</h3>
                <p>The Flutter development server may not be ready yet.</p>
                <button onclick="refreshPreview()">Try Again</button>
            </div>

            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();

                function handleLoad() {
                    document.getElementById('error-overlay').style.display = 'none';
                    vscode.postMessage({ type: 'loaded' });
                }

                function handleError() {
                    document.getElementById('error-overlay').style.display = 'flex';
                    vscode.postMessage({
                        type: 'error',
                        error: 'Failed to load Flutter preview'
                    });
                }

                function refreshPreview() {
                    vscode.postMessage({ type: 'reload' });
                }

                function openDevTools() {
                    vscode.postMessage({ type: 'openDevTools' });
                }

                // Handle iframe errors
                window.addEventListener('message', function(event) {
                    if (event.data.type === 'flutter-error') {
                        handleError();
                    }
                });

                // Auto-refresh on connection errors
                let retryCount = 0;
                const maxRetries = 3;

                function autoRetry() {
                    if (retryCount < maxRetries) {
                        retryCount++;
                        setTimeout(() => {
                            document.getElementById('preview-frame').src = "${url}";
                        }, 2000 * retryCount);
                    }
                }

                document.getElementById('preview-frame').addEventListener('error', autoRetry);
            </script>
        </body>
        </html>`;
    }
    /**
     * Get error HTML content
     */
    _getErrorHtml(error) {
        const nonce = this._getNonce();
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <title>Flutter Preview Error</title>
            <style>
                body {
                    margin: 0;
                    padding: 20px;
                    background: var(--vscode-editor-background);
                    color: var(--vscode-errorForeground);
                    font-family: var(--vscode-font-family);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    text-align: center;
                }
                .error-icon {
                    font-size: 48px;
                    margin-bottom: 20px;
                }
                h2 {
                    margin: 0 0 10px 0;
                }
                .error-message {
                    background: var(--vscode-inputValidation-errorBackground);
                    border: 1px solid var(--vscode-inputValidation-errorBorder);
                    padding: 15px;
                    border-radius: 4px;
                    margin: 20px 0;
                    max-width: 400px;
                }
                button {
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    margin: 5px;
                }
                button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
            </style>
        </head>
        <body>
            <div class="error-icon">⚠️</div>
            <h2>Flutter Preview Error</h2>
            <div class="error-message">
                <p>${error}</p>
            </div>
            <button onclick="retry()">Retry</button>
            <button onclick="showLogs()">Show Logs</button>

            <script nonce="${nonce}">
                const vscode = acquireVsCodeApi();

                function retry() {
                    vscode.postMessage({ type: 'reload' });
                }

                function showLogs() {
                    vscode.postMessage({ type: 'showLogs' });
                }
            </script>
        </body>
        </html>`;
    }
    /**
     * Generate a nonce for CSP
     */
    _getNonce() {
        let text = "";
        const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
exports.WebViewProvider = WebViewProvider;
//# sourceMappingURL=WebViewProvider.js.map