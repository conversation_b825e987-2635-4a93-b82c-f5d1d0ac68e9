import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { Logger } from './Logger';

/**
 * File system utilities for the SyncView extension
 */
export class FileUtils {
    
    /**
     * Check if a file exists
     */
    public static async fileExists(filePath: string): Promise<boolean> {
        try {
            await fs.promises.access(filePath, fs.constants.F_OK);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Check if a directory exists
     */
    public static async directoryExists(dirPath: string): Promise<boolean> {
        try {
            const stats = await fs.promises.stat(dirPath);
            return stats.isDirectory();
        } catch {
            return false;
        }
    }

    /**
     * Read file content as string
     */
    public static async readFile(filePath: string): Promise<string> {
        try {
            return await fs.promises.readFile(filePath, 'utf8');
        } catch (error) {
            Logger.error(`Failed to read file: ${filePath}`, error);
            throw error;
        }
    }

    /**
     * Write content to file
     */
    public static async writeFile(filePath: string, content: string): Promise<void> {
        try {
            await fs.promises.writeFile(filePath, content, 'utf8');
        } catch (error) {
            Logger.error(`Failed to write file: ${filePath}`, error);
            throw error;
        }
    }

    /**
     * Create directory recursively
     */
    public static async createDirectory(dirPath: string): Promise<void> {
        try {
            await fs.promises.mkdir(dirPath, { recursive: true });
        } catch (error) {
            Logger.error(`Failed to create directory: ${dirPath}`, error);
            throw error;
        }
    }

    /**
     * Delete file
     */
    public static async deleteFile(filePath: string): Promise<void> {
        try {
            await fs.promises.unlink(filePath);
        } catch (error) {
            Logger.error(`Failed to delete file: ${filePath}`, error);
            throw error;
        }
    }

    /**
     * Delete directory recursively
     */
    public static async deleteDirectory(dirPath: string): Promise<void> {
        try {
            await fs.promises.rmdir(dirPath, { recursive: true });
        } catch (error) {
            Logger.error(`Failed to delete directory: ${dirPath}`, error);
            throw error;
        }
    }

    /**
     * Get file stats
     */
    public static async getFileStats(filePath: string): Promise<fs.Stats> {
        try {
            return await fs.promises.stat(filePath);
        } catch (error) {
            Logger.error(`Failed to get file stats: ${filePath}`, error);
            throw error;
        }
    }

    /**
     * List directory contents
     */
    public static async listDirectory(dirPath: string): Promise<string[]> {
        try {
            return await fs.promises.readdir(dirPath);
        } catch (error) {
            Logger.error(`Failed to list directory: ${dirPath}`, error);
            throw error;
        }
    }

    /**
     * Find files matching a pattern
     */
    public static async findFiles(
        rootPath: string,
        pattern: RegExp,
        maxDepth: number = 5
    ): Promise<string[]> {
        const results: string[] = [];
        
        try {
            await this.findFilesRecursive(rootPath, pattern, results, 0, maxDepth);
        } catch (error) {
            Logger.error(`Failed to find files in: ${rootPath}`, error);
        }
        
        return results;
    }

    /**
     * Recursive helper for finding files
     */
    private static async findFilesRecursive(
        currentPath: string,
        pattern: RegExp,
        results: string[],
        currentDepth: number,
        maxDepth: number
    ): Promise<void> {
        if (currentDepth >= maxDepth) {
            return;
        }

        try {
            const entries = await fs.promises.readdir(currentPath, { withFileTypes: true });
            
            for (const entry of entries) {
                const fullPath = path.join(currentPath, entry.name);
                
                if (entry.isFile() && pattern.test(entry.name)) {
                    results.push(fullPath);
                } else if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
                    await this.findFilesRecursive(fullPath, pattern, results, currentDepth + 1, maxDepth);
                }
            }
        } catch (error) {
            // Silently skip directories we can't read
            Logger.debug(`Skipping directory due to error: ${currentPath}`, error);
        }
    }

    /**
     * Check if directory should be skipped
     */
    private static shouldSkipDirectory(dirName: string): boolean {
        const skipDirs = [
            'node_modules',
            '.git',
            '.vscode',
            '.idea',
            'build',
            '.dart_tool',
            '.packages',
            'coverage'
        ];

        return skipDirs.includes(dirName) || dirName.startsWith('.');
    }

    /**
     * Get workspace relative path
     */
    public static getWorkspaceRelativePath(absolutePath: string): string | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        
        if (!workspaceFolders) {
            return undefined;
        }

        for (const folder of workspaceFolders) {
            const folderPath = folder.uri.fsPath;
            if (absolutePath.startsWith(folderPath)) {
                return path.relative(folderPath, absolutePath);
            }
        }

        return undefined;
    }

    /**
     * Resolve path relative to workspace
     */
    public static resolveWorkspacePath(relativePath: string): string | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return undefined;
        }

        return path.join(workspaceFolders[0].uri.fsPath, relativePath);
    }

    /**
     * Copy file
     */
    public static async copyFile(sourcePath: string, destPath: string): Promise<void> {
        try {
            await fs.promises.copyFile(sourcePath, destPath);
        } catch (error) {
            Logger.error(`Failed to copy file from ${sourcePath} to ${destPath}`, error);
            throw error;
        }
    }

    /**
     * Move/rename file
     */
    public static async moveFile(sourcePath: string, destPath: string): Promise<void> {
        try {
            await fs.promises.rename(sourcePath, destPath);
        } catch (error) {
            Logger.error(`Failed to move file from ${sourcePath} to ${destPath}`, error);
            throw error;
        }
    }

    /**
     * Get file extension
     */
    public static getFileExtension(filePath: string): string {
        return path.extname(filePath).toLowerCase();
    }

    /**
     * Get file name without extension
     */
    public static getFileNameWithoutExtension(filePath: string): string {
        const fileName = path.basename(filePath);
        const extension = path.extname(fileName);
        return fileName.substring(0, fileName.length - extension.length);
    }

    /**
     * Normalize path separators
     */
    public static normalizePath(filePath: string): string {
        return path.normalize(filePath).replace(/\\/g, '/');
    }

    /**
     * Check if path is absolute
     */
    public static isAbsolutePath(filePath: string): boolean {
        return path.isAbsolute(filePath);
    }

    /**
     * Join paths safely
     */
    public static joinPaths(...paths: string[]): string {
        return path.join(...paths);
    }

    /**
     * Get directory name
     */
    public static getDirectoryName(filePath: string): string {
        return path.dirname(filePath);
    }

    /**
     * Get base name
     */
    public static getBaseName(filePath: string): string {
        return path.basename(filePath);
    }

    /**
     * Watch file for changes
     */
    public static watchFile(
        filePath: string,
        callback: (eventType: string, filename: string | null) => void
    ): fs.FSWatcher {
        return fs.watch(filePath, callback);
    }

    /**
     * Create a temporary file
     */
    public static async createTempFile(content: string, extension: string = '.tmp'): Promise<string> {
        const tempDir = require('os').tmpdir();
        const tempFileName = `syncview_${Date.now()}${extension}`;
        const tempFilePath = path.join(tempDir, tempFileName);
        
        await this.writeFile(tempFilePath, content);
        return tempFilePath;
    }

    /**
     * Clean up temporary files
     */
    public static async cleanupTempFiles(pattern: string = 'syncview_*'): Promise<void> {
        try {
            const tempDir = require('os').tmpdir();
            const files = await this.listDirectory(tempDir);
            
            for (const file of files) {
                if (file.includes('syncview_')) {
                    const filePath = path.join(tempDir, file);
                    try {
                        await this.deleteFile(filePath);
                        Logger.debug(`Cleaned up temp file: ${filePath}`);
                    } catch (error) {
                        // Ignore errors when cleaning up temp files
                        Logger.debug(`Failed to clean up temp file: ${filePath}`, error);
                    }
                }
            }
        } catch (error) {
            Logger.error('Failed to cleanup temp files', error);
        }
    }
}
