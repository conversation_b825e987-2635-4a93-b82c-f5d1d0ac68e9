import * as vscode from 'vscode';
import { EventEmitter } from 'events';
import { ProxyServer } from './ProxyServer';
import { <PERSON>rs<PERSON>and<PERSON> } from './CorsHandler';
import { Logger } from '../utils/Logger';

/**
 * Manages the HTTP proxy server for Flutter web preview
 */
export class ProxyServerManager extends EventEmitter implements vscode.Disposable {
    private proxyServer: ProxyServer | undefined;
    private corsHandler: CorsHandler;
    private isServerRunning = false;
    private currentProxyUrl: string | undefined;
    private targetUrl: string | undefined;

    constructor() {
        super();
        this.corsHandler = new CorsHandler();
    }

    /**
     * Start the proxy server
     */
    public async start(): Promise<string> {
        if (this.isServerRunning) {
            Logger.warn('Proxy server is already running');
            return this.currentProxyUrl!;
        }

        try {
            Logger.info('Starting proxy server...');

            this.proxyServer = new ProxyServer(this.corsHandler);
            const proxyUrl = await this.proxyServer.start();

            this.isServerRunning = true;
            this.currentProxyUrl = proxyUrl;

            this.setupServerEventListeners();
            this.emit('serverStarted', proxyUrl);

            Logger.info(`Proxy server started at: ${proxyUrl}`);
            return proxyUrl;

        } catch (error) {
            this.isServerRunning = false;
            Logger.error('Failed to start proxy server', error);
            throw error;
        }
    }

    /**
     * Stop the proxy server
     */
    public async stop(): Promise<void> {
        if (!this.isServerRunning || !this.proxyServer) {
            Logger.warn('Proxy server is not running');
            return;
        }

        try {
            Logger.info('Stopping proxy server...');

            await this.proxyServer.stop();
            
            this.isServerRunning = false;
            this.currentProxyUrl = undefined;
            this.proxyServer = undefined;

            this.emit('serverStopped');
            Logger.info('Proxy server stopped successfully');

        } catch (error) {
            Logger.error('Failed to stop proxy server', error);
            throw error;
        }
    }

    /**
     * Set the target URL for proxying
     */
    public setTargetUrl(url: string): void {
        this.targetUrl = url;
        
        if (this.proxyServer) {
            this.proxyServer.setTargetUrl(url);
            Logger.info(`Proxy target URL updated: ${url}`);
        }

        // Start proxy server if not already running
        if (!this.isServerRunning) {
            this.start().catch(error => {
                Logger.error('Failed to start proxy server after setting target URL', error);
            });
        }
    }

    /**
     * Get the current proxy URL
     */
    public getProxyUrl(): string | undefined {
        return this.currentProxyUrl;
    }

    /**
     * Get the current target URL
     */
    public getTargetUrl(): string | undefined {
        return this.targetUrl;
    }

    /**
     * Check if proxy server is running
     */
    public isRunning(): boolean {
        return this.isServerRunning;
    }

    /**
     * Restart the proxy server
     */
    public async restart(): Promise<string> {
        Logger.info('Restarting proxy server...');
        
        if (this.isServerRunning) {
            await this.stop();
        }
        
        return this.start();
    }

    /**
     * Setup event listeners for the proxy server
     */
    private setupServerEventListeners(): void {
        if (!this.proxyServer) {
            return;
        }

        this.proxyServer.on('request', (req: any) => {
            Logger.debug(`Proxy request: ${req.method} ${req.url}`);
        });

        this.proxyServer.on('error', (error: Error) => {
            Logger.error('Proxy server error', error);
            this.emit('error', error);
        });

        this.proxyServer.on('targetError', (error: Error) => {
            Logger.error('Proxy target error', error);
            this.emit('targetError', error);
        });

        this.proxyServer.on('proxyResponse', (proxyRes: any) => {
            Logger.debug(`Proxy response: ${proxyRes.statusCode}`);
        });
    }

    /**
     * Get proxy server statistics
     */
    public getStats(): any {
        if (!this.proxyServer) {
            return null;
        }

        return this.proxyServer.getStats();
    }

    /**
     * Configure CORS settings
     */
    public configureCors(options: any): void {
        this.corsHandler.configure(options);
        Logger.info('CORS configuration updated');
    }

    /**
     * Dispose all resources
     */
    public dispose(): void {
        this.stop().catch(error => {
            Logger.error('Error during proxy server disposal', error);
        });
        
        this.removeAllListeners();
    }
}
