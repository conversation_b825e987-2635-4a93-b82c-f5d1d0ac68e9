{"name": "debug-server-ready", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.32.0"}, "icon": "media/icon.png", "activationEvents": ["onDebugResolve"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": true}}, "enabledApiProposals": ["terminalDataWriteEvent"], "main": "./dist/extension", "contributes": {"debuggers": [{"type": "*", "configurationAttributes": {"launch": {"properties": {"serverReadyAction": {"oneOf": [{"type": "object", "additionalProperties": false, "markdownDescription": "%debug.server.ready.serverReadyAction.description%", "default": {"action": "openExternally", "killOnServerStop": false}, "properties": {"action": {"type": "string", "enum": ["openExternally"], "enumDescriptions": ["%debug.server.ready.action.openExternally.description%"], "markdownDescription": "%debug.server.ready.action.description%", "default": "openExternally"}, "pattern": {"type": "string", "markdownDescription": "%debug.server.ready.pattern.description%", "default": "listening on port ([0-9]+)"}, "uriFormat": {"type": "string", "markdownDescription": "%debug.server.ready.uriFormat.description%", "default": "http://localhost:%s"}, "killOnServerStop": {"type": "boolean", "markdownDescription": "%debug.server.ready.killOnServerStop.description%", "default": false}}}, {"type": "object", "additionalProperties": false, "markdownDescription": "%debug.server.ready.serverReadyAction.description%", "default": {"action": "debugWithEdge", "pattern": "listening on port ([0-9]+)", "uriFormat": "http://localhost:%s", "webRoot": "${workspaceFolder}", "killOnServerStop": false}, "properties": {"action": {"type": "string", "enum": ["debugWithChrome", "debugWithEdge"], "enumDescriptions": ["%debug.server.ready.action.debugWithChrome.description%"], "markdownDescription": "%debug.server.ready.action.description%", "default": "debugWithEdge"}, "pattern": {"type": "string", "markdownDescription": "%debug.server.ready.pattern.description%", "default": "listening on port ([0-9]+)"}, "uriFormat": {"type": "string", "markdownDescription": "%debug.server.ready.uriFormat.description%", "default": "http://localhost:%s"}, "webRoot": {"type": "string", "markdownDescription": "%debug.server.ready.webRoot.description%", "default": "${workspaceFolder}"}, "killOnServerStop": {"type": "boolean", "markdownDescription": "%debug.server.ready.killOnServerStop.description%", "default": false}}}, {"type": "object", "additionalProperties": false, "markdownDescription": "%debug.server.ready.serverReadyAction.description%", "default": {"action": "startDebugging", "name": "<launch browser config name>", "killOnServerStop": false}, "required": ["name"], "properties": {"action": {"type": "string", "enum": ["startDebugging"], "enumDescriptions": ["%debug.server.ready.action.startDebugging.description%"], "markdownDescription": "%debug.server.ready.action.description%", "default": "startDebugging"}, "pattern": {"type": "string", "markdownDescription": "%debug.server.ready.pattern.description%", "default": "listening on port ([0-9]+)"}, "name": {"type": "string", "markdownDescription": "%debug.server.ready.debugConfigName.description%", "default": "Launch Browser"}, "killOnServerStop": {"type": "boolean", "markdownDescription": "%debug.server.ready.killOnServerStop.description%", "default": false}}}, {"type": "object", "additionalProperties": false, "markdownDescription": "%debug.server.ready.serverReadyAction.description%", "default": {"action": "startDebugging", "config": {"type": "node", "request": "launch"}, "killOnServerStop": false}, "required": ["config"], "properties": {"action": {"type": "string", "enum": ["startDebugging"], "enumDescriptions": ["%debug.server.ready.action.startDebugging.description%"], "markdownDescription": "%debug.server.ready.action.description%", "default": "startDebugging"}, "pattern": {"type": "string", "markdownDescription": "%debug.server.ready.pattern.description%", "default": "listening on port ([0-9]+)"}, "config": {"type": "object", "markdownDescription": "%debug.server.ready.debugConfig.description%", "default": {}}, "killOnServerStop": {"type": "boolean", "markdownDescription": "%debug.server.ready.killOnServerStop.description%", "default": false}}}]}}}}}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}