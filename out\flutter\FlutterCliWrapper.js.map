{"version": 3, "file": "FlutterCliWrapper.js", "sourceRoot": "", "sources": ["../../src/flutter/FlutterCliWrapper.ts"], "names": [], "mappings": ";;;AACA,mCAAsC;AACtC,wDAAqD;AACrD,4CAAyC;AAiBzC;;GAEG;AACH,MAAa,iBAAkB,SAAQ,qBAAY;IAG7B;IAFZ,cAAc,CAAS;IAE/B,YAAoB,OAA0B;QAC5C,KAAK,EAAE,CAAC;QADU,YAAO,GAAP,OAAO,CAAmB;QAE5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,WAAW,CAAC,EACb;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YACF,OAAO,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB;QAC5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,WAAW,CAAC,EACb;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjD,OAAO,WAAW,CAAC,IAAI,EAAE,CAAC;YAC5B,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,SAAS,EAAE,WAAW,CAAC,EACxB;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC1C,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;wBACnC,EAAE,EAAE,MAAM,CAAC,EAAE;wBACb,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ;wBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;wBACzB,YAAY,EAAE,MAAM,CAAC,YAAY;qBAClC,CAAC,CAAC,CAAC;gBACN,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,UAAU,CAAC,CAAC;oBACzD,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACzB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QACxC,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC;IAC7D,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM;QACjB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,KAAK,EAAE,KAAK,CAAC,EACd;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACtD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACtD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,KAAK;QAChB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;YACxC,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,OAAO,CAAC,EACT;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;gBACpD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ;QACnB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;YACvC,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAE9B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAChC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC5C,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE;gBACnE,OAAO,EAAE,MAAM,EAAE,YAAY;gBAC7B,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;gBACxD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACxD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,IAAa;QACjC,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;QAEzC,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,cAAc;QAC9C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;QAEzC,sBAAsB;QACtB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAC5C,CAAC;QAED,eAAM,CAAC,IAAI,CACT,gCAAgC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACxE,CAAC;QAEF,MAAM,OAAO,GAAG,2BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE;YAC5D,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;SACnC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACxB,WAAmB,EACnB,WAAmB;QAEnB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,WAAW,EAAE,CAAC,CAAC;YACxD,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,QAAQ,EAAE,aAAa,EAAE,KAAK,EAAE,WAAW,CAAC,EAC7C;gBACE,OAAO,EAAE,MAAM,EAAE,YAAY;gBAC7B,GAAG,EAAE,WAAW;aACjB,CACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;gBACpD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC/C,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,QAAQ,EAAE,GAAG,EAAE,aAAa,EAAE,KAAK,CAAC,EACrC;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;gBAC1B,eAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;gBACxD,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpE,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM;QACjB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,QAAQ,EAAE,IAAI,CAAC,EAChB;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,CAAC;gBAC9B,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;aACtC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACpD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,iCAAiC,KAAK,EAAE;aACjD,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO;QAClB,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC5C,MAAM,MAAM,GAAG,MAAM,2BAAY,CAAC,OAAO,CACvC,IAAI,CAAC,cAAc,EACnB,CAAC,SAAS,CAAC,EACX;gBACE,OAAO,EAAE,KAAK;gBACd,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB;aACnC,CACF,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM;iBACzB,KAAK,CAAC,IAAI,CAAC;iBACX,MAAM,CACL,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACtB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAC3B;iBACA,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;iBAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAErC,OAAO;gBACL,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,CAAC;gBAC9B,MAAM;aACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,CAAC,oBAAoB,KAAK,EAAE,CAAC;aACtC,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;YAC/C,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;YACzD,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,QAAQ,UAAU,EAAE,CAAC;QAC5D,CAAC;QAED,wBAAwB;QACxB,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,oBAAoB;QAI/B,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACpD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;gBACtE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;YAClC,CAAC;YAED,uBAAuB;YACvB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CACT,mEAAmE,CACpE,CAAC;YACJ,CAAC;YAED,oBAAoB;YACpB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YACjD,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CACT,uEAAuE,CACxE,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;gBAC1B,MAAM;aACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;YACnD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;CACF;AA7YD,8CA6YC"}