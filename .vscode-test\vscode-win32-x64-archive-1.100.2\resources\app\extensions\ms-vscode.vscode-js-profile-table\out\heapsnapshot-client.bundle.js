(()=>{var e={273:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".pORror3dhFo_jlOpYDCf{align-items:center;background:var(--vscode-input-background);border:1px solid transparent;border-radius:2px;display:flex;flex-grow:1}.pORror3dhFo_jlOpYDCf:focus-within{border-color:var(--vscode-focusBorder)}.pORror3dhFo_jlOpYDCf>input{background:none;border:0;color:var(--vscode-input-foreground);flex-grow:1;font-family:var(--vscode-font-family);font-size:var(--vscode-editor-font-size);font-weight:var(--vscode-editor-font-weight);padding:3px 4px}.pORror3dhFo_jlOpYDCf>input::-moz-placeholder{color:var(--vscode-input-placeholderForeground)}.pORror3dhFo_jlOpYDCf>input::placeholder{color:var(--vscode-input-placeholderForeground)}.pORror3dhFo_jlOpYDCf>input:focus{outline:0}.pORror3dhFo_jlOpYDCf>button{align-self:stretch;flex-shrink:0;margin-bottom:2px;margin-top:2px;padding-bottom:1px;padding-top:1px}.TxnreUUAKeaIj08v1D74{outline:1px solid var(--vscode-inputValidation-errorBorder)!important}",""]),a.locals={wrapper:"pORror3dhFo_jlOpYDCf",error:"TxnreUUAKeaIj08v1D74"};const s=a},838:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,"._10qE_dwQvcTIkxXSevC{align-items:center;background:var(--vscode-editorWidget-background);box-shadow:0 0 8px 2px var(--vscode-widget-shadow);box-sizing:border-box;display:flex;height:33px;padding:4px;position:relative}",""]),a.locals={f:"_10qE_dwQvcTIkxXSevC"};const s=a},27:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,'.R2IAIfDFX3iMHGg7IZmw{height:2px;left:0;overflow:hidden;pointer-events:none;position:absolute;right:0;top:0;z-index:1}.R2IAIfDFX3iMHGg7IZmw:before{animation-duration:4s;animation-iteration-count:infinite;animation-name:R2IAIfDFX3iMHGg7IZmw;animation-timing-function:linear;background:var(--vscode-progressBar-background);content:"";inset:0;position:absolute;transform:translateZ(0);width:2%}@keyframes R2IAIfDFX3iMHGg7IZmw{0%{transform:translateX(0) scaleX(1)}50%{transform:translateX(2500%) scaleX(3)}to{transform:translateX(4900%) scaleX(1)}}',""]),a.locals={progress:"R2IAIfDFX3iMHGg7IZmw"};const s=a},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".xmE80NYmNXD_PVlCFkpw{background:var(--vscode-inputValidation-errorBackground);border:1px solid var(--vscode-inputValidation-errorBorder);padding:4px;position:absolute;top:calc(100% - 4px);z-index:1}",""]),a.locals={error:"xmE80NYmNXD_PVlCFkpw"};const s=a},94:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".kMe1rh3sGlRge1xNo2FI{align-items:center;background:none;border:1px solid transparent;border-radius:3px;color:var(--vscode-editorWidget-foreground);cursor:pointer;display:flex;margin-left:2px;margin-right:2px;outline:0!important;padding:1px}.kMe1rh3sGlRge1xNo2FI+.kMe1rh3sGlRge1xNo2FI{margin-left:0}.kMe1rh3sGlRge1xNo2FI:hover{background-color:var(--vscode-inputOption-hoverBackground)}.kMe1rh3sGlRge1xNo2FI:focus{border-color:var(--vscode-focusBorder)}.kMe1rh3sGlRge1xNo2FI[aria-checked=true]{background:var(--vscode-inputOption-activeBackground)!important;border:1px solid var(--vscode-inputOption-activeBorder);color:var(--vscode-inputOption-activeForeground)}.kMe1rh3sGlRge1xNo2FI>svg{height:16px;width:16px}",""]),a.locals={button:"kMe1rh3sGlRge1xNo2FI"};const s=a},71:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,"*{margin:0;padding:0}.f0Jk8dUahhi2qYS9k4oW{display:flex;flex-direction:column;height:100vh}.zHL1SBvvBysxPwQUMdES{flex-shrink:0;padding-bottom:10px}.Cekf7yByuJxmXr1F30ko{display:flex;flex-basis:0px;flex-direction:column;flex-grow:1;overflow:hidden;position:relative}",""]),a.locals={wrapper:"f0Jk8dUahhi2qYS9k4oW",filter:"zHL1SBvvBysxPwQUMdES",rows:"Cekf7yByuJxmXr1F30ko"};const s=a},13:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,"*{margin:0;padding:0}.s60FX4Gsndji9tYAIKUs{display:flex;flex-direction:column;height:100vh}.b9nxfCTXGDNxo2IiwNIH{flex-shrink:0;padding-bottom:10px}.cBcdOYmT9_99aVdm9PZF{display:flex;flex-basis:0px;flex-direction:column;flex-grow:1;overflow:hidden;position:relative}",""]),a.locals={wrapper:"s60FX4Gsndji9tYAIKUs",filter:"b9nxfCTXGDNxo2IiwNIH",rows:"cBcdOYmT9_99aVdm9PZF"};const s=a},925:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(173),o=n.n(r),i=n(806),a=n.n(i)()(o());a.push([e.id,".oKKFacfbvSqqq_aC8AE3{flex-grow:1;font-family:var(--vscode-editor-font-family);overflow:auto}.SmGXcbNWaLnOSaUk6_wl{cursor:default;display:flex;height:23px;-webkit-user-select:none;-moz-user-select:none;user-select:none}.SmGXcbNWaLnOSaUk6_wl:focus{background:var(--vscode-list-focusBackground);color:var(--vscode-list-focusForeground);outline:0}.SmGXcbNWaLnOSaUk6_wl>div{margin:2px 4px}.MujOaFcerHdWdwd6a6Up{cursor:pointer;margin-left:244px!important}.QgpWiq7tKfenPd7TZWkz,.enzbXVI4PCTd9u4rZnht.XyAbSpz9jylz8ZS39BwX{text-align:right;width:110px}.enzbXVI4PCTd9u4rZnht.XyAbSpz9jylz8ZS39BwX{cursor:pointer}.enzbXVI4PCTd9u4rZnht svg{display:inline-block;height:1em;margin-right:.25em}.QgpWiq7tKfenPd7TZWkz{color:var(--vscode-terminal-ansiYellow);flex-shrink:0;z-index:0}.QgpWiq7tKfenPd7TZWkz,.QgpWiq7tKfenPd7TZWkz>span{position:relative}.ly1Zy3MRQIzbgri0u5Nx{align-items:center;color:var(--vscode-terminal-foreground);display:flex;flex-grow:1;overflow:hidden;padding-left:10px}.ly1Zy3MRQIzbgri0u5Nx.eyDJMWoZxGo2OH8Utijk{opacity:.5}.ly1Zy3MRQIzbgri0u5Nx a{color:var(--vscode-terminal-foreground);cursor:pointer;text-decoration:none}.ly1Zy3MRQIzbgri0u5Nx a:focus,.ly1Zy3MRQIzbgri0u5Nx a:hover{text-decoration:underline}.ly1Zy3MRQIzbgri0u5Nx a:focus{outline:1px solid var(--vscode-focusBorder)}.e_Y8B9frar9b2mXzvehf,.NFJunN7lzsRC155ophiH{overflow:hidden;white-space:nowrap}.e_Y8B9frar9b2mXzvehf,.NFJunN7lzsRC155ophiH{text-overflow:ellipsis}.NFJunN7lzsRC155ophiH{flex-shrink:0;max-width:calc(100% - 20px)}.e_Y8B9frar9b2mXzvehf{direction:rtl;flex-grow:1;flex-shrink:1;font-family:var(--vscode-font-family);font-size:.8em;margin-left:2em;opacity:.8}.fvv0GAqFPWwrnW2jN8rt{background:none;border:0;flex-shrink:0;opacity:.7;outline:0}.fvv0GAqFPWwrnW2jN8rt,.fvv0GAqFPWwrnW2jN8rt svg{cursor:pointer;width:1em}.SmGXcbNWaLnOSaUk6_wl:hover .fvv0GAqFPWwrnW2jN8rt{opacity:1}.IWaoWposwXCYN2K4Z1vv{background:hsla(0,0%,100%,.1);border-bottom:2px solid hsla(0,0%,100%,.2);bottom:0;left:0;position:absolute;right:0;top:0;transform-origin:100%;z-index:-1}.vscode-light .IWaoWposwXCYN2K4Z1vv{background:rgba(0,0,0,.2)}.vscode-high-contrast .IWaoWposwXCYN2K4Z1vv{background:#fff}",""]),a.locals={rows:"oKKFacfbvSqqq_aC8AE3",row:"SmGXcbNWaLnOSaUk6_wl",footer:"MujOaFcerHdWdwd6a6Up",duration:"QgpWiq7tKfenPd7TZWkz",heading:"enzbXVI4PCTd9u4rZnht",timing:"XyAbSpz9jylz8ZS39BwX",location:"ly1Zy3MRQIzbgri0u5Nx",virtual:"eyDJMWoZxGo2OH8Utijk",file:"e_Y8B9frar9b2mXzvehf",fn:"NFJunN7lzsRC155ophiH",expander:"fvv0GAqFPWwrnW2jN8rt",impactBar:"IWaoWposwXCYN2K4Z1vv"};const s=a},806:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(r)for(var s=0;s<this.length;s++){var l=this[s][0];null!=l&&(a[l]=!0)}for(var c=0;c<e.length;c++){var d=[].concat(e[c]);r&&a[d[0]]||(void 0!==i&&(void 0===d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=i),n&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=n):d[2]=n),o&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=o):d[4]="".concat(o)),t.push(d))}},t}},173:e=>{"use strict";e.exports=function(e){return e[1]}},604:e=>{"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},a=[],s=0;s<e.length;s++){var l=e[s],c=r.base?l[0]+r.base:l[0],d=i[c]||0,u="".concat(c," ").concat(d);i[c]=d+1;var _=n(u),p={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==_)t[_].references++,t[_].updater(p);else{var f=o(p,r);r.byIndex=s,t.splice(s,0,{identifier:u,updater:f,references:1})}a.push(u)}return a}function o(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var i=r(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var s=n(i[a]);t[s].references--}for(var l=r(e,o),c=0;c<i.length;c++){var d=n(i[c]);0===t[d].references&&(t[d].updater(),t.splice(d,1))}i=l}}},863:e=>{"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},896:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},124:(e,t,n)=>{"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},101:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},917:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},113:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M8.85352 11.7021H7.85449L7.03809 9.54297H3.77246L3.00439 11.7021H2L4.9541 4H5.88867L8.85352 11.7021ZM6.74268 8.73193L5.53418 5.4502C5.49479 5.34277 5.4554 5.1709 5.41602 4.93457H5.39453C5.35872 5.15299 5.31755 5.32487 5.271 5.4502L4.07324 8.73193H6.74268Z"></path><path d="M13.756 11.7021H12.8752V10.8428H12.8537C12.4706 11.5016 11.9066 11.8311 11.1618 11.8311C10.6139 11.8311 10.1843 11.686 9.87273 11.396C9.56479 11.106 9.41082 10.721 9.41082 10.2412C9.41082 9.21354 10.016 8.61556 11.2262 8.44727L12.8752 8.21631C12.8752 7.28174 12.4974 6.81445 11.7419 6.81445C11.0794 6.81445 10.4815 7.04004 9.94793 7.49121V6.58887C10.4886 6.24512 11.1117 6.07324 11.8171 6.07324C13.1097 6.07324 13.756 6.75716 13.756 8.125V11.7021ZM12.8752 8.91992L11.5485 9.10254C11.1403 9.15983 10.8324 9.26188 10.6247 9.40869C10.417 9.55192 10.3132 9.80794 10.3132 10.1768C10.3132 10.4453 10.4081 10.6655 10.5978 10.8374C10.7912 11.0057 11.0472 11.0898 11.3659 11.0898C11.8027 11.0898 12.1626 10.9377 12.4455 10.6333C12.7319 10.3254 12.8752 9.93685 12.8752 9.46777V8.91992Z"></path></svg>'},84:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.976 10.072l4.357-4.357.62.618L8.284 11h-.618L3 6.333l.619-.618 4.357 4.357z"></path></svg>'},974:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.072 8.024L5.715 3.667l.618-.62L11 7.716v.618L6.333 13l-.618-.619 4.357-4.357z"></path></svg>'},143:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M10.012 2h.976v3.113l2.56-1.557.486.885L11.47 6l2.564 1.559-.485.885-2.561-1.557V10h-.976V6.887l-2.56 1.557-.486-.885L9.53 6 6.966 4.441l.485-.885 2.561 1.557V2zM2 10h4v4H2v-4z"></path></svg>'},161:e=>{e.exports='<svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><path d="M13.5 11h-1.729L8.438 6H9.5l.5-.5v-4L9.5 1h-4l-.5.5v4l.5.5h1.062l-3.333 5H1.5l-.5.5v3l.5.5h3l.5-.5v-3l-.5-.5h-.068L7.5 6.4l3.068 4.6H10.5l-.5.5v3l.5.5h3l.5-.5v-3l-.5-.5zM6 5V2h3v3H6zm-2 7v2H2v-2h2zm9 2h-2v-2h2v2z"></path></svg>'}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={id:r,exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0,(()=>{"use strict";var e,t,r,o,i,a,s,l,c={},d=[],u=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,_=Array.isArray;function p(e,t){for(var n in t)e[n]=t[n];return e}function f(e){var t=e.parentNode;t&&t.removeChild(e)}function h(t,n,r){var o,i,a,s={};for(a in n)"key"==a?o=n[a]:"ref"==a?i=n[a]:s[a]=n[a];if(arguments.length>2&&(s.children=arguments.length>3?e.call(arguments,2):r),"function"==typeof t&&null!=t.defaultProps)for(a in t.defaultProps)void 0===s[a]&&(s[a]=t.defaultProps[a]);return v(t,s,o,i,null)}function v(e,n,o,i,a){var s={type:e,props:n,key:o,ref:i,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==a?++r:a,__i:-1};return null==a&&null!=t.vnode&&t.vnode(s),s}function m(e){return e.children}function g(e,t){this.props=e,this.context=t}function y(e,t){if(null==t)return e.__?y(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?y(e):null}function b(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return b(e)}}function x(e){(!e.__d&&(e.__d=!0)&&o.push(e)&&!w.__r++||i!==t.debounceRendering)&&((i=t.debounceRendering)||a)(w)}function w(){var e,t,n,r,i,a,l,c,d;for(o.sort(s);e=o.shift();)e.__d&&(t=o.length,r=void 0,i=void 0,a=void 0,c=(l=(n=e).__v).__e,(d=n.__P)&&(r=[],i=[],(a=p({},l)).__v=l.__v+1,T(d,a,l,n.__n,void 0!==d.ownerSVGElement,null!=l.__h?[c]:null,r,null==c?y(l):c,l.__h,i),a.__.__k[a.__i]=a,I(r,a,i),a.__e!=c&&b(a)),o.length>t&&o.sort(s));w.__r=0}function k(e,t,n,r,o,i,a,s,l,u,p){var f,h,g,b,x,w,k,N,z,M=0,F=r&&r.__k||d,I=F.length,P=I,L=t.length;for(n.__k=[],f=0;f<L;f++)null!=(b=n.__k[f]=null==(b=t[f])||"boolean"==typeof b||"function"==typeof b?null:b.constructor==String||"number"==typeof b||"bigint"==typeof b?v(null,b,null,null,b):_(b)?v(m,{children:b},null,null,null):b.__b>0?v(b.type,b.props,b.key,b.ref?b.ref:null,b.__v):b)?(b.__=n,b.__b=n.__b+1,b.__i=f,-1===(N=A(b,F,k=f+M,P))?g=c:(g=F[N]||c,F[N]=void 0,P--),T(e,b,g,o,i,a,s,l,u,p),x=b.__e,(h=b.ref)&&g.ref!=h&&(g.ref&&E(g.ref,null,b),p.push(h,b.__c||x,b)),null==w&&null!=x&&(w=x),(z=g===c||null===g.__v)?-1==N&&M--:N!==k&&(N===k+1?M++:N>k?P>L-k?M+=N-k:M--:M=N<k&&N==k-1?N-k:0),k=f+M,"function"==typeof b.type?(N!==k||g.__k===b.__k?l=S(b,l,e):void 0!==b.__d?l=b.__d:x&&(l=x.nextSibling),b.__d=void 0):x&&(l=N!==k||z?C(e,x,l):x.nextSibling),"function"==typeof n.type&&(n.__d=l)):(g=F[f])&&null==g.key&&g.__e&&(g.__e==l&&(l=y(g),"function"==typeof n.type&&(n.__d=l)),H(g,g,!1),F[f]=null);for(n.__e=w,f=I;f--;)null!=F[f]&&("function"==typeof n.type&&null!=F[f].__e&&F[f].__e==l&&(n.__d=F[f].__e.nextSibling),H(F[f],F[f]))}function S(e,t,n){for(var r,o=e.__k,i=0;o&&i<o.length;i++)(r=o[i])&&(r.__=e,t="function"==typeof r.type?S(r,t,n):C(n,r.__e,t));return t}function C(e,t,n){return t!=n&&e.insertBefore(t,n||null),t.nextSibling}function A(e,t,n,r){var o=e.key,i=e.type,a=n-1,s=n+1,l=t[n];if(null===l||l&&o==l.key&&i===l.type)return n;if(r>(null!=l?1:0))for(;a>=0||s<t.length;){if(a>=0){if((l=t[a])&&o==l.key&&i===l.type)return a;a--}if(s<t.length){if((l=t[s])&&o==l.key&&i===l.type)return s;s++}}return-1}function N(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||u.test(t)?n:n+"px"}function z(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||N(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||N(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/,"$1")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.u=r.u:(n.u=Date.now(),e.addEventListener(t,i?F:M,i)):e.removeEventListener(t,i?F:M,i);else if("dangerouslySetInnerHTML"!==t){if(o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&"rowSpan"!==t&&"colSpan"!==t&&"role"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,n))}}function M(e){var n=this.l[e.type+!1];if(e.t){if(e.t<=n.u)return}else e.t=Date.now();return n(t.event?t.event(e):e)}function F(e){return this.l[e.type+!0](t.event?t.event(e):e)}function T(e,n,r,o,i,a,s,l,c,d){var u,f,h,v,y,b,x,w,S,C,A,N,z,M,F,T=n.type;if(void 0!==n.constructor)return null;null!=r.__h&&(c=r.__h,l=n.__e=r.__e,n.__h=null,a=[l]),(u=t.__b)&&u(n);e:if("function"==typeof T)try{if(w=n.props,S=(u=T.contextType)&&o[u.__c],C=u?S?S.props.value:u.__:o,r.__c?x=(f=n.__c=r.__c).__=f.__E:("prototype"in T&&T.prototype.render?n.__c=f=new T(w,C):(n.__c=f=new g(w,C),f.constructor=T,f.render=L),S&&S.sub(f),f.props=w,f.state||(f.state={}),f.context=C,f.__n=o,h=f.__d=!0,f.__h=[],f._sb=[]),null==f.__s&&(f.__s=f.state),null!=T.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=p({},f.__s)),p(f.__s,T.getDerivedStateFromProps(w,f.__s))),v=f.props,y=f.state,f.__v=n,h)null==T.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(null==T.getDerivedStateFromProps&&w!==v&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(w,C),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(w,f.__s,C)||n.__v===r.__v)){for(n.__v!==r.__v&&(f.props=w,f.state=f.__s,f.__d=!1),n.__e=r.__e,n.__k=r.__k,n.__k.forEach((function(e){e&&(e.__=n)})),A=0;A<f._sb.length;A++)f.__h.push(f._sb[A]);f._sb=[],f.__h.length&&s.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(w,f.__s,C),null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(v,y,b)}))}if(f.context=C,f.props=w,f.__P=e,f.__e=!1,N=t.__r,z=0,"prototype"in T&&T.prototype.render){for(f.state=f.__s,f.__d=!1,N&&N(n),u=f.render(f.props,f.state,f.context),M=0;M<f._sb.length;M++)f.__h.push(f._sb[M]);f._sb=[]}else do{f.__d=!1,N&&N(n),u=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++z<25);f.state=f.__s,null!=f.getChildContext&&(o=p(p({},o),f.getChildContext())),h||null==f.getSnapshotBeforeUpdate||(b=f.getSnapshotBeforeUpdate(v,y)),k(e,_(F=null!=u&&u.type===m&&null==u.key?u.props.children:u)?F:[F],n,r,o,i,a,s,l,c,d),f.base=n.__e,n.__h=null,f.__h.length&&s.push(f),x&&(f.__E=f.__=null)}catch(e){n.__v=null,c||null!=a?(n.__e=l,n.__h=!!c,a[a.indexOf(l)]=null):(n.__e=r.__e,n.__k=r.__k),t.__e(e,n,r)}else null==a&&n.__v===r.__v?(n.__k=r.__k,n.__e=r.__e):n.__e=P(r.__e,n,r,o,i,a,s,c,d);(u=t.diffed)&&u(n)}function I(e,n,r){n.__d=void 0;for(var o=0;o<r.length;o++)E(r[o],r[++o],r[++o]);t.__c&&t.__c(n,e),e.some((function(n){try{e=n.__h,n.__h=[],e.some((function(e){e.call(n)}))}catch(e){t.__e(e,n.__v)}}))}function P(t,n,r,o,i,a,s,l,d){var u,p,h,v=r.props,m=n.props,g=n.type,b=0;if("svg"===g&&(i=!0),null!=a)for(;b<a.length;b++)if((u=a[b])&&"setAttribute"in u==!!g&&(g?u.localName===g:3===u.nodeType)){t=u,a[b]=null;break}if(null==t){if(null===g)return document.createTextNode(m);t=i?document.createElementNS("http://www.w3.org/2000/svg",g):document.createElement(g,m.is&&m),a=null,l=!1}if(null===g)v===m||l&&t.data===m||(t.data=m);else{if(a=a&&e.call(t.childNodes),p=(v=r.props||c).dangerouslySetInnerHTML,h=m.dangerouslySetInnerHTML,!l){if(null!=a)for(v={},b=0;b<t.attributes.length;b++)v[t.attributes[b].name]=t.attributes[b].value;(h||p)&&(h&&(p&&h.__html==p.__html||h.__html===t.innerHTML)||(t.innerHTML=h&&h.__html||""))}if(function(e,t,n,r,o){var i;for(i in n)"children"===i||"key"===i||i in t||z(e,i,null,n[i],r);for(i in t)o&&"function"!=typeof t[i]||"children"===i||"key"===i||"value"===i||"checked"===i||n[i]===t[i]||z(e,i,t[i],n[i],r)}(t,m,v,i,l),h)n.__k=[];else if(k(t,_(b=n.props.children)?b:[b],n,r,o,i&&"foreignObject"!==g,a,s,a?a[0]:r.__k&&y(r,0),l,d),null!=a)for(b=a.length;b--;)null!=a[b]&&f(a[b]);l||("value"in m&&void 0!==(b=m.value)&&(b!==t.value||"progress"===g&&!b||"option"===g&&b!==v.value)&&z(t,"value",b,v.value,!1),"checked"in m&&void 0!==(b=m.checked)&&b!==t.checked&&z(t,"checked",b,v.checked,!1))}return t}function E(e,n,r){try{"function"==typeof e?e(n):e.current=n}catch(e){t.__e(e,r)}}function H(e,n,r){var o,i;if(t.unmount&&t.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||E(o,null,n)),null!=(o=e.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){t.__e(e,n)}o.base=o.__P=null,e.__c=void 0}if(o=e.__k)for(i=0;i<o.length;i++)o[i]&&H(o[i],n,r||"function"!=typeof e.type);r||null==e.__e||f(e.__e),e.__=e.__e=e.__d=void 0}function L(e,t,n){return this.constructor(e,n)}e=d.slice,t={__e:function(e,t,n,r){for(var o,i,a;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),a=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),a=o.__d),a)return o.__E=o}catch(t){e=t}throw e}},r=0,g.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=p({},this.state),"function"==typeof e&&(e=e(p({},n),this.props)),e&&p(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),x(this))},g.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),x(this))},g.prototype.render=m,o=[],a="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,s=function(e,t){return e.__v.__b-t.__v.__b},w.__r=0,l=0;var R,B,W,D,U=0,O=[],X=[],j=t.__b,Z=t.__r,G=t.diffed,q=t.__c,V=t.unmount;function $(e,n){t.__h&&t.__h(B,e,U||n),U=0;var r=B.__H||(B.__H={__:[],__h:[]});return e>=r.__.length&&r.__.push({__V:X}),r.__[e]}function Y(e){return U=1,function(e,t,n){var r=$(R++,2);if(r.t=e,!r.__c&&(r.__=[ce(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=B,!B.u)){var o=function(e,t,n){if(!r.__c.__H)return!0;var o=r.__c.__H.__.filter((function(e){return e.__c}));if(o.every((function(e){return!e.__N})))return!i||i.call(this,e,t,n);var a=!1;return o.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}})),!(!a&&r.__c.props===e)&&(!i||i.call(this,e,t,n))};B.u=!0;var i=B.shouldComponentUpdate,a=B.componentWillUpdate;B.componentWillUpdate=function(e,t,n){if(this.__e){var r=i;i=void 0,o(e,t,n),i=r}a&&a.call(this,e,t,n)},B.shouldComponentUpdate=o}return r.__N||r.__}(ce,e)}function K(e,n){var r=$(R++,3);!t.__s&&le(r.__H,n)&&(r.__=e,r.i=n,B.__H.__h.push(r))}function Q(e,n){var r=$(R++,4);!t.__s&&le(r.__H,n)&&(r.__=e,r.i=n,B.__h.push(r))}function J(e){return U=5,ee((function(){return{current:e}}),[])}function ee(e,t){var n=$(R++,7);return le(n.__H,t)?(n.__V=e(),n.i=t,n.__h=e,n.__V):n.__}function te(e,t){return U=8,ee((function(){return e}),t)}function ne(e){var t=B.context[e.__c],n=$(R++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(B)),t.props.value):e.__}function re(){for(var e;e=O.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(ae),e.__H.__h.forEach(se),e.__H.__h=[]}catch(n){e.__H.__h=[],t.__e(n,e.__v)}}t.__b=function(e){B=null,j&&j(e)},t.__r=function(e){Z&&Z(e),R=0;var t=(B=e.__c).__H;t&&(W===B?(t.__h=[],B.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.__V=X,e.__N=e.i=void 0}))):(t.__h.forEach(ae),t.__h.forEach(se),t.__h=[],R=0)),W=B},t.diffed=function(e){G&&G(e);var n=e.__c;n&&n.__H&&(n.__H.__h.length&&(1!==O.push(n)&&D===t.requestAnimationFrame||((D=t.requestAnimationFrame)||ie)(re)),n.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.__V!==X&&(e.__=e.__V),e.i=void 0,e.__V=X}))),W=B=null},t.__c=function(e,n){n.some((function(e){try{e.__h.forEach(ae),e.__h=e.__h.filter((function(e){return!e.__||se(e)}))}catch(r){n.some((function(e){e.__h&&(e.__h=[])})),n=[],t.__e(r,e.__v)}})),q&&q(e,n)},t.unmount=function(e){V&&V(e);var n,r=e.__c;r&&r.__H&&(r.__H.__.forEach((function(e){try{ae(e)}catch(e){n=e}})),r.__H=void 0,n&&t.__e(n,r.__v))};var oe="function"==typeof requestAnimationFrame;function ie(e){var t,n=function(){clearTimeout(r),oe&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);oe&&(t=requestAnimationFrame(n))}function ae(e){var t=B,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),B=t}function se(e){var t=B;e.__c=e.__(),B=t}function le(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function ce(e,t){return"function"==typeof t?t(e):t}var de=n(604),ue=n.n(de),_e=n(101),pe=n.n(_e),fe=n(863),he=n.n(fe),ve=n(124),me=n.n(ve),ge=n(896),ye=n.n(ge),be=n(917),xe=n.n(be),we=n(27),ke={};ke.styleTagTransform=xe(),ke.setAttributes=me(),ke.insert=he().bind(null,"head"),ke.domAPI=pe(),ke.insertStyleElement=ye(),ue()(we.A,ke);const Se=we.A&&we.A.locals?we.A.locals:void 0,Ce=()=>h("div",{className:Se.progress});var Ae,Ne=n(113),ze=n(143);!function(e){e[e.String=0]="String",e[e.Number=1]="Number"}(Ae||(Ae={}));const Me={[Ae.Number]:{":":e=>t=>t===Number(e),"=":e=>t=>t===Number(e),">":e=>t=>t>Number(e),"<":e=>t=>t<Number(e),"<=":e=>t=>t<=Number(e),">=":e=>t=>t>=Number(e),"<>":e=>t=>t!==Number(e),"!=":e=>t=>t!==Number(e)},[Ae.String]:{":":e=>t=>t===e,"=":e=>t=>t===e,"!=":e=>t=>t!==e,"<>":e=>t=>t!==e,"~=":e=>{const t=/^\/(.+)\/([a-z])*$/.exec(e),n=t?new RegExp(t[1],t[2]):new RegExp(e);return e=>(n.lastIndex=0,n.test(e))}}};class Fe extends Error{constructor(e,t){super(e),this.index=t}}const Te=new Set(Object.values(Me).map((e=>Object.keys(e))).reduce(((e,t)=>[...e,...t]),[]));class Ie{get eof(){return this.data?.length===this.length||Array.isArray(this._read)}get loaded(){return this.data||[]}static fromArray(e,t){return this.fromTopLevelArray(e,(e=>Ie.fromArray(t(e),t)))}static fromTopLevelArray(e,t){const n=new Ie(e.length,(()=>Promise.resolve(e)),t);return n.data=e,n}static fromProvider(e,t,n){return Ie.fromProvider(e,t,n)}constructor(e,t,n){this.length=e,this._getChildren=n,this.asyncLoads=[],this.children=new Map,Array.isArray(t)||t instanceof Array?this.data=t:this._read=t}setSort(e){e!==this.sortFn&&(this.sortFn=e,this.eof?this.data&&this.data.sort(e):(this.data=void 0,this.asyncLoads=[]))}getChildren(e){let t=this.children.get(e);return t||(t=this._getChildren(e),this.children.set(e,t)),t.setSort(this.sortFn),t}didReadUpTo(e){if(this.eof||!this._read)return!0;const t=this.asyncLoads[this.asyncLoads.length-1];return!!(t&&t.upTo>=e)}async read(e){if(!this._read)return Promise.resolve(this.loaded);const t=this.asyncLoads[this.asyncLoads.length-1]||{upTo:0,p:Promise.resolve()};if(t.upTo>=e)return t.p;const n=t.p.then((async()=>{const n=await this._read(t.upTo,e,this.sortFn);return this.data?.length?this.data=this.data.concat(n):this.data=n,this.data}));return this.asyncLoads.push({upTo:e,p:n}),n}}const Pe=(e,t,n,r)=>{let o=!1;t(n)&&(r.selected.add(n),r.selectedAndParents.add(n),o=!0);const i=e.getChildren(n);for(const e of i.loaded)Pe(i,t,e,r)&&(r.selectedAndParents.add(n),o=!0);return o};var Ee=n(273),He={};He.styleTagTransform=xe(),He.setAttributes=me(),He.insert=he().bind(null,"head"),He.domAPI=pe(),He.insertStyleElement=ye(),ue()(Ee.A,He);const Le=Ee.A&&Ee.A.locals?Ee.A.locals:void 0,Re=(...e)=>e.filter(Boolean).join(" "),Be=({value:e,hasError:t,min:n,type:r,onChange:o,placeholder:i="Filter for function",foot:a})=>{const s=te((e=>{o(e.target.value)}),[o]);return h("div",{className:Le.wrapper},h("input",{className:Re(t&&Le.error),type:r,min:n,value:e,placeholder:i,onPaste:s,onKeyUp:s}),a)};var We=n(838),De={};De.styleTagTransform=xe(),De.setAttributes=me(),De.insert=he().bind(null,"head"),De.domAPI=pe(),De.insertStyleElement=ye(),ue()(We.A,De);const Ue=We.A&&We.A.locals?We.A.locals:void 0,Oe=({children:e})=>h("div",{className:Ue.f},e);var Xe=n(646),je={};je.styleTagTransform=xe(),je.setAttributes=me(),je.insert=he().bind(null,"head"),je.domAPI=pe(),je.insertStyleElement=ye(),ue()(Xe.A,je);const Ze=Xe.A&&Xe.A.locals?Xe.A.locals:void 0;var Ge=n(94),qe={};qe.styleTagTransform=xe(),qe.setAttributes=me(),qe.insert=he().bind(null,"head"),qe.domAPI=pe(),qe.insertStyleElement=ye(),ue()(Ge.A,qe);const Ve=Ge.A&&Ge.A.locals?Ge.A.locals:void 0,$e=({icon:e,label:t,checked:n,onChange:r,onClick:o})=>{const i=te((()=>{o?.(),r?.(!n)}),[n,o,r]);return h("button",{className:Ve.button,type:"button",role:"switch",alt:t,title:t,"aria-label":t,"aria-checked":n?"true":"false",dangerouslySetInnerHTML:{__html:e},onClick:i})},Ye=function(e,t){var n={__c:t="__cC"+l++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some((function(e){e.__e=!0,x(e)}))},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}(acquireVsCodeApi()),Ke=(e,t)=>{const n=ne(Ye),[r,o]=Y(n.getState()?.[e]??t);return((t,o)=>{const i=J(!0);K((()=>{i.current?i.current=!1:n.setState({...n.getState(),[e]:r})}),o)})(0,[r]),[r,o]},Qe=()=>({placeholder:e,data:t,onChange:n,foot:r})=>{const[o,i]=Y(!1),[a,s]=Y(!1),[l,c]=Ke("filterText",""),[d,u]=Y(void 0);return K((()=>{try{n((e=>{const t=((e,t,n=Me)=>{const r=[],o=[];for(let i=0;i<e.length;i++){const a=e[i];switch(a.token){case 1:const s=t.datasource.properties[a.text];if(!s){const e=Object.keys(t.datasource.properties).join(", ");throw new Fe(`Unknown column @${a.text}, have: ${e}`,a.start)}const l=e[++i];if(2!==l?.token)throw new Fe(`Missing operator for column @${a.text}`,a.start);if(!n[s.type][l.text])throw new Fe(`Unknown operator for @${a.text}, have: ${Object.keys(n[s.type]).join(", ")}`,l.start);const c=e[++i];if(3!==c?.token)throw new Fe(`Missing operand for column @${c.text}`,a.start);const d=n[s.type][l.text](c.text);r.push((e=>d(s.accessor(e))));break;case 0:o.push(a.text.trim());break;default:throw new Error(`Illegal token ${a.token}`)}}const i=o.join(" ").trim();if(i){const e=`/${t.regex?i:(a=i,a.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&"))}/`+(t.caseSensitive?"":"i"),o=n[Ae.String]["~="](e);r.push((e=>o(t.datasource.genericMatchStr(e))))}var a;return e=>{for(const t of r)if(!t(e))return!1;return!0}})((e=>{const t=[];let n=0;const r=(t,r)=>{let o="";const i=n;for(;n<e.length;){const t=e[n];if("\\"!==t){if(!r(t,n))break;o+=t,n++}else o+=e[++n],n++}return{token:t,text:o,start:i,length:n-i}};let o=0;for("@"===e[0]&&(o=1,n++);n<e.length;){const i=e[n];switch(o){case 0:const a=e.indexOf(" @",n);-1===a?t.push(r(0,(()=>!0))):(t.push(r(0,((e,t)=>t<=a))),n++,o=1);break;case 1:t.push(r(1,(e=>e>="A"&&e<="z"))),o=2;break;case 2:t.push(r(2,(e=>Te.has(e)))),o=3;break;case 3:const s='"'!==i&&"'"!==i;s||n++,t.push(r(3,(e=>s?" "!==e:e!==i))),o=0,s||n++;break;default:throw new Error(`Illegal state ${o}`)}}return t})(e.input),e),n={selected:new Set,selectedAndParents:new Set,all:!e.input.trim()};for(const r of e.datasource.data.loaded)Pe(e.datasource.data,t,r,n);return n})({input:l,regex:o,caseSensitive:a,datasource:t})),u(void 0)}catch(e){u(e.message)}}),[o,a,l,t]),h(Oe,null,h(Be,{value:l,placeholder:e,onChange:c,hasError:!!d,foot:h(m,null,h($e,{icon:Ne,label:"Match Case",checked:a,onChange:s}),h($e,{icon:ze,label:"Use Regular Expression",checked:o,onChange:i}))}),d&&h("div",{className:Ze.error},d),r)};var Je=n(71),et={};et.styleTagTransform=xe(),et.setAttributes=me(),et.insert=he().bind(null,"head"),et.domAPI=pe(),et.insertStyleElement=ye(),ue()(Je.A,et);const tt=Je.A&&Je.A.locals?Je.A.locals:void 0;let nt=Math.floor(2147483647*Math.random());var rt=n(13),ot={};ot.styleTagTransform=xe(),ot.setAttributes=me(),ot.insert=he().bind(null,"head"),ot.domAPI=pe(),ot.insertStyleElement=ye(),ue()(rt.A,ot);const it=rt.A&&rt.A.locals?rt.A.locals:void 0;var at=n(84),st=n(161);const lt=["B","kB","MB","GB","TB","PB","EB","ZB","YB"],ct=["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"],dt=["b","kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],ut=["b","kibit","Mibit","Gibit","Tibit","Pibit","Eibit","Zibit","Yibit"],_t=(e,t,n)=>{let r=e;return"string"==typeof t||Array.isArray(t)?r=e.toLocaleString(t,n):!0!==t&&void 0===n||(r=e.toLocaleString(void 0,n)),r};function pt(e,t){if(!Number.isFinite(e))throw new TypeError(`Expected a finite number, got ${typeof e}: ${e}`);const n=(t={bits:!1,binary:!1,space:!0,...t}).bits?t.binary?ut:dt:t.binary?ct:lt,r=t.space?" ":"";if(t.signed&&0===e)return` 0${r}${n[0]}`;const o=e<0,i=o?"-":t.signed?"+":"";let a;if(o&&(e=-e),void 0!==t.minimumFractionDigits&&(a={minimumFractionDigits:t.minimumFractionDigits}),void 0!==t.maximumFractionDigits&&(a={maximumFractionDigits:t.maximumFractionDigits,...a}),e<1)return i+_t(e,t.locale,a)+r+n[0];const s=Math.min(Math.floor(t.binary?Math.log(e)/Math.log(1024):Math.log10(e)/3),n.length-1);return e/=(t.binary?1024:1e3)**s,a||(e=e.toPrecision(3)),i+_t(Number(e),t.locale,a)+r+n[s]}const ft=({i:e,...t})=>h("span",{dangerouslySetInnerHTML:{__html:e},style:{color:"var(--vscode-icon-foreground)"},...t});Symbol("unset");var ht=n(925),vt={};vt.styleTagTransform=xe(),vt.setAttributes=me(),vt.insert=he().bind(null,"head"),vt.domAPI=pe(),vt.insertStyleElement=ye(),ue()(ht.A,vt);const mt=ht.A&&ht.A.locals?ht.A.locals:void 0,gt=({row:e,renderRow:t,style:n})=>h("div",{style:n},ee((()=>t(e)),[e])),yt=(e,t)=>`position:absolute;left:0;right:0;height:${t}px;top:${e*t}px`,bt=(e,t,n)=>{n((n=>{if(n.get(e)===t){const t=new Map(n);return t.delete(e),t}return n}))},xt=({depth:e,position:t,node:n,dataProvider:r,promise:o,onLoadMore:i})=>{const[a,s]=Y(!!o);return r.eof?null:(K((()=>{o?o.finally((()=>s(!1))):s(!1)}),[o]),h("div",{className:mt.row,"data-row-id":`loading-${t}`,tabIndex:0,role:"treeitem","aria-posinset":t,"aria-level":e+1},h("div",{className:mt.footer,style:{paddingLeft:15*e}},a?"Loading...":h(m,null,h("a",{role:"button",onClick:()=>i(n,r)},"Load more rows")))))};var wt=n(974);const kt=e=>{const t=[e.id];for(let n=e.parent;n;n=n.parent)t.push(n.id);return t.join("-")},St=({impact:e})=>h("div",{className:mt.impactBar,style:{transform:`scaleX(${e})`}}),Ct=(()=>{const e=({containerRef:e=J(null),data:t,className:n,renderRow:r,rowHeight:o,overscanCount:i})=>{const[a,s]=Y([]),l=t.length*o,c=te((()=>{const{current:n}=e;if(!n)return;const r=n.scrollTop,a=Math.max(0,Math.floor(r/o)-i),l=Math.min(t.length-1,a+Math.ceil(n.clientHeight/o)+2*i);s(function(e,t){const n=[];for(let r=e;r<t;r++)n.push(r);return n}(a,l+1))}),[t,o,i]);return((e,t,n)=>{K((()=>{if(!t)return;const r=new ResizeObserver((t=>{for(const n of t)e(n)}));return r.observe(t,n),()=>r.disconnect()}),[e,t,n])})(c,e.current),Q((()=>c()),[c]),h("div",{ref:e,className:n,style:{height:"100%",overflow:"auto"},onScroll:c},h("div",{style:{height:l,position:"relative"}},a.map((e=>h(gt,{renderRow:r,row:t[e],style:yt(e,o),key:e})))))};return({data:t,header:n,query:r,sortFn:o,row:i})=>{const a=J(new Map),[s,l]=Y(new Map),c=J(null),[d,u]=Y(void 0),[_,p]=Y(new Set),f=ee((()=>{const e=o?t.loaded.slice().sort(o):t.loaded;for(const n of e)t.setSort(o),a.current.set(n,t);return e}),[t,o]),v=ee((()=>{const e=f.filter((e=>r.selectedAndParents.has(e))).map((e=>({node:e,position:1,depth:0,provider:t})));for(let t=0;t<e.length;t++){const{node:n,depth:o,isFooter:i,entireSubtree:s}=e[t];if(!i&&_.has(n)){const i=a.current.get(n)?.getChildren(n);if(i){for(const e of i.loaded)a.current.set(e,i);const l=[];for(const e of i.loaded)(r.all||r.selectedAndParents.has(e)||s)&&l.push({node:e,position:t+1,depth:o+1,provider:i,entireSubtree:s||r.selected.has(e)});r.all&&l.push({isFooter:!0,node:n,position:t+l.length,depth:o+1,provider:i}),e.splice(t+1,0,...l)}}}return e}),[f,_,o,r,s]),g=te(((e,t)=>{const n=a.current.get(t);let r;switch(e.key){case"Enter":case"Space":p(((e,t)=>{const n=new Set([...e]);return n.has(t)?n.delete(t):n.add(t),n})(_,t)),e.preventDefault();break;case"ArrowDown":r=v[v.findIndex((e=>e.node===t))+1]?.node;break;case"ArrowUp":r=v[v.findIndex((e=>e.node===t))-1]?.node;break;case"ArrowLeft":_.has(t)?p(((e,t)=>{const n=new Set([...e]);return n.delete(t),n})(_,t)):r=t.parent;break;case"ArrowRight":{const e=n?.getChildren(t);e?.length&&!_.has(t)?p(((e,t)=>{const n=new Set([...e,t]);return n.add(t),n})(_,t)):r=v.find((e=>e.node?.parent===t))?.node;break}case"Home":c.current&&(c.current.scrollTop=0),r=v[0]?.node;break;case"End":c.current&&(c.current.scrollTop=c.current.scrollHeight),r=v[v.length-1]?.node;break;case"*":{const e=new Set(_);if(d&&d.parent){const t=d?.parent,n=t&&a.current.get(t);for(const t of n?.getChildren(d).loaded||[])e.add(t);p(e)}break}}r&&(u(r),e.preventDefault())}),[v,_]);K((()=>{l((e=>{let t;for(const n of _){const r=a.current.get(n)?.getChildren(n);if(r&&!r.didReadUpTo(100)){t??=new Map(e);const o=r.read(100).then((()=>bt(n,o,l)));t.set(n,o)}}return t||e}))}),[_,o]),K((()=>c.current?.setAttribute("role","tree")),[c.current]),Q((()=>{const e=c.current;e&&d&&setTimeout((()=>{const t=e.querySelector(`[data-row-id="${(e=>{const t=[e.id];for(let n=e.parent;n;n=n.parent)t.push(n.id);return t.join("-")})(d)}"]`);t?.focus()}))}),[d]);const y=(e,t)=>{p((n=>{const r=new Set(n);return e?r.add(t):r.delete(t),r.size!==n.size?r:n}))},b=te(((e,t)=>{const n=t,r=e;l((e=>{const t=new Map(e),o=n.read(n.loaded.length+100).then((()=>bt(r,o,l)));return t.set(r,o),t}))}),[]),x=te((e=>e.isFooter?h(xt,{node:e.node,depth:e.depth,position:e.position,promise:s.get(e.node),dataProvider:e.provider,onLoadMore:b}):h(i,{onKeyDown:g,node:e.node,depth:e.depth,position:e.position,numChildren:e.provider.getChildren(e.node).length,expanded:_.has(e.node),onExpanded:y,onFocus:u})),[_,p,g]);return h(m,null,n,h(e,{containerRef:c,className:mt.rows,data:v,renderRow:x,rowHeight:25,overscanCount:30}))}})(),At=(e,t)=>t.selfSize-e.selfSize,Nt=(e,t)=>t.retainedSize-e.retainedSize,zt=(e,t)=>e.name.localeCompare(t.name),Mt=({query:e,data:t})=>{const[n,r]=Y(void 0);return h(Ct,{data:t,sortFn:n||Nt,query:e,header:h(Ft,{sort:n,onChangeSort:r}),row:ee((()=>It(t)),[t])})},Ft=({sort:e,onChangeSort:t})=>h("div",{className:mt.row},h("div",{id:"self-size-header",className:Re(mt.heading,mt.timing),"aria-sort":e===At?"descending":void 0,onClick:te((()=>t((()=>e===At?void 0:At))),[e])},e===At&&h(ft,{i:at}),"Self Size"),h("div",{id:"retained-size-header",className:Re(mt.heading,mt.timing),"aria-sort":e===Nt?"descending":void 0,onClick:te((()=>t((()=>e===Nt?void 0:Nt))),[e])},e===Nt&&h(ft,{i:at}),"Retained Size")),Tt=({node:e,depth:t,numChildren:n,expanded:r,position:o,onKeyDown:i,onFocus:a,onClick:s,onExpanded:l,children:c,rowText:d,locationText:u,virtual:_=!u})=>{const p=te((()=>l(!r,e)),[r,e]),f=te((t=>{i?.(t,e)}),[i,e]),v=te((()=>{a?.(e)}),[a,e]);let m=e;for(;m.parent;)m=m.parent;const g=h("span",{className:mt.expander},n>0?h(ft,{i:r?at:wt}):null);return h("div",{className:mt.row,style:{cursor:n>0?"pointer":"default"},"data-row-id":kt(e),onKeyDown:f,onFocus:v,onClick:p,tabIndex:0,role:"treeitem","aria-posinset":o,"aria-level":t+1,"aria-expanded":r},c,u?h("div",{className:mt.location,style:{marginLeft:15*t}},g," ",h("span",{className:mt.fn,style:{maxWidth:"80%"}},d),h("span",{className:mt.file},h("a",{href:"#",onClick:s},u))):h("div",{className:Re(mt.location,_&&mt.virtual),style:{marginLeft:15*t}},g," ",h("span",{className:mt.fn},d)))},It=e=>t=>{const{node:n}=t,r=n.parent?void 0:e.loaded.reduce(((e,t)=>(e.selfSize+=t.selfSize,e.retainedSize+=t.retainedSize,e)),{selfSize:0,retainedSize:0}),o=ne(Ye),i=te((e=>{e.stopPropagation(),o.postMessage({type:"command",command:"jsProfileVisualizer.heapsnapshot.flame.show",args:[{uri:DOCUMENT_URI,index:n.index,name:n.name}],requireExtension:"ms-vscode.vscode-js-profile-flame"})}),[o,n.index]),a=r&&{self:n.selfSize/r.selfSize,ret:n.retainedSize/r.retainedSize};return h(Tt,{...t,virtual:!1,onClick:i,rowText:h(m,null,n.parent&&h("a",{role:"button",alt:"View Retainer Graph",title:"View Retainer Graph",onClick:i},h(ft,{i:st,style:{display:"inline-block",width:"1em"}}))," ",n.name,h("span",{style:{opacity:.5}},n.parent?` @${n.id}`:""))},h("div",{className:mt.duration,"aria-labelledby":"self-size-header",title:a&&`${n.selfSize} bytes, ${(100*a.self).toFixed(1)}% of total`},a&&h(St,{impact:a.self}),pt(n.selfSize)),h("div",{className:mt.duration,"aria-labelledby":"retained-size-header",title:a&&`${n.retainedSize} bytes, ${(100*a.ret).toFixed(1)}% of total`},a&&h(St,{impact:a.ret}),pt(n.retainedSize)))},Pt=({query:e,data:t})=>h(Mt,{query:e,data:t}),Et=({data:e,body:t,filterFooter:n})=>{const r=ee(Qe,[]),[o,i]=Y(void 0),a=ee((()=>n?h(n,{viewType:"jsProfileVisualizer.cpuprofile.flame",requireExtension:"ms-vscode.vscode-js-profile-flame"}):void 0),[n]);return h(m,null,h("div",{className:tt.filter},h(r,{data:e,onChange:i,placeholder:"Filter functions or files, or start a query()",foot:a})),h("div",{className:tt.rows},o&&h(t,{query:o,data:e.data})))},Ht=e=>e===At?0:e===zt?2:1,Lt=(e,t)=>new Ie(e.childrenLen,((n,r,o)=>("type"in e?t.getNodeChildren(e.index,n,r,Ht(o)):t.getClassChildren(e.index,n,r,Ht(o))).then((t=>{for(const n of t)n.parent=e;return t}))),(e=>Lt(e,t))),Rt=document.createElement("div");Rt.classList.add(it.wrapper),document.body.appendChild(Rt),function(n,r,o){var i,a,s,l;t.__&&t.__(n,r),a=(i="function"==typeof o)?null:o&&o.__k||r.__k,s=[],l=[],T(r,n=(!i&&o||r).__k=h(m,null,[n]),a||c,c,void 0!==r.ownerSVGElement,!i&&o?[o]:a?null:r.firstChild?e.call(r.childNodes):null,s,!i&&o?o:a?a.__e:r.firstChild,i,l),I(s,n,l)}(h((()=>{const e=(()=>{const e=ne(Ye);return ee((()=>new Proxy({},{get:(t,n)=>(...t)=>((e,t,n)=>{const r=nt++;return e.postMessage({type:"callGraph",inner:{method:t,args:n,id:r}}),new Promise(((e,t)=>{const n=o=>{if("graphRet"===o.data?.method&&o.data.message.id===r){window.removeEventListener("message",n);const r=o.data.message.result;"ok"in r?e(r.ok):t(new Error(r.err))}};window.addEventListener("message",n)}))})(e,n,t)})),[])})(),[t,n]=Y(void 0);return K((()=>{e.getClassGroups(0,1e4).then((e=>e.map((e=>({...e,id:e.index}))))).then(n,n)}),[]),void 0===t?h(Ce,null):t instanceof Error?h("div",null,String(t)):h(Et,{data:{data:Ie.fromTopLevelArray(t,(t=>Lt(t,e))),genericMatchStr:e=>`${e.name} ${e.id}`,properties:{object:{type:Ae.String,accessor:e=>e.name},selfSize:{type:Ae.Number,accessor:e=>e.selfSize},retainedSize:{type:Ae.Number,accessor:e=>e.retainedSize},id:{type:Ae.Number,accessor:e=>e.id}}},body:Pt})}),null),Rt)})()})();